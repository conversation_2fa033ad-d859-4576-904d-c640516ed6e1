<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <th:block th:include="include :: header('交易流水')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <!-- 搜索区域 -->
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>收支类别：</label>
                                <select name="direction" title="交易类型" onchange="$.table.search()">
                                    <option value="">所有</option>
                                    <option value="in">入账</option>
                                    <option value="out">出账</option>
                                </select>
                            </li>  
                            <li>
                                <label>交易时间：</label>
                                <input type="text" class="time-input" name="tran_date_start" th:value="${beginDate}" placeholder="请输入交易时间" title="交易时间"/>
                                <span class="text-muted">至</span>
                                <input type="text" class="time-input" name="tran_date_end" th:value="${endDate}" placeholder="请输入交易时间" title="交易时间"/>
                            </li>
                            <li>
                                <label>对方账号：</label>
                                <input type="text" name="acct_no" placeholder="请输入对方账号" title="对方账号"/>
                            </li>        
                            <li>
                                <label>流水号：</label>
                                <input type="text" name="tran_flow_no" placeholder="请输入流水号" title="流水号"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" title="状态" onchange="$.table.search()">
                                    <option value="">所有</option>
                                    <option value="draft">待公示</option>
                                    <option value="published">已公示</option>
                                    <option value="archived">已归档</option>
                                </select>
                            </li>
                            <li>
                                <label>账目类别：</label>
                                <select name="account_type_id" id="searchAccountTypeSelect" title="账目类别" onchange="$.table.search()">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()" title="搜索">
                                    <i class="fa fa-search"></i>&nbsp;搜索
                                </a>
                                <a class="btn btn-warning btn-rounded btn-sm ml-5" onclick="$.form.reset()" title="重置">
                                    <i class="fa fa-undo"></i>&nbsp;重置
                                </a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <!-- 工具栏 -->
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="syncData()" title="立即同步">
                    <i class="fa fa-sync"></i> 立即同步
                </a>
                <a class="btn btn-primary ml-5" onclick="batchEditStatus('published')" title="批量公示">
                    <i class="fa fa-bullhorn"></i> 批量公示
                </a>
                <a class="btn btn-danger ml-5" onclick="batchEditStatus('archived')" title="批量归档">
                    <i class="fa fa-archive"></i> 批量归档
                </a>
                <a class="btn btn-info ml-5" onclick="showBatchAccountTypeModal()" title="批量修改分类">
                    <i class="fa fa-tags"></i> 修改账目分类
                </a>
            </div>

            <!-- 表格 -->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/ccbTran";
        
        $(function() {
            var tranDateStart = $("[name='tran_date_start']").val();
            if(tranDateStart==''){
                $.post(prefix + "/initDate", {}, function(result){
                    $("[name='tran_date_start']").val(result.data);
                })
            }

            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "收费标准",
                pageSize:30,
                pageList: [10, 20, 30, 50, 100],
                uniqueId:'list',
                columns: [{
                    checkbox: true
                },
                {
                    field: 'tran_flow_no',
                    title: '流水号'
                },
                {
                    field: 'direction',
                    title: '收支类别',
                    formatter: function(value) {
                        if(value=='in'){
                            return '<span class="label label-success">入账</span>';
                        }else if(value=='out'){
                            return '<span class="label label-danger">出账</span>';
                        } else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'amt',
                    title: '发生额'
                },
                {
                    field: 'amt_all',
                    title: '余额'
                },
                {
                    field: 'tran_datetime',
                    title: '交易时间'
                },
                {
                    field: 'message',
                    title: '摘要'
                },
                {
                    field: 'acct_no',
                    title: '对方账号'
                },
                {
                    field: 'acct_name',
                    title: '对方户名'
                },
                {
                    field: 'account_type_name',
                    title: '账目分类'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value) {
                        if(value=='draft'){
                            return '<span class="label label-default">待公示</span>';
                        }else if(value=='published'){
                            return '<span class="label label-info">已公示</span>';
                        }else if(value=='archived'){
                            return '<span class="label label-success">已归档</span>';
                        } else {
                            return value || '';
                        }
                    }
                },
               
                {
                    field: 'remark',
                    title: '备注',
                    formatter: function(value) {
                        if (!value) return '';
                        var shortVal = value.length > 20 ? value.substring(0, 20) + '...' : value;
                        return '<span title="' + value.replace(/"/g, '&quot;') + '">' + shortVal + '</span>';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '150px',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.status == 'draft'){
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="editActive(\'' + row.trck_no + '\', \'published\')" title="公示"><i class="fa fa-edit"></i>公示</a> ');
                        }
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="showRemarkModal(\'' + row.trck_no + '\', \'' + row.remark + '\')" title="备注"><i class="fa fa-edit"></i>备注</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function syncData(){
            if(!confirm("确定要同步数据吗？")){
                return;
            }
            var data =  {tran_date_start: $("[name='tran_date_start']").val(), tran_date_end: $("[name='tran_date_end']").val()};
            $.modal.loading("正在同步数据，请稍后...");
            $.operate.save(prefix + "/syncData", data,function(result){
                $.modal.closeLoading();
                if(result.code==0){
                    $.modal.alertSuccess(result.msg);
                    $.table.refresh();
                }else{
                    $.modal.alertError(result.msg);
                }
            },function(){
                $.modal.closeLoading();
                $.modal.alertError("数据同步失败");
            });
        }

        function editActive(trck_no, status) {
            $.operate.save(prefix + "/editActive", {trck_no: trck_no, status: status},function(){
                
            });
        }

        function batchEditStatus(status) {
            var _status  = $("[name='status']").val();
            if(_status!='draft'){
                $("[name='status']").val('draft');
                batchEditStatus(status);
            }
            var trckNos = $.table.selectColumns("trck_no");
            if(trckNos.length==0){
                $.modal.alertWarning("请选择要操作的数据");
                return;
            }
            if(!confirm("确定要" + (status == 'published' ? '公示' : '归档') + "吗？")){
                return;
            }
            $.operate.save(prefix + "/batchEditStatus", {trckNos:trckNos.join(','),status: status},function(){
                $.modal.alertSuccess("操作成功");
                $.table.refresh();
            });
        }

        function showRemarkModal(trck_no, remark) {
            $('#remarkTrckNo').val(trck_no);
            $('#remarkTextarea').val(remark || '');
            $('#remarkModal').modal('show');
            setTimeout(function(){ $('#remarkTextarea').focus(); }, 300);
        }

        function saveRemark() {
            var trck_no = $('#remarkTrckNo').val();
            var remark = $('#remarkTextarea').val();
            var $btn = $('#remarkModal .btn-primary');
            $btn.prop('disabled', true);
            $.operate.save(prefix + "/editRemark", {trck_no: trck_no, remark: remark}, function(){
                $('#remarkModal').modal('hide');
                $('#remarkTextarea').val('');
                $btn.prop('disabled', false);
                $.table.refresh();
            }, function(){
                $btn.prop('disabled', false);
            });
        }

        function showBatchAccountTypeModal() {
            var ids = $.table.selectColumns("trck_no");
            if (ids.length == 0) {
                $.modal.alertWarning("请选择要操作的数据");
                return;
            }
            // 加载账目分类
            $.ajax({
                url: ctx + "oc/accountType/activeList",
                type: "POST",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var $select = $("#batchAccountTypeSelect");
                        $select.empty();
                        $select.append('<option value="">请选择账目分类</option>');
                        $.each(res.data, function(i, item) {
                            $select.append('<option value="' + item.id + '" data-name="' + item.name + '">' + item.name + '</option>');
                        });
                        $("#batchAccountTypeModal").modal("show");
                    } else {
                        $.modal.alertError("加载账目分类失败");
                    }
                }
            });
        }

        function submitBatchAccountType() {
            var ids = $.table.selectColumns("trck_no");
            var $option = $("#batchAccountTypeSelect option:selected");
            var accountTypeId = $option.val();
            var accountTypeName = $option.data("name");
            if (!accountTypeId) {
                $.modal.alertWarning("请选择账目分类");
                return;
            }
            $.operate.save(prefix + "/batchUpdateAccountType", {
                trckNos: ids.join(","),
                account_type_id: accountTypeId,
                account_type_name: accountTypeName
            }, function() {
                $("#batchAccountTypeModal").modal("hide");
                $.table.refresh();
            });
        }

        $(function(){
            $('input[name="acct_no"]').attr({placeholder:'请输入对方账号',title:'对方账号'});
            $('select[name="direction"]').attr({title:'交易类型'});
            $('select[name="status"]').attr({title:'状态'});
            // 动态加载账目类别
            $.ajax({
                url: ctx + "oc/accountType/activeList",
                type: "POST",
                dataType: "json",
                success: function(res) {
                    if (res.code == 0) {
                        var $select = $("#searchAccountTypeSelect");
                        $select.empty();
                        $select.append('<option value="">所有</option>');
                        $.each(res.data, function(i, item) {
                            $select.append('<option value="' + item.id + '">' + item.name + '</option>');
                        });
                    }
                }
            });
        });
    </script>
    <!-- 备注编辑模态框 -->
    <div class="modal fade" id="remarkModal" tabindex="-1" role="dialog" aria-labelledby="remarkModalLabel">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title" id="remarkModalLabel">编辑备注</h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          </div>
          <div class="modal-body">
            <textarea id="remarkTextarea" class="form-control" rows="6" placeholder="请输入备注内容（长度不限）"></textarea>
            <input type="hidden" id="remarkTrckNo" />
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="saveRemark()">保存</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 批量修改账单分类模态框 -->
    <div class="modal fade" id="batchAccountTypeModal" tabindex="-1" role="dialog" aria-labelledby="batchAccountTypeModalLabel">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title" id="batchAccountTypeModalLabel">批量修改账单分类</h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
          </div>
          <div class="modal-body">
            <select id="batchAccountTypeSelect" class="form-control" title="账目分类">
              <option value="">请选择账目分类</option>
            </select>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="submitBatchAccountType()">确定</button>
          </div>
        </div>
      </div>
    </div>
</body>
</html> 