const app = getApp();
Page({
  data: {
    list: [],
    loading: true
  },
  onLoad() {
    this.getHistory();
  },
  async getHistory() {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/bx/history',
        method: 'POST'
      });
      if (res.code === 0) {
        const list = (res.data || []).map(item => ({
          ...item,
          mediaUrls: item.media_urls ? JSON.parse(item.media_urls) : []
        }));
        this.setData({ list, loading: false });
      } else {
        this.setData({ list: [], loading: false });
      }
    } catch (e) {
      this.setData({ list: [], loading: false });
    }
  },
  onPullDownRefresh() {
    this.getHistory().then(() => wx.stopPullDownRefresh());
  }
}); 