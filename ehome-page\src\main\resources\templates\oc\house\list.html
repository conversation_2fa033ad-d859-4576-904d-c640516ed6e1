<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('房屋信息列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
 
     
</head>
<body class="gray-bg">   
    <div class="ui-layout-west">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa fa-sitemap"></i> 房屋管理
				</div>
				<div class="box-tools pull-right">
				    <a type="button" class="btn btn-box-tool" href="javascript:void(0)" onclick="buildingMgr()" title="管理楼栋"><i class="fa fa-edit"></i></a>
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="buildingTree" class="ztree"></div>
			</div>
		</div>
	</div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <!-- 搜索区域 -->
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" name="buildingId" id="buildingId"/>
                        <input type="hidden" name="unitId" id="unitId"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>房间号：</label>
                                    <input type="text" name="room"/>
                                </li>                               
                                <li>
                                    <label>楼栋号：</label>
                                    <input type="text" name="buildingName" placeholder="请选择左侧楼栋单元" readonly/>
                                </li>
                                <li style="display: none;">
                                    <label>单元号：</label>
                                    <input type="text" name="unitName" readonly/>
                                </li>
                                <li>
                                    <label>房屋状态：</label>
                                    <select name="house_status">
                                        <option value="">所有</option>
                                        <option value="2002">未销售</option>
                                        <option value="2001">已入住</option>
                                        <option value="2003">已交房</option>
                                        <option value="2005">已装修</option>
                                        <option value="2004">未入住</option>
                                        <option value="2009">装修中</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- 工具栏和表格区域 -->
                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-success" onclick="$.operate.add()">
                        <i class="fa fa-plus"></i> 新增房屋
                    </a>
                    <a class="btn btn-warning ml-10" onclick="importData()">
                        <i class="fa fa-file-excel-o"></i> 批量导入
                    </a>
                    <a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">

        var prefix = ctx + "oc/house";
        var statusDatas = [
            { dictValue: "2002", dictLabel: "未销售" },
            { dictValue: "2001", dictLabel: "已入住" },
            { dictValue: "2003", dictLabel: "已交房" },
            { dictValue: "2005", dictLabel: "已装修" },
            { dictValue: "2004", dictLabel: "未入住" },
            { dictValue: "2009", dictLabel: "装修中" }
        ];

        var houseTypeDatas = [
            { dictValue: "1", dictLabel: "住宅" },
            { dictValue: "2", dictLabel: "商铺" }
        ];

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 220, resizeWithWindow: false });
        
            loadBuildingZTree();
            
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "房屋信息",
                limit:30,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'combina_name',
                    title: '楼栋号'
                },
                {
                    field: 'floor',
                    title: '楼层'
                },
                {
                    field:'room',
                    title:'房号'
                },         
                {
                    field: 'use_area',
                    title: '使用面积',
                    formatter: function(value, row, index) {
                        return row.use_area+ "㎡";
                    }
                },                
                {
                    field: 'total_area',
                    title: '建筑面积',
                    formatter: function(value, row, index) {
                        return row.total_area+ "㎡";
                    }
                },
                {
                    field: 'owner_str',
                    title: '住户信息'
                },
                {
                    field: 'house_id',
                    title: '绑定住户',
                    visible: false,
                    formatter: function(value, row, index) {
                        var ownerCount = row.owner_count || 0;
                        if(ownerCount > 0) {
                            return ownerCount + '人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.house_id + '\')">查看住户</a>';
                        } else {
                            return '0人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.house_id + '\')">新增住户</a>';
                        }
                    }
                },
                {
                    field: 'house_type',
                    title: '住宅性质',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(houseTypeDatas, value);
                    }
                },
                {
                    field: 'house_status',
                    title: '房屋状态',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },{
                    field: 'room_tag',
                    title: '房屋标签',
                    visible: false
                },
                {
                    field: 'create_time',
                    title: '创建时间',
                    visible: false
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="houseDetail(\'' + row.house_id + '\')"><i class="fa fa-user"></i> 更绑住户</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.house_id + '\')"><i class="fa fa-edit"></i> 编辑</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.house_id + '\')"><i class="fa fa-remove"></i> 删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 房屋详情
        function houseDetail(houseId) {
            var url = prefix + "/owners/" + houseId;
            $.modal.openTab("房屋详情", url);
        }

        function loadBuildingZTree() {
            $.ajax({
                url: ctx + "oc/building/tree",
                type: "GET",
                success: function(res) {
                    if (res.code == 0) {
                        var zNodes = convertToZTreeNodes(res.data);
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    if(treeNode.type === 'building') {
                                        $("#buildingId").val(treeNode.id);
                                        $("#unitId").val("");
                                        $("input[name='buildingName']").val(treeNode.name);
                                        $("input[name='unitName']").val("");
                                    } else if(treeNode.type === 'unit') {
                                        $("#buildingId").val(treeNode.pId);
                                        $("#unitId").val(treeNode.id);
                                        $("input[name='buildingName']").val(treeNode.buildingName);
                                        $("input[name='unitName']").val(treeNode.name);
                                    }
                                    $.table.search();
                                }
                            }
                        };
                        $.fn.zTree.init($("#buildingTree"), setting, zNodes);
                    } else {
                        $.modal.alertError("加载楼栋单元树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载楼栋单元树失败：" + error);
                }
            });
        }

        // 转换接口数据为ztree扁平结构
        function convertToZTreeNodes(data) {
            var nodes = [];
            $.each(data, function(i, building) {
                var bNode = {
                    id: building.buildingId,
                    pId: null,
                    name: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋',
                    type: 'building',
                    open: false
                };
                nodes.push(bNode);
                if(building.children && building.children.length > 0) {
                    $.each(building.children, function(j, unit) {
                        nodes.push({
                            id: unit.unitId,
                            pId: building.buildingId,
                            name: unit.unitName.endsWith('单元') ? unit.unitName : unit.unitName + '单元',
                            type: 'unit',
                            buildingName: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋'
                        });
                    });
                }
            });
            return nodes;
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadBuildingZTree();
        });

        // 自定义重置
        function resetPre() {
            $("#formId")[0].reset();
            $("#buildingId").val("");
            $("#unitId").val("");
            $.table.search();
        }

        // 查看房屋绑定的住户
        function showOwners(houseId) {
            var url = prefix + "/owners/" + houseId;
            $.modal.openTab("房屋住户", url);
            }

        // 管理楼栋 
        function buildingMgr(){
            var url = ctx + "oc/building/mgr";
            $.modal.openTab("楼栋管理", url);
        }
        function importData(){

        }
    </script>
</body>
</html> 