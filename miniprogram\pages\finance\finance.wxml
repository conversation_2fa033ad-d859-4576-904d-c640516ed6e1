<view class="container">
  <!-- 余额信息卡片 -->
  <view class="balance-card">
    <view class="balance-info">
      <view class="balance-amount">{{accountInfo.balance}}</view>
      <view class="balance-label">账户余额</view>
    </view>
    <view class="balance-metadata">
      <view class="metadata-item">截止到: {{accountInfo.updateTime}}</view>
      <view class="metadata-item">数据来源: {{accountInfo.dataSource}}</view>
      <view class="metadata-item">{{accountInfo.accountNumber}}</view>
    </view>
  </view>

  <!-- 多年份分组展示 -->
  <view wx:if="{{yearData.length === 0}}" class="empty-tip">
    <image src="/static/images/empty-data.png" class="empty-img" mode="widthFix" />
    暂无收支数据
  </view>
  <block wx:for="{{yearData}}" wx:key="year" wx:for-index="yearIndex">
    <view class="year-stat">
      <view class="year-header" bindtap="toggleYear" data-year="{{item.year}}" data-index="{{yearIndex}}">
        <view class="year-title-row">
          <text class="year-title">{{item.year}}年</text>
          <text class="year-desc">银行账户流水</text>
        </view>
        <text class="year-arrow">
          <text class="iconfont {{item.expanded ? 'icon-up' : 'icon-down'}}"></text>
        </text>
      </view>
      <view wx:if="{{item.expanded}}">
        <!-- 加载状态 -->
        <view wx:if="{{item.loading}}" class="loading-tip">
          <text>加载中...</text>
        </view>
        <!-- 年度汇总 -->
        <view wx:elif="{{item.loaded}}" class="year-summary">
          <view class="summary-item income-block">
            <view class="summary-label">年度收入(元)</view>
            <view class="summary-value">{{item.yearIncome}}</view>
          </view>
          <view class="summary-item expense-block">
            <view class="summary-label">年度支出(元)</view>
            <view class="summary-value">{{item.yearExpense}}</view>
          </view>
        </view>
        <!-- 月份明细表 -->
        <view wx:if="{{item.loaded && item.months.length > 0}}" class="finance-table">
          <view class="table-header">
            <view class="header-cell">月份</view>
            <view class="header-cell">收入(元)</view>
            <view class="header-cell">支出(元)</view>
          </view>
          <block wx:for="{{item.months}}" wx:key="tran_month" wx:for-item="monthItem" wx:for-index="monthIndex">
            <view class="month-row" bindtap="toggleMonth" 
                  data-month="{{monthItem.tran_month}}" 
                  data-year="{{monthItem.year}}"
                  data-year-index="{{yearIndex}}"
                  data-month-index="{{monthIndex}}">
              <view class="month-cell">{{monthItem.monthStr}}</view>
              <view class="amount-cell">{{monthItem.income}}</view>
              <view class="amount-cell">{{monthItem.expend}}</view>
              <text class="toggle-icon {{monthItem.expanded ? 'expanded' : ''}}">
                <text class="iconfont {{monthItem.expanded ? 'icon-up' : 'icon-down'}}"></text>
              </text>
            </view>
            <view wx:if="{{monthItem.expanded}}">
              <view class="transaction-list expanded">
                <block wx:if="{{monthItem.loading}}">
                  <view class="empty-tip">加载中...</view>
                </block>
                <block wx:elif="{{monthItem.details && monthItem.details.length > 0}}">
                  <view class="month-summary">
                    <text class="month-income">收入: ¥{{monthItem.monthIncome}}</text>
                    <text class="month-expense">支出: ¥{{monthItem.monthExpense}}</text>
                  </view>
                  <block wx:for="{{monthItem.details}}" wx:key="id" wx:for-item="tran">
                    <view class="transaction-item" bindtap="viewTransactionDetail" data-id="{{tran.id}}">
                      <view class="transaction-info">
                        <view class="transaction-title">{{tran.title}}</view>
                        <view class="transaction-subtitle">{{tran.date}}</view>
                        <view class="transaction-time">{{tran.type === 'in' ? '收入' : '支出'}}</view>
                      </view>
                      <view class="transaction-amount {{tran.type === 'in' ? 'income' : 'expense'}}">
                        {{tran.type === 'in' ? '+' : '-'}}¥{{tran.amount}}
                      </view>
                      <text class="detail-arrow">
                        <text class="iconfont icon-right"></text>
                      </text>
                    </view>
                  </block>
                </block>
                <block wx:else>
                  <view class="empty-tip">本月暂无收支记录</view>
                </block>
              </view>
            </view>
          </block>
        </view>
        <!-- 无月份数据 -->
        <view wx:elif="{{item.loaded && item.months.length === 0}}" class="empty-tip">
          本年度暂无收支记录
        </view>
      </view>
    </view>
  </block>

  <!-- 原有的空数据提示，保留但隐藏 -->
  <view class="finance-list" style="display: none;">
    <block wx:if="{{financeList.length === 0}}">
      <view class="empty-tip">本月暂无收支记录</view>
    </block>
    <block wx:else>
      <view wx:for="{{financeList}}" wx:key="id" class="finance-item" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="item-left">
          <text class="item-title">{{item.title}}</text>
          <text class="item-date">{{item.date}}</text>
        </view>
        <text class="item-amount {{item.type === 'in' ? 'in' : 'out'}}">
          {{item.type === 'in' ? '+' : '-'}}¥{{item.amount}}
        </text>
      </view>
    </block>
  </view>
</view> 