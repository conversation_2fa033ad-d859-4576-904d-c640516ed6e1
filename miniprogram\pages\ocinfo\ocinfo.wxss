.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 40rpx;
}
.cover-section {
  position: relative;
  width: 100%;
  height: 320rpx;
}
.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.community-name {
  position: absolute;
  left: 0; right: 0; bottom: 0;
  font-size: 40rpx;
  color: #fff;
  font-weight: 700;
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  padding: 24rpx;
  text-align: left;
}
.info-card, .desc-card, .facility-card, .other-card {
  margin: 24rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}
.info-row {
  display: flex;
  margin-bottom: 18rpx;
}
.info-label {
  width: 140rpx;
  color: #888;
  font-size: 28rpx;
}
.info-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}
.phone {
  color: #07c160;
  text-decoration: underline;
}
.desc-title, .facility-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}
.desc-content {
  color: #555;
  font-size: 28rpx;
  line-height: 1.7;
}
.facility-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}
.facility-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
}
.facility-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 8rpx;
}
.facility-name {
  font-size: 26rpx;
  color: #333;
}
.other-btn {
  width: 100%;
  margin-top: 16rpx;
  background: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 12rpx;
} 