package com.ehome.oc.mapper;

import com.ehome.oc.domain.HouseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HouseInfoMapper {
    /**
     * 查询房屋列表
     */
    List<HouseInfo> selectHouseList(@Param("wxUserId") Long wxUserId);

    /**
     * 查询房屋详情
     */
    HouseInfo selectHouseById(@Param("houseId") Long houseId);

    /**
     * 新增房屋
     */
    int insertHouse(HouseInfo house);

    /**
     * 修改房屋
     */
    int updateHouse(HouseInfo house);

    /**
     * 删除房屋
     */
    int deleteHouseById(@Param("houseId") Long houseId);

    /**
     * 重置默认房屋
     */
    int resetDefaultHouse(@Param("wxUserId") Long wxUserId);

    /**
     * 设置默认房屋
     */
    int setDefaultHouse(@Param("houseId") Long houseId);

    /**
     * 查询用户的房屋数量
     */
    int selectHouseCount(@Param("wxUserId") Long wxUserId);

    /**
     * 统计用户的房屋数量
     */
    int countHouseByUserId(@Param("wxUserId") Long wxUserId);
} 