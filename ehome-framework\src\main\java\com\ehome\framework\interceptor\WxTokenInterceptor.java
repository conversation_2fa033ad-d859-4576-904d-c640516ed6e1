package com.ehome.framework.interceptor;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class WxTokenInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(WxTokenInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                throw new ServiceException("缺少或格式错误的token");
            }
            LoginUser loginUser = SecurityUtils.getLoginUser(token);
            if (loginUser == null) {
                throw new ServiceException("token无效");
            }
            // 可选：将 loginUser 放入 request attribute
            request.setAttribute("currentUser", loginUser);
            return true;
        } catch (ServiceException ex) {
            logger.warn("Token校验失败: {}", ex.getMessage());
            response.setStatus(401);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"msg\":\"未登录或token失效\"}");
            return false;
        } catch (Exception e) {
            logger.error("Token校验异常: {}", e.getMessage(), e);
            response.setStatus(401);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"msg\":\"token校验异常\"}");
            return false;
        }
    }
} 