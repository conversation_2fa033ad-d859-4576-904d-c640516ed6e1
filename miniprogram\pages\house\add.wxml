<view class="container">
  <view class="form">
    <view class="form-group">
      <view class="form-label">所属小区 <text class="required">*</text></view>
      <picker bindchange="onCommunityChange" value="{{communityIndex}}" range="{{communityList}}" range-key="ocName">
        <view class="picker">
          <text class="{{selectedCommunity ? '' : 'placeholder'}}">{{selectedCommunity ? selectedCommunity.ocName : '请选择所属小区'}}</text>
        </view>
      </picker>
    </view>

    <view class="form-group">
      <view class="form-label">楼栋号 <text class="required">*</text></view>
      <input class="form-input" type="text" placeholder="请输入楼栋号" value="{{building}}" bindinput="onBuildingInput" />
    </view>

    <view class="form-group">
      <view class="form-label">单元号 <text class="required">*</text></view>
      <input class="form-input" type="text" placeholder="请输入单元号" value="{{unit}}" bindinput="onUnitInput" />
    </view>

    <view class="form-group">
      <view class="form-label">房间号 <text class="required">*</text></view>
      <input class="form-input" type="text" placeholder="请输入房间号" value="{{room}}" bindinput="onRoomInput" />
    </view>

    <view class="form-group">
      <view class="form-label">业主姓名 <text class="required">*</text></view>
      <input class="form-input" type="text" placeholder="请输入业主姓名" value="{{ownerName}}" bindinput="onOwnerNameInput" />
    </view>

    <view class="form-group">
      <view class="form-label">联系电话 <text class="required">*</text></view>
      <input class="form-input" type="number" placeholder="请输入联系电话" value="{{ownerPhone}}" bindinput="onOwnerPhoneInput" />
    </view>

    <view class="form-group">
      <view class="form-label">身份证号 <text class="required">*</text></view>
      <input class="form-input" type="idcard" placeholder="请输入身份证号" value="{{idCard}}" bindinput="onIdCardInput" />
    </view>

    <view class="form-tips">
      <text class="tips-text">注：带 * 为必填项</text>
      <text class="tips-text">提交后需要等待物业审核，审核通过后方可使用</text>
    </view>
  </view>

  <view class="submit-btn {{canSubmit ? '' : 'disabled'}}" bindtap="submitForm">
    提交认证
  </view>
</view> 