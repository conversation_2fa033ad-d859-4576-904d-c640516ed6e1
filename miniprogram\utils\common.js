// 通用工具函数
import { USER_ROLE_NAMES } from '../constants/index.js'

/**
 * 时间格式化函数
 * @param {string|number|Date} timestamp 时间戳或时间字符串
 * @param {string} format 格式化模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(timestamp, format = 'YYYY-MM-DD') {
  try {
    if (!timestamp) return ''

    // 安全的类型检查
    if (timestamp === null || timestamp === undefined) {
      return ''
    }

    let dateStr = timestamp

    // 处理不同格式的时间字符串
    if (typeof dateStr === 'string' && dateStr.indexOf('-') > -1 && dateStr.indexOf(':') > -1) {
      // 将 "yyyy-MM-dd HH:mm:ss" 转为 "yyyy-MM-ddTHH:mm:ss"
      dateStr = dateStr.replace(' ', 'T')

      // 如果还是无效，转为 "yyyy/MM/dd HH:mm:ss"
      if (isNaN(new Date(dateStr).getTime())) {
        dateStr = dateStr.replace(/-/g, '/').replace('T', ' ')
      }
    }

    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      console.warn('[formatTime] 无效的时间格式:', timestamp)
      // 安全的toString调用
      return (timestamp && typeof timestamp.toString === 'function') ? timestamp.toString() : String(timestamp)
    }
    
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
      
  } catch (error) {
    console.error('[formatTime] 时间格式化失败:', error)
    // 安全的错误处理
    return (timestamp && typeof timestamp.toString === 'function') ? timestamp.toString() : String(timestamp || '')
  }
}

/**
 * 防抖函数
 * @param {Function} func 需要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 需要节流的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 300) {
  let timeout
  return function executedFunction(...args) {
    if (!timeout) {
      func.apply(this, args)
      timeout = setTimeout(() => {
        timeout = null
      }, wait)
    }
  }
}

/**
 * 深拷贝函数
 * @param {any} obj 需要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (obj instanceof Object) {
    const copy = {}
    Object.keys(obj).forEach(key => {
      copy[key] = deepClone(obj[key])
    })
    return copy
  }
}

/**
 * HTML标签过滤函数
 * @param {string} html HTML字符串
 * @returns {string} 过滤后的纯文本
 */
export function filterHtmlTags(html) {
  if (!html || typeof html !== 'string') return ''
  
  return html
    .replace(/<[^>]*>/g, '') // 移除HTML标签
    .replace(/&nbsp;/g, ' ') // 替换空格实体
    .replace(/&lt;/g, '<')   // 替换小于号实体
    .replace(/&gt;/g, '>')   // 替换大于号实体
    .replace(/&amp;/g, '&')  // 替换和号实体
    .replace(/&quot;/g, '"') // 替换引号实体
    .trim()
}

/**
 * 获取用户角色名称
 * @param {string} roleCode 角色代码
 * @returns {string} 角色名称
 */
export function getUserRoleName(roleCode) {
  return USER_ROLE_NAMES[roleCode] || '未知角色'
}

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export function validatePhone(phone) {
  if (!phone || typeof phone !== 'string') return false
  return /^1[3-9]\d{9}$/.test(phone)
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
export function validateIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') return false
  return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
export function validateEmail(email) {
  if (!email || typeof email !== 'string') return false
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 扩展名
 */
export function getFileExtension(filename) {
  if (!filename || typeof filename !== 'string') return ''
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 检查是否为空值
 * @param {any} value 值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

export default {
  formatTime,
  debounce,
  throttle,
  deepClone,
  filterHtmlTags,
  getUserRoleName,
  validatePhone,
  validateIdCard,
  validateEmail,
  formatFileSize,
  getFileExtension,
  generateUUID,
  isEmpty
} 