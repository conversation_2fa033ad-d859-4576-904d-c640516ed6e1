# 优化 clearLoginState 函数

## 问题描述
在 app.js 中，checkToken 函数存在潜在问题，当 token 验证失败时会清除登录状态，但没有明确的重新登录引导机制，存在以下问题：
1. 导航冲突：使用 wx.navigateTo 可能造成页面栈重复
2. 缺乏用户提示：token 验证失败时直接跳转
3. 防抖机制不完善
4. 状态同步问题

## 解决方案
采用方案1：优化现有 clearLoginState 函数

## 执行计划
1. 优化 app.js 中的 clearLoginState 函数
2. 优化网络请求中的登录失效处理  
3. 增强 checkToken 函数的错误处理
4. 优化 stateManager 的 clearState 方法
5. 更新错误处理工具

## 执行状态
- [x] 步骤1：优化 clearLoginState 函数
- [x] 步骤2：优化网络请求处理
- [x] 步骤3：增强 checkToken 函数
- [x] 步骤4：优化 stateManager
- [x] 步骤5：更新错误处理工具

## 完成的优化
1. **clearLoginState 函数优化**：
   - 添加了 showMessage 参数控制是否显示提示
   - 改进了导航逻辑，避免重复跳转到登录页
   - 增强了防抖机制，延长防抖时间到2秒
   - 添加了用户友好的提示信息
   - 根据页面栈深度选择合适的跳转方式

2. **网络请求优化**：
   - 在401错误处理中添加了详细日志
   - 确保调用 clearLoginState 时显示提示信息

3. **checkToken 函数增强**：
   - 添加了详细的日志记录
   - 区分不同类型的验证失败情况
   - 网络错误时不清除登录状态，给用户重试机会
   - 启动时验证失败不显示提示，避免干扰用户

4. **stateManager 优化**：
   - 添加了回调函数支持
   - 增加了详细的日志记录

5. **错误处理工具更新**：
   - 完善了 handleLoginExpired 函数
   - 添加了重复调用检查
   - 增加了错误日志
