<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('房屋住户绑定')" />
    <style>
        .container-div {
            background: #f0f2f5;
        }
        .fixed-table-toolbar {
            display: none;
        }
        .ibox-tools{
            bottom: 5px;
        }
        .ibox {
            margin-bottom: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,.1);
        }
        .ibox-title {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background-color: #f8f8f8;
            border-radius: 4px 4px 0 0;
        }
        .ibox-content {
            padding: 20px;
            border-style: none;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .control-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: right;
        }
        .form-control-static {
            padding-top: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        .table-responsive {
            margin-top: 10px;
        }
        #bootstrap-table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        #bootstrap-table td {
            vertical-align: middle;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 3px;
        }
        .badge-primary {
            background-color: #1890ff;
        }
        .badge-default {
            background-color: #d9d9d9;
            color: #666;
        }
        .btn-xs {
            padding: 2px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
        .modal-body {
            padding: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <input type="hidden" id="houseId" th:value="${houseId}"/>
        <div class="row" style="margin-top: 10px;margin-left: -30px;margin-right: -30px;">
            <div class="col-sm-12">
                <!-- 房屋信息展示区域 -->
                <div class="ibox" style="margin-bottom: 10px;">
                    <div class="ibox-title">
                        <h5>房屋信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row" id="houseInfo">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">楼栋/单元：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="buildingUnitInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房间号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="roomInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">楼层：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="floorInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">建筑面积：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="totalAreaInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">室内面积：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="useAreaInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房屋类型：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="houseTypeInfo"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">房屋状态：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="houseStatusInfo">--</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">创建时间：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="createTimeInfo">--</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">备注：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="remarkInfo">--</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 住户列表区域 -->
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>住户列表</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="bindOwner()">
                                <i class="fa fa-plus"></i> 绑定住户
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table id="bootstrap-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>

                <!--车辆列表区域-->
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>车辆列表</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="bindVehicle()">
                                <i class="fa fa-plus"></i> 绑定车辆
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="table-responsive">
                            <table id="vehicle-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    
    <!-- 选择业主弹窗 -->
    <div id="selectOwnerModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="selectOwnerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="selectOwnerModalLabel">选择业主</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="ownerSearchInput" style="width: 200px;display: inline-block;" class="form-control" placeholder="输入房屋编号楼栋-单元-房屋">
                                    <input type="text" id="ownerNameInput" style="width: 150px;display: inline-block;margin-left: 5px;" class="form-control" placeholder="输入业主名称">
                                    <span class="input-group-btn" syle="margin-left: 15px;float: left;">
                                        <button type="button" class="btn btn-primary" onclick="searchOwner()">
                                            <i class="fa fa-search"></i> 查询
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="resetOwnerSearch()">
                                            <i class="fa fa-refresh"></i> 重置
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="owner-table" data-mobile-responsive="true"></table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 绑定住户弹窗 -->
    <div id="bindOwnerModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="bindOwnerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="bindOwnerModalLabel">绑定住户</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" id="bindOwnerForm">
                        <input type="hidden" id="bindHouseId" name="houseId">
                        <input type="hidden" id="bindOwnerId" name="ownerId">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">业主姓名：</label>
                            <div class="col-sm-8">
                                <input type="text" id="bindOwnerName" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <input type="text" id="bindOwnerPhone" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">关系类型：</label>
                            <div class="col-sm-8">
                                <select id="relType" name="relType" class="form-control">
                                    <option value="1">业主</option>
                                    <option value="2">家庭成员</option>
                                    <option value="3">租户</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">是否默认：</label>
                            <div class="col-sm-8">
                                <label class="toggle-switch switch-solid">
                                    <input type="checkbox" id="isDefault" name="isDefault" checked>
                                    <span></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">备注：</label>
                            <div class="col-sm-8">
                                <textarea id="remark" name="remark" class="form-control"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitBindOwner()">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 选择车辆弹窗 -->
    <div id="selectVehicleModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="selectVehicleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="selectVehicleModalLabel">选择车辆</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="vehiclePlateInput" style="width: 200px;display: inline-block;" class="form-control" placeholder="输入车牌号">
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-primary" onclick="searchVehicle()">
                                            <i class="fa fa-search"></i> 查询
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="resetVehicleSearch()">
                                            <i class="fa fa-refresh"></i> 重置
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="vehicle-select-table" data-mobile-responsive="true"></table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/house";
        var houseId = $("#houseId").val();
        var relTypeDatas = [
            { dictValue: "1", dictLabel: "业主" },
            { dictValue: "2", dictLabel: "家庭成员" },
            { dictValue: "3", dictLabel: "租户" }
        ];
        var checkStatusDatas = [
            { dictValue: "0", dictLabel: "未审核" },
            { dictValue: "1", dictLabel: "已审核" },
            { dictValue: "2", dictLabel: "审核不通过" }
        ];
        var statusDatas = [
            { dictValue: "2002", dictLabel: "未销售" },
            { dictValue: "2001", dictLabel: "已入住" },
            { dictValue: "2003", dictLabel: "已交房" },
            { dictValue: "2005", dictLabel: "已装修" },
            { dictValue: "2004", dictLabel: "未入住" },
            { dictValue: "2009", dictLabel: "装修中" }
        ];
        var houseTypeDatas = [
            { dictValue: "1", dictLabel: "住宅" },
            { dictValue: "2", dictLabel: "商铺" }
        ];
        var roleDatas = [
            { dictValue: "1", dictLabel: "业主" },
            { dictValue: "2", dictLabel: "家庭成员" },
            { dictValue: "3", dictLabel: "租户" }
        ];
        var vehicleTypeDatas = [
            { dictValue: "1", dictLabel: "业主车辆" },
            { dictValue: "2", dictLabel: "公共车辆" }
        ];
        
        $(function() {
            // 加载房屋信息
            loadHouseInfo();
            
            // 初始化住户列表
            initOwnerTable();
            // 初始化车辆列表
            initVehicleTable();
        });
        
        // 加载房屋信息
        function loadHouseInfo() {
            $.ajax({
                url: prefix + "/detail",
                type: "post",
                data: { house_id: houseId },
                success: function(res) {
                    if (res.code == 0) {
                        var house = res.data;
                        $("#buildingUnitInfo").text(house.buildingName + "/" + house.unitName);
                        $("#roomInfo").text(house.room);
                        $("#floorInfo").text(house.floor);
                        $("#totalAreaInfo").text(house.total_area + "㎡");
                        $("#useAreaInfo").text(house.use_area + "㎡");
                        
                        // 使用字典数据显示房屋类型
                        $("#houseTypeInfo").html($.table.selectDictLabel(houseTypeDatas, house.house_type) || "-");
                        
                        // 使用字典数据显示房屋状态
                        $("#houseStatusInfo").html($.table.selectDictLabel(statusDatas, house.house_status) || "-");
                        
                        $("#createTimeInfo").text(house.create_time);
                        $("#remarkInfo").text(house.remark || "-");
                    } else {
                        $.modal.alertError("加载房屋信息失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载房屋信息失败");
                }
            });
        }
        
        // 初始化住户列表
        function initOwnerTable() {
            var options = {
                url: prefix + "/ownerList",
                queryParams: { houseId: houseId },
                modalName: "住户",
                striped: true,
                bordered: true,
                showToolbar: false,        // 禁用工具栏
                search: false,             // 禁用搜索框
                showSearch: false,         // 隐藏搜索按钮
                showRefresh: false,        // 隐藏刷新按钮
                showColumns: false,        // 隐藏列选择按钮
                columns: [{
                    checkbox: true,
                    width: 40
                },
                {
                    field: 'owner_name',
                    title: '住户姓名',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'mobile',
                    title: '联系电话',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'gender',
                    title: '性别',
                    align: 'center',
                    width: 60,
                    formatter: function(value, row, index) {
                        return value == 'M' ? '男' : (value == 'F' ? '女' : '-');
                    }
                },
                {
                    field: 'role',
                    title: '人员角色',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(roleDatas, value) || '-';
                    }
                },
                {
                    field: 'house_info',
                    title: '已绑定房屋',
                    width:120
                },
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },            
                {
                    title: '操作',
                    align: 'center',
                    width: 160,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindOwner(\'' + row.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ');
                        if (row.is_default != 1) {
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="setDefault(\'' + row.rel_id + '\')"><i class="fa fa-check"></i> 设为默认</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        }
        
        // 绑定住户
        function bindOwner() {
            // 初始化业主选择表格
            initOwnerSelectTable();
            // 显示选择业主弹窗
            $("#selectOwnerModal").modal("show");
        }
        
        // 初始化业主选择表格
        function initOwnerSelectTable() {
            var options = {
                id: "owner-table",
                url: ctx + "oc/owner/list",
                method: "post",
                modalName: "业主",
                pagination: true,                 // 启用分页
                pageSize: 10,                     // 每页显示数量
                pageList: [10, 20, 50],          // 每页显示数量选项
                sidePagination: "server",         // 服务端分页
                queryParams: function(params) {   // 查询参数
                    var search = {};
                    search.pageSize = params.limit;
                    search.pageNum = params.offset / params.limit + 1;
                    search.owner_name = $("#ownerNameInput").val();
                    search.house_code = $("#ownerSearchInput").val();
                    return search;
                },
                columns: [
                {
                    field: 'owner_name',
                    title: '姓名'
                },
                {
                    field: 'mobile',
                    title: '联系方式'
                },
                {
                    field: 'house_info',
                    title: '已绑房屋',
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return '<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="selectOwner(\'' + row.owner_id + '\', \'' + row.owner_name + '\', \'' + row.mobile + '\')"><i class="fa fa-check"></i>选择</a>';
                    }
                }]
            };
            $.table.init(options);
        }
        
        // 搜索业主
        function searchOwner() {
            $("#owner-table").bootstrapTable('refresh');
        }
        
        // 重置业主搜索
        function resetOwnerSearch() {
            $("#ownerSearchInput").val("");
            $("#ownerNameInput").val("");
            $("#owner-table").bootstrapTable('refresh');
        }
        
        // 选择业主
        function selectOwner(ownerId, ownerName, ownerPhone) {
            // 关闭选择业主弹窗
            $("#selectOwnerModal").modal("hide");
            
            // 填充绑定表单
            $("#bindHouseId").val(houseId);
            $("#bindOwnerId").val(ownerId);
            $("#bindOwnerName").val(ownerName);
            $("#bindOwnerPhone").val(ownerPhone);
            
            // 显示绑定弹窗
            $("#bindOwnerModal").modal("show");
        }
        
        // 提交绑定
        function submitBindOwner() {
            var data = {
                houseId: $("#bindHouseId").val(),
                ownerId: $("#bindOwnerId").val(),
                relType: $("#relType").val(),
                isDefault: $("#isDefault").is(":checked") ? 1 : 0,
                remark: $("#remark").val()
            };
            
            $.ajax({
                url: prefix + "/bindOwner",
                type: "post",
                data: data,
                success: function(res) {
                    if (res.code == 0) {
                        $.modal.msgSuccess("绑定成功");
                        location.reload();
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }
        
        // 解绑住户
        function unbindOwner(relId) {
            $.modal.confirm("确定解绑该住户吗？", function() {
                $.ajax({
                    url: prefix + "/unbindOwner",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            location.reload();
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }
        
        // 设置默认住户
        function setDefault(relId) {
            $.modal.confirm("确定将该住户设为默认住户吗？", function() {
                $.ajax({
                    url: prefix + "/setDefaultOwner",
                    type: "post",
                    data: { relId: relId, houseId: houseId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("设置成功");
                            location.reload();
                        } else {
                            $.modal.alertError("设置失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("设置失败");
                    }
                });
            });
        }

        // ================= 车辆相关 =================
        // 初始化车辆列表
        function initVehicleTable() {
            var options = {
                id: "vehicle-table",
                url: prefix + "/vehicleList",
                queryParams: { houseId: houseId },
                modalName: "车辆",
                striped: true,
                bordered: true,
                showToolbar: false,
                search: false,
                showSearch: false,
                showRefresh: false,
                showColumns: false,
                columns: [
                    { checkbox: true, width: 40 },
                    { field: 'plate_no', title: '车牌号', align: 'center', width: 120 },
                    { field: 'vehicle_type', title: '车辆类型', align: 'center', width: 100, formatter: function(value) { return $.table.selectDictLabel(vehicleTypeDatas, value) || '-'; } },
                    { field: 'remark', title: '备注', align: 'center', width: 100 },
                    { title: '操作', align: 'center', width: 100, formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindVehicle(\'' + row.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ');
                        return actions.join('');
                    }}
                ]
            };
            $.table.init(options);
        }

        // 绑定车辆
        function bindVehicle() {
            initVehicleSelectTable();
            $("#selectVehicleModal").modal("show");
        }

        // 初始化车辆选择表格
        function initVehicleSelectTable() {
            var options = {
                id: "vehicle-select-table",
                url: ctx + "oc/vehicle/list",
                method: "post",
                modalName: "车辆",
                pagination: true,
                pageSize: 10,
                pageList: [10, 20, 50],
                sidePagination: "server",
                queryParams: function(params) {
                    var search = {};
                    search.pageSize = params.limit;
                    search.pageNum = params.offset / params.limit + 1;
                    search.plate_no = $("#vehiclePlateInput").val();
                    return search;
                },
                columns: [
                    { field: 'plate_no', title: '车牌号' },
                    { field: 'vehicle_type', title: '车辆类型', formatter: function(value) { return $.table.selectDictLabel(vehicleTypeDatas, value) || '-'; } },
                    { title: '操作', align: 'center', formatter: function(value, row, index) {
                        return '<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="selectVehicle(\'' + row.vehicle_id + '\', \'' + row.plate_no + '\')"><i class="fa fa-check"></i>选择</a>';
                    }}
                ]
            };
            $.table.init(options);
        }

        // 搜索车辆
        function searchVehicle() {
            $("#vehicle-select-table").bootstrapTable('refresh');
        }

        // 重置车辆搜索
        function resetVehicleSearch() {
            $("#vehiclePlateInput").val("");
            $("#vehicle-select-table").bootstrapTable('refresh');
        }

        // 选择车辆
        function selectVehicle(vehicleId, plateNo) {
            $("#selectVehicleModal").modal("hide");
            $.modal.confirm("确定将车辆【" + plateNo + "】绑定到本房屋吗？", function() {
                $.ajax({
                    url: prefix + "/bindVehicle",
                    type: "post",
                    data: { houseId: houseId, vehicleId: vehicleId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("绑定成功");
                            $.table.refresh("vehicle-table");
                        } else {
                            $.modal.alertError("绑定失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("绑定失败");
                    }
                });
            });
        }

        // 解绑车辆
        function unbindVehicle(relId) {
            $.modal.confirm("确定解绑该车辆吗？", function() {
                $.ajax({
                    url: prefix + "/unbindVehicle",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            $.table.refresh("vehicle-table");
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }
    </script>
</body>
</html> 