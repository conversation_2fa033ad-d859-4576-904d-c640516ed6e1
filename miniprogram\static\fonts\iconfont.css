@font-face {
  font-family: "iconfont"; /* Project id 4812128 */
  src: url('iconfont.woff2?t=1747825643862') format('woff2'),
       url('iconfont.woff?t=1747825643862') format('woff'),
       url('iconfont.ttf?t=1747825643862') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-down:before {
  content: "\e632";
}

.icon-cheliang:before {
  content: "\e62a";
}

.icon-chewei:before {
  content: "\ec8e";
}

.icon-zuo:before {
  content: "\e63d";
}

.icon-wuyebaoxiu:before {
  content: "\e727";
}

.icon-tousu:before {
  content: "\e6dd";
}

.icon-fuwudianhua:before {
  content: "\e602";
}

.icon-yaoqing:before {
  content: "\e654";
}

.icon-hetong:before {
  content: "\e6d5";
}

.icon-you:before {
  content: "\e612";
}

.icon-shang:before {
  content: "\e63c";
}

.icon-up:before {
  content: "\e608";
}

.icon-tongzhi:before {
  content: "\e759";
}

.icon-gonggao:before {
  content: "\e609";
}

.icon-wodefangwu:before {
  content: "\e634";
}

.icon-xinwen:before {
  content: "\e613";
}

.icon-profile:before {
  content: "\e6b7";
}

.icon-service:before {
  content: "\e6c7";
}

.icon-arrow:before {
  content: "\e664";
}

.icon-about:before {
  content: "\e640";
}

.icon-suggestion:before {
  content: "\e6b4";
}

.icon-house:before {
  content: "\e61a";
}

.icon-wechat-fill:before {
  content: "\e883";
}

.icon-buildings:before {
  content: "\e607";
}

.icon-menu_area:before {
  content: "\e669";
}

.icon-icon_unit:before {
  content: "\e629";
}

.icon-mine:before {
  content: "\e600";
}

.icon-complaint:before {
  content: "\e656";
}

.icon-payment:before {
  content: "\e64a";
}

.icon-announcement:before {
  content: "\e601";
}

.icon-repair:before {
  content: "\e615";
}

.icon-package:before {
  content: "\e8b6";
}

.icon-visitor:before {
  content: "\e72f";
}

