<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>车牌号：</label>
                                <input type="text" name="plate_no"/>
                            </li>
                            <li>
                                <label>车辆类型：</label>
                                <select name="vehicle_type">
                                    <option value="">所有</option>
                                    <option value="1">业主车辆</option>
                                    <option value="2">公共车辆</option>
                                </select>
                            </li>
                            <li>
                                <label>审核状态：</label>
                                <select name="check_status">
                                    <option value="">所有</option>
                                    <option value="0">未审核</option>
                                    <option value="1">已审核</option>
                                    <option value="2">审核不通过</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="oc:vehicle:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="oc:vehicle:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="oc:vehicle:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('oc:vehicle:edit')}]];
        var removeFlag = [[${@permission.hasPermi('oc:vehicle:remove')}]];
        var prefix = ctx + "oc/vehicle";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "车辆",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'vehicle_id',
                    title: '车辆ID',
                    visible: false
                },
                {
                    field: 'plate_no',
                    title: '车牌号'
                },
                {
                    field: 'vehicle_type',
                    title: '车辆类型'
                },
                {
                    field: 'owner_real_name',
                    title: '车主姓名'
                },
                {
                    field: 'owner_phone',
                    title: '车主号码'
                },
                {
                    field:'owner_name',
                    title:'绑定住户'
                },
                {
                    field:'house_name',
                    title:'绑定房屋'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.vehicle_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.vehicle_id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>