<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('小区管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<input type="hidden" name="pmsId" th:value="${pmsId}"/>
					<div class="select-list">
						<ul>
							<li>
								小区名称：<input type="text" name="oc_name"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/info";

        $(function() {
            var options = {
                url: prefix + "/pmsOcList",
                modalName: "小区",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'oc_id',
                    title: '小区ID',
                    visible: false
                },
                {
                    field: 'oc_name',
                    title: '小区名称',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'oc_address',
                    title: '小区地址',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'oc_link',
                    title: '联系人信息',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'oc_state',
                    title: '小区状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 0) return '<span class="badge badge-primary">正常</span>';
                        else if (value == 1) return '<span class="badge badge-warning">维护中</span>';
                        else if (value == 2) return '<span class="badge badge-danger">关闭</span>';
                        return value;
                    }
                },
                {
                    field: 'owner_count',
                    title: '业主数量',
                    align: 'center'
                },
                {
                    field: 'building_num', 
                    title: '楼宇栋数',
                    align: 'center'
                },
                {
                    field: 'community_name',
                    title: '所属社区'
                },
                {
                    field: 'create_by',
                    title: '创建人'
                },
                {
                    field: 'create_time',
                    title: '创建时间'
                }]
            };
            $.table.init(options);
        });

    </script>
</body>
</html>