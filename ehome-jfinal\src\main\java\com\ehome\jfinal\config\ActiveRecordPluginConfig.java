package com.ehome.jfinal.config;

import com.ehome.framework.config.properties.DruidProperties;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.druid.DruidPlugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Configuration
public class ActiveRecordPluginConfig {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    @Value("${spring.datasource.druid.master.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.druid.master.username}")
    private String username;

    @Value("${spring.datasource.druid.master.password}")
    private String password;

    @Value("${spring.datasource.driverClassName}")
    private String driverClassName;

    @Autowired
    private DruidProperties druidProperties;

    private DruidPlugin druidPlugin;

    private ActiveRecordPlugin arp;

    @PostConstruct
    public void init() {
        logger.info("开始初始化 JFinal ActiveRecord 插件...");
        
        druidPlugin = new DruidPlugin(jdbcUrl, username, password.trim(), driverClassName);

        druidProperties.configureDruidPlugin(druidPlugin);
        druidPlugin.start();
        
        arp = new ActiveRecordPlugin(druidPlugin);
        arp.setShowSql(true);
        arp.setDevMode(true);

        _MappingKit.mapping(arp);
        
        arp.start();
        
        logger.info("JFinal ActiveRecord 插件启动成功");
    }

    @Bean
    public ActiveRecordPlugin activeRecordPlugin() {
        return arp;
    }

    @PreDestroy
    public void destroy() {
        logger.info("正在关闭 JFinal ActiveRecord 插件...");
        if (arp != null) {
            arp.stop();
        }
        if (druidPlugin != null) {
            druidPlugin.stop();
        }
        logger.info("JFinal ActiveRecord 插件已关闭");
    }
}
