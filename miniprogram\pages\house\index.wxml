<view class="container">
  <!-- 房屋列表 -->
  <view class="house-list">
    <view class="house-card" wx:for="{{houseList}}" wx:key="id">
      <view class="house-info">
        <view class="house-title">{{item.communityName}}</view>
        <view class="house-address">{{item.combinaName}}/{{item.room}}</view>
        <view class="house-status-wrapper">
          <view class="house-status {{item.isDefault === 1 ? 'default' : ''}}">
            {{item.isDefault === 1 ? '默认房屋' : ''}}
          </view>
        </view>
      </view>
      <view class="house-actions">
        <view class="action-btn edit" bindtap="editHouse" data-id="{{item.id}}">
          <text class="iconfont icon-right"></text>
          <text>查看房屋详情</text>
        </view>
        <view class="action-btn set-default" bindtap="setDefault" data-id="{{item.id}}" wx:if="{{item.isDefault !== 1 && item.status === 1}}">
          <text class="iconfont icon-default"></text>
          <text>设为默认</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加房屋按钮 -->
  <view class="add-house" bindtap="addVisit">
    <text class="iconfont icon-add"></text>
    <text>邀请住户</text>
  </view>

</view>