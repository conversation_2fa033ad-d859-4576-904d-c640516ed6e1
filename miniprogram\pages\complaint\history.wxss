.history-page { padding: 32rpx; background: #f7f8fa; min-height: 100vh; }
.loading, .empty { text-align: center; color: #aaa; margin-top: 80rpx; }
.history-item { background: #fff; border-radius: 12rpx; margin-bottom: 32rpx; padding: 24rpx; box-shadow: 0 2rpx 8rpx #eee; }
.item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12rpx; }
.type { color: #1890ff; font-weight: 600; }
.status { color: #faad14; font-size: 26rpx; }
.content { color: #333; font-size: 30rpx; margin-bottom: 8rpx; }
.meta { display: flex; align-items: center; gap: 16rpx; }
.time { color: #aaa; font-size: 24rpx; }
.thumb { width: 80rpx; height: 80rpx; border-radius: 8rpx; background: #eee; }
.reply { margin-top: 8rpx; font-size: 26rpx; color: #666; }
.reply-label { color: #888; margin-right: 8rpx; }
.reply-content { color: #333; } 