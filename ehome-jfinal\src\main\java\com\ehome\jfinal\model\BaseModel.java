package com.ehome.jfinal.model;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Table;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class BaseModel<M extends BaseModel> extends Model<M> {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    public  void setColumns(JSONObject params){
      if(params!=null){
          Table table = _getTable();
          Set<String> keys =  params.keySet();
          for (String key:keys){
              if(table.hasColumnLabel(key)){
                  this.set(key,params.get(key));
              }else{
                  logger.error("表"+table.getName()+"没有字段"+key);
              }
          }
      }
    }
}
