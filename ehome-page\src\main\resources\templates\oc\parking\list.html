<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车位信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>车位号：</label>
                                <input type="text" name="parking_no"/>
                            </li>
                            <li>
                                <label>车位类型：</label>
                                <select name="parking_type">
                                    <option value="">所有</option>
                                    <option value="1">私人车位</option>
                                    <option value="2">子母车位</option>
                                </select>
                            </li>
                            <li>
                                <label>车位状态：</label>
                                <select name="parking_status">
                                    <option value="">所有</option>
                                    <option value="1">出售</option>
                                    <option value="2">出租</option>
                                    <option value="3">自用</option>
                                </select>
                            </li>
                            <li>
                                <label>审核状态：</label>
                                <select name="check_status">
                                    <option value="">所有</option>
                                    <option value="0">未审核</option>
                                    <option value="1">已审核</option>
                                    <option value="2">审核不通过</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="oc:parking:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="oc:parking:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="oc:parking:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/parking";
        var parkingTypeDatas = [
            { dictValue: "1", dictLabel: "私人车位" },
            { dictValue: "2", dictLabel: "子母车位" }
        ];
        var parkingStatusDatas = [
            { dictValue: "1", dictLabel: "出售" },
            { dictValue: "2", dictLabel: "出租" },
            { dictValue: "3", dictLabel: "自用" }
        ];
        var checkStatusDatas = [
            { dictValue: "0", dictLabel: "未审核" },
            { dictValue: "1", dictLabel: "已审核" },
            { dictValue: "2", dictLabel: "审核不通过" }
        ];

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "车位信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'parking_no',
                    title: '车位号'
                },
                {
                    field: 'parking_type',
                    title: '车位类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(parkingTypeDatas, value);
                    }
                },
                {
                    field: 'parking_status',
                    title: '车位状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(parkingStatusDatas, value);
                    }
                },
                {
                    field: 'owner_count',
                    title: '绑定业主',
                    formatter: function(value, row, index) {
                        var ownerCount = row.owner_count || 0;
                        if(ownerCount > 0) {
                            return ownerCount + '人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.parking_id + '\')">查看业主</a>';
                        } else {
                            return '0人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.parking_id + '\')">新增业主</a>';
                        }
                    }
                },
                {
                    field: 'check_status',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(checkStatusDatas, value);
                    }
                },
                {
                    field: 'create_time',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.parking_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.parking_id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function showOwners(parkingId) {
            var url = prefix + "/owners/" + parkingId;
            $.modal.openTab("查看业主", url);
        }
    </script>
</body>
</html> 