<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta charset="UTF-8">
    <title>小区收支公示</title>
    <link rel="stylesheet" href="https://cdn.easybui.com/1.9.30/bui.css">
</head>
<body>
    <div class="bui-page">
        <!-- 头部导航 -->
        <header class="bui-bar">
            <div class="bui-bar-left">
                <a class="bui-btn" onclick="bui.back();"><i class="icon-back"></i></a>
            </div>
            <div class="bui-bar-main">小区收支公示</div>
            <div class="bui-bar-right">
                <a class="bui-btn" href="javascript:void(0)"><i class="icon-refresh"></i></a>
            </div>
        </header>
        <!-- 主内容区域 -->
        <main>
            <!-- 日期选择区域 -->
            <div class="bui-panel">
                <div class="bui-panel-head">
                    <span>选择日期区间</span>
                    <span class="date-range">03/29-04/05</span>
                </div>
            </div>
            
            <!-- 收支总览卡片 -->
            <div class="bui-panel bui-box-vertical">
                <div class="panel-title">阳光下晒账单</div>
                <div class="panel-subtitle">南湖碧园小区收支公示</div>
                <div class="amount-total">
                    <div class="label">账户结余</div>
                    <div class="value">¥3111040.88</div>
                </div>
                <div class="bui-box amount-detail">
                    <div class="span1">
                        <div class="label">入账金额</div>
                        <div class="value income">¥4171168.06</div>
                    </div>
                    <div class="span1">
                        <div class="label">出账金额</div>
                        <div class="value expense">¥1060127.18</div>
                    </div>
                </div>
            </div>

            <!-- 收支明细列表 -->
            <div class="bui-panel">
                <div class="bui-panel-head">
                    <span>收支明细</span>
                </div>
                <div class="bui-list">
                    <div class="bui-btn bui-box">
                        <div class="span1">
                            <h3 class="item-title">亲水园C座1单元亲水园</h3>
                            <p class="item-text">C0401</p>
                            <p class="item-text">2024-04-05 09:32:05</p>
                        </div>
                        <div class="span1 text-right">
                            <div class="price-text">收565.27元</div>
                            <div class="payment-method">微信公众号支付</div>
                        </div>
                    </div>
                    <div class="bui-btn bui-box">
                        <div class="span1">
                            <h3 class="item-title">亲水园G座1单元亲水园</h3>
                            <p class="item-text">G0501</p>
                            <p class="item-text">2024-04-05 08:41:53</p>
                        </div>
                        <div class="span1 text-right">
                            <div class="price-text">收673.57元</div>
                            <div class="payment-method">后台扫码枪</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.easybui.com/1.9.30/bui.js"></script>
    <style>
        .bui-panel { margin: 10px; border-radius: 8px; background: #fff; }
        .panel-title { font-size: 18px; font-weight: bold; text-align: center; margin-top: 15px; }
        .panel-subtitle { font-size: 14px; color: #666; text-align: center; margin: 5px 0; }
        .amount-total { text-align: center; padding: 15px 0; }
        .amount-total .label { font-size: 14px; color: #666; }
        .amount-total .value { font-size: 24px; font-weight: bold; color: #333; margin-top: 5px; }
        .amount-detail { padding: 15px; border-top: 1px solid #eee; }
        .amount-detail .label { font-size: 12px; color: #666; }
        .amount-detail .value { font-size: 16px; margin-top: 5px; }
        .amount-detail .income { color: #07c160; }
        .amount-detail .expense { color: #ff6b6b; }
        .date-range { float: right; color: #666; }
        .bui-list .bui-btn { border-bottom: 1px solid #eee; }
        .item-title { font-size: 15px; color: #333; margin-bottom: 5px; }
        .item-text { font-size: 12px; color: #999; margin: 2px 0; }
        .price-text { font-size: 16px; color: #07c160; margin-bottom: 5px; }
        .payment-method { font-size: 12px; color: #999; }
    </style>
    <script>
        bui.ready(function() {
            // 初始化页面
            bui.init({
                id: "#page"
            })
        })
    </script>
</body>
</html>
