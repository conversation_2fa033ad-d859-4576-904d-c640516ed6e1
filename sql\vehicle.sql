 -- 车辆信息表
CREATE TABLE `eh_vehicle` (
  `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
  `plate_no` varchar(20) NOT NULL COMMENT '车牌号',
  `vehicle_type` char(1) NOT NULL COMMENT '车辆类型(1:业主车辆 2:公共车辆)',
  `vehicle_brand` varchar(50) DEFAULT NULL COMMENT '车辆品牌',
  `vehicle_model` varchar(50) DEFAULT NULL COMMENT '车辆型号', 
  `owner_count` int DEFAULT '0' COMMENT '绑定业主数',
  `check_status` char(1) DEFAULT '0' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`vehicle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';

-- 车辆业主关联表
CREATE TABLE `eh_vehicle_owner_rel` (
  `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
  `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认(0:否 1:是)',
  `check_status` char(1) DEFAULT '0' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆业主关联表';