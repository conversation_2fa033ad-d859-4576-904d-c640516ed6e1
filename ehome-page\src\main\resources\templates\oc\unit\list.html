<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('单元列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" name="buildingId" th:value="${buildingId}"/>
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>单元名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/unit";
        var buildingId = [[${buildingId}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add/" + buildingId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "单元",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'unitId',
                    title: '单元ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '单元名称'
                },
                {
                    field: 'buildingName',
                    title: '所属楼栋',
                    visible: false
                },
                {
                    field: 'houseCount',
                    title: '房屋数量'
                },
                {
                    field: 'houseArea',
                    title: '建筑面积'
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.unitId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.unitId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html> 