const app = getApp()

Page({
  data: {
    userInfo: {},
    communityInfo:{},
    ownerInfo:{},
    showBindPhonePopup: false
  },

  onLoad() {
    if (!this.checkLoginStatus()) return
    this.loadUserInfo()
  },

  onShow() {
    if (!this.checkLoginStatus()) return
    this.loadUserInfo()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('wxUserInfo') || {}
    const ownerInfo = wx.getStorageSync('ownerInfo') || {};
    const communityInfo = wx.getStorageSync('communityInfo') || {}
    this.setData({
      userInfo:userInfo,
      ownerInfo:ownerInfo,
      communityInfo:communityInfo
    })
  },
  updatePhone(){

  },
  bindPhone(){

  },
  // 阻止事件冒泡
  stopPropagation() {
    return
  }
})