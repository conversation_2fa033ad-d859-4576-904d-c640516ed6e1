const app = getApp()

Page({
  data: {
    communityList: [],
    communityIndex: 0,
    selectedCommunity: null,
    building: '',
    unit: '',
    room: '',
    ownerName: '',
    ownerPhone: '',
    idCard: '',
    canSubmit: false
  },

  onLoad() {
    if (!this.checkLoginStatus()) return
    this.loadCommunityList()
    // 如果有用户信息，自动填充姓名和手机号
    const userInfo = wx.getStorageSync('wxUserInfo')
    if (userInfo) {
      this.setData({
        ownerName: userInfo.nickName || '',
        ownerPhone: userInfo.phoneNumber || ''
      })
    }
  },

  onShow() {
    if (!this.checkLoginStatus()) return
  },

  // 加载小区列表
  loadCommunityList() {
    app.request({
      url: '/api/wx/community/list',
      method: 'GET'
    }).then(res => {
      console.log('获取小区列表响应:', res)
      if (res.code === 0) {
        console.log('设置小区列表数据:', res.data)
        this.setData({
          communityList: res.data || [],
          selectedCommunity: res.data && res.data.length > 0 ? res.data[0] : null,
          communityIndex: 0
        }, () => {
          console.log('设置后的数据:', this.data.communityList, this.data.selectedCommunity)
        })
      } else {
        wx.showToast({
          title: res.msg || '获取小区列表失败',
          icon: 'none'
        })
      }
    }).catch(error => {
      console.error('获取小区列表失败:', error)
      wx.showToast({
        title: '获取小区列表失败',
        icon: 'none'
      })
    })
  },

  // 小区选择改变
  onCommunityChange(e) {
    const index = e.detail.value
    this.setData({
      communityIndex: index,
      selectedCommunity: this.data.communityList[index]
    })
  },

  // 输入事件处理
  onBuildingInput(e) {
    this.setData({ building: e.detail.value }, this.checkForm)
  },

  onUnitInput(e) {
    this.setData({ unit: e.detail.value }, this.checkForm)
  },

  onRoomInput(e) {
    this.setData({ room: e.detail.value }, this.checkForm)
  },

  onOwnerNameInput(e) {
    this.setData({ ownerName: e.detail.value }, this.checkForm)
  },

  onOwnerPhoneInput(e) {
    this.setData({ ownerPhone: e.detail.value }, this.checkForm)
  },

  onIdCardInput(e) {
    this.setData({ idCard: e.detail.value }, this.checkForm)
  },

  // 检查表单是否可提交
  checkForm() {
    const { building, unit, room, ownerName, ownerPhone, idCard } = this.data
    const canSubmit = building && unit && room && ownerName && ownerPhone && idCard
    this.setData({ canSubmit })
  },

  // 验证手机号
  validatePhone(phone) {
    return /^1[3-9]\d{9}$/.test(phone)
  },

  // 验证身份证号
  validateIdCard(idCard) {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
  },

  // 提交表单
  async submitForm() {
    if (!this.data.canSubmit) {
      return
    }

    if (!this.data.selectedCommunity) {
      wx.showToast({
        title: '请选择所属小区',
        icon: 'none'
      })
      return
    }

    try {
      // 获取当前用户ID
      const userInfo = wx.getStorageSync('wxUserInfo')
      if (!userInfo || !userInfo.userId) {
        throw new Error('用户未登录')
      }

      const houseData = {
        userId: userInfo.userId,
        communityId: this.data.selectedCommunity.ocId,
        communityName: this.data.selectedCommunity.ocName,
        building: this.data.building,
        unit: this.data.unit,
        room: this.data.room,
        ownerName: this.data.ownerName,
        ownerPhone: this.data.ownerPhone,
        idCard: this.data.idCard,
        status: '0',
        isDefault: 0
      }

      const res = await app.request({
        url: '/api/wx/house/add',
        method: 'POST',
        data: houseData
      })

      if (res.code === 0) {
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        if (prevPage) {
          prevPage.data.needRefresh = true
        }

        wx.showToast({
          title: '添加成功',
          icon: 'success',
          complete: () => {
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        })
      } else {
        wx.showToast({
          title: res.msg || '添加失败',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('添加房屋失败:', error)
      wx.showToast({
        title: error.message || '添加失败',
        icon: 'none',
        duration: 2000
      })
    }
  }
})