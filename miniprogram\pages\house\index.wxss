/* 房屋页面 */
page {
  height: 100vh;
  background: #f7f8fa;
}

.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 24rpx;
  box-sizing: border-box;
}

.house-list {
  margin-bottom: 32rpx;
}

.house-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.house-info {
  margin-bottom: 24rpx;
}

.house-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.house-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.house-status-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.house-status {
  display: inline-block;
  font-size: 24rpx;
  color: #999;
}

.house-status.default {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.auth-status {
  display: inline-block;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.auth-status.pending {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

.auth-status.success {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

.auth-status.reject {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.house-actions {
  display: flex;
  gap: 24rpx;
  border-top: 2rpx solid #f5f5f5;
  padding-top: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.action-btn.edit {
  color: #1890ff;
}

.action-btn.delete {
  color: #ff4d4f;
}

.action-btn.set-default {
  color: #07c160;
}

.add-house {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 88rpx;
  background: #fff;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #07c160;
  margin-bottom: 32rpx;
}

.tips {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.tips-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-content text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}

/* 图标字体 */
.icon-edit:before { content: "\e649"; }
.icon-right:before { content: "\e612"; }
.icon-delete:before { content: "\e6b4"; }
.icon-default:before { content: "\e6e1"; }
.icon-add:before { content: "\e6da"; } 