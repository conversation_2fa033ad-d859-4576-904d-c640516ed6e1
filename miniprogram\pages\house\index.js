import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    houseList: [],
    loading: false,
    isLogin: false
  },

  onLoad() {
    this.initializePage()
  },

  onShow() {
    // 设置tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 2 })
    }
    
    this.refreshPageData()
  },

  // 初始化页面
  async initializePage() {
    this.refreshPageData()
    await this.loadHouseList()
  },

  // 刷新页面数据
  refreshPageData() {
    this.refreshPageState()
  },

  // 获取房屋列表
  async loadHouseList() {
    if (!this.checkLoginStatus()) return
    
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: '/api/wx/house/list',
        method: 'GET'
      })

      if (res.code === 0) {
        const list = this.processHouseList(res.data.list || [])
        this.setData({ houseList: list })
      } else {
        throw new Error(res.msg || '获取房屋列表失败')
      }
    } catch (error) {
      // 如果是登录过期错误，不显示错误提示，让全局处理
      if (error.message && error.message.includes('登录')) {
        return
      }
      
      handleError(error, '获取房屋列表')
      this.setData({ houseList: [] })
    } finally {
      loadingManager.hide()
    }
  },

  // 处理房屋列表数据
  processHouseList(list) {
    return list.map(item => ({
      ...item,
      id: item.houseId || item.id, // 优先使用houseId
      statusText: this.getStatusText(item.status),
      isDefaultText: item.isDefault === 1 ? '默认房屋' : ''
    }))
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待审核',
      1: '已认证',
      2: '已拒绝'
    }
    return statusMap[status] || '未知状态'
  },

  // 邀请住户（暂时显示提示）
  addVisit() {
    wx.showModal({
      title: '功能开发中',
      content: '邀请住户功能正在开发中，敬请期待',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  // 添加新房屋
  addHouse() {
    if (!this.checkLoginStatus()) return
    
    if (this.data.houseList.length >= 5) {
      wx.showToast({
        title: '最多只能添加5套房屋',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/house/add'
    })
  },

  // 查看房屋详情
  editHouse(e) {
    if (!this.checkLoginStatus()) return
    
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/house/edit?id=${id}`
    })
  },

  // 设为默认房屋
  async setDefault(e) {
    if (!this.checkLoginStatus()) return
    
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }

    try {
      loadingManager.show('设置中...')
      
      const res = await app.request({
        url: '/api/wx/house/set-default',
        method: 'POST',
        data: { id }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })
        
        // 刷新列表
        await this.loadHouseList()
      } else {
        throw new Error(res.msg || '设置失败')
      }
    } catch (error) {
      handleError(error, '设置默认房屋')
    } finally {
      loadingManager.hide()
    }
  },

  // 删除房屋
  deleteHouse(e) {
    if (!this.checkLoginStatus()) return
    
    const id = e.currentTarget.dataset.id
    const name = e.currentTarget.dataset.name
    
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除房屋"${name}"吗？`,
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteHouse(id)
        }
      }
    })
  },

  // 执行删除房屋
  async performDeleteHouse(id) {
    try {
      loadingManager.show('删除中...')
      
      const res = await app.request({
        url: '/api/wx/house/delete',
        method: 'POST',
        data: { id }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        // 刷新列表
        await this.loadHouseList()
      } else {
        throw new Error(res.msg || '删除失败')
      }
    } catch (error) {
      handleError(error, '删除房屋')
    } finally {
      loadingManager.hide()
    }
  }
})