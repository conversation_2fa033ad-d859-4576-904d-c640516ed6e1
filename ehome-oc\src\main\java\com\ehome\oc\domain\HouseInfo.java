package com.ehome.oc.domain;

import com.ehome.common.core.domain.BaseEntity;
import java.math.BigDecimal;

public class HouseInfo extends BaseEntity {
    private Long houseId;
    
    /** 用户ID */
    private Long wxUserId;
    
    /** 小区ID */
    private String communityId;
    
    /** 业主ID */
    private Integer ownerId;
    
    /** 小区名称 */
    private String communityName;

    /** 楼栋ID */
    private String buildingId;
    
    /** 楼栋号 */
    private String building;
    
    /** 单元号 */
    private String unit;
    
    /** 房间号 */
    private String room;

    private String combinaName;

    /** 房屋面积 */
    private BigDecimal area;
    
    /** 业主姓名 */
    private String ownerName;
    
    /** 联系电话 */
    private String ownerPhone;
    
    /** 身份证号 */
    private String idCard;

    private  String houseStatus;

    private String checkStatus;
    
    /** 审核状态（0未审核 1已审核 2审核不通过） */
    private Integer status;
    
    /** 是否默认（0否 1是） */
    private Integer isDefault;
    
    /** 审核备注 */
    private String remark;

    public Long getHouseId() {
        return houseId;
    }

    public void setHouseId(Long houseId) {
        this.houseId = houseId;
    }

    public Long getWxUserId() {
        return wxUserId;
    }

    public void setWxUserId(Long wxUserId) {
        this.wxUserId = wxUserId;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(String buildingId) {
        this.buildingId = buildingId;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerPhone() {
        return ownerPhone;
    }

    public void setOwnerPhone(String ownerPhone) {
        this.ownerPhone = ownerPhone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }


    public String getCombinaName() {
        return combinaName;
    }

    public void setCombinaName(String combinaName) {
        this.combinaName = combinaName;
    }

    public String getHouseStatus() {
        return houseStatus;
    }

    public void setHouseStatus(String houseStatus) {
        this.houseStatus = houseStatus;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }
}