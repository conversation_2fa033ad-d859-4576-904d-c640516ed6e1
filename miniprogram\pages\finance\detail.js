Page({
  data: {
    detail: null
  },

  async onLoad(options) {
    if (!this.checkLoginStatus()) return
    const { id } = options
    await this.getFinanceDetail(id)
  },

  onShow() {
    if (!this.checkLoginStatus()) return
  },

  // 获取收支详情
  async getFinanceDetail(id) {
    try {
      const res = await getApp().request({
        url: '/api/wx/ccb/detail',
        data: { id }
      })
      if (res.code === 0 && res.data) {
        this.setData({
          detail: res.data
        })
      } else {
        wx.showToast({
          title: '数据格式异常',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      })
      console.error('获取收支详情失败', error)
    }
  },

  // 查看附件
  viewAttachment(e) {
    const url = e.currentTarget.dataset.url
    wx.downloadFile({
      url: url,
      success: (res) => {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          success: function (res) {
            console.log('打开文档成功')
          }
        })
      }
    })
  }
})