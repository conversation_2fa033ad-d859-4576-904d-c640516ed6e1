<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
    <title>智慧小区</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/buijs/lib/latest/bui.css">
    <style>
        .banner-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .grid-container {
            padding: 15px;
        }
        .grid-item {
            text-align: center;
            padding: 10px;
        }
        .grid-item i {
            font-size: 28px;
            color: #666;
        }
        .grid-item span {
            display: block;
            margin-top: 5px;
            font-size: 14px;
            color: #333;
        }
        .subscribe-section {
            padding: 15px;
            margin-top: 20px;
            background: #f5f5f5;
        }
        .subscribe-section .title {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .subscribe-btn {
            display: inline-block;
            padding: 8px 20px;
            background: #ff6b6b;
            color: #fff;
            border-radius: 20px;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 顶部banner -->
    <div class="banner">
        <img src="/img/community-banner.jpg" alt="社区全景" class="banner-img">
    </div>

    <!-- 功能网格 -->
    <div class="grid-container bui-box-space">
        <div class="bui-box">
            <div class="span1 grid-item">
                <i class="icon-home"></i>
                <span>我的房屋</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-pay"></i>
                <span>生活缴费</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-service"></i>
                <span>便民热线</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-repair"></i>
                <span>户内报修</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-notice"></i>
                <span>公区报事</span>
            </div>
        </div>

        <div class="bui-box">
            <div class="span1 grid-item">
                <i class="icon-decoration"></i>
                <span>装修管理</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-parking"></i>
                <span>车位续租</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-praise"></i>
                <span>物业表扬</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-suggest"></i>
                <span>投诉建议</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-pass"></i>
                <span>放行条</span>
            </div>
        </div>

        <div class="bui-box">
            <div class="span1 grid-item">
                <i class="icon-visitor"></i>
                <span>访客登记</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-door"></i>
                <span>智慧门禁</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-parking-fee"></i>
                <span>临停收费</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-lost"></i>
                <span>失物招领</span>
            </div>
            <div class="span1 grid-item">
                <i class="icon-more"></i>
                <span>其他服务</span>
            </div>
        </div>
    </div>

    <!-- 底部关注区域 -->
    <div class="subscribe-section">
        <div class="title">关注即享贴心服务</div>
        <p>扫码关注，重要消息不遗漏</p>
        <a href="javascript:;" class="subscribe-btn">立即关注</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/buijs/lib/latest/bui.js"></script>
    <script>
        bui.ready(function() {
            // 初始化页面
            var uiPage = bui.page({
                id: "page",
                autoInit: true
            });

            // 绑定点击事件
            bui.on(".grid-item", "click", function() {
                var text = $(this).find("span").text();
                bui.hint("点击了" + text);
            });
        });
    </script>
</body>
</html>
