<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('住户详情')" />
    <style>
        .container-div {
            background: #f0f2f5;
            padding: 15px;
        }

        .container-div .row{
            height: initial!important;
        }

        .ibox {
            margin-bottom: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,.1);
        }
        .ibox-title {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background-color: #f8f8f8;
            border-radius: 4px 4px 0 0;
        }
        .ibox-content {
            padding: 20px;
            border-style: none;
        }

        .form-group {
            margin-bottom: 15px;
        }
        .control-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: right;
        }
        .form-control-static {
            padding-top: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        .btn-group {
            margin-bottom: 20px;
        }
        .tab-content {
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .nav-tabs {
            margin-bottom: 0;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 3px;
        }
        .badge-primary {
            background-color: #1890ff;
        }
        .badge-default {
            background-color: #d9d9d9;
            color: #666;
        }
        .btn-xs {
            padding: 2px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
        .operation-btn {
            margin-bottom: 10px;
        }

        #ownerInfo{
            padding: 10px 0px 0px 0px;
        }

    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <!-- 业主基本信息 -->
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>业主信息</h5>
                        <div class="btn-group pull-right" style="margin-top: -5px;">
                            <button type="button" class="btn btn-xs btn-default" onclick="pageBack()">
                                <i class="fa fa-reply"></i> 返回
                            </button>
                            <button type="button" class="btn btn-xs btn-primary" onclick="editOwner()">
                                <i class="fa fa-edit"></i> 修改
                            </button>
                        </div>
                    </div>
                    <div class="ibox-content" id="ownerInfo">
                        <div class="row">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">业主ID：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="owner_id">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">姓名：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="owner_name">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">性别：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="gender_text">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">手机号：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="mobile">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">绑定房屋：</label>
                                    <div class="col-sm-8">
                                        <p class="form-control-static" id="house_info">-</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">备注：</label>
                                    <div class="col-sm-10">
                                        <p class="form-control-static" id="remark">-</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>详细信息</h5>
                    </div>
                    <div class="ibox-content p-0">
                            <div class="operation-btn">
                                <button type="button" class="btn btn-primary btn-sm" onclick="bindHouse()">
                                    <i class="fa fa-plus"></i> 绑定房屋
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>楼栋/单元</th>
                                            <th>房间号</th>
                                            <th>建筑面积</th>
                                            <th>是否默认</th>
                                            <th>关系类型</th>
                                            <th>审核状态</th>
                                            <th>房屋状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="houseListBody">
                                        <tr>
                                            <td colspan="8" class="text-center">暂无房屋信息</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>操作记录</h5>
                    </div>
                    <div class="ibox-content p-0">
                        <!-- 车辆信息标签页 -->
                        <div class="tab-pane" id="tab-vehicle">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>车辆品牌</th>
                                        <th>车辆型号</th>
                                        <th>是否默认</th>
                                        <th>审核状态</th>
                                    </tr>
                                    </thead>
                                    <tbody id="vehicleListBody">
                                    <tr>
                                        <td colspan="5" class="text-center">暂无车辆信息</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择房屋弹窗 -->
    <div id="selectHouseModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="selectHouseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="selectHouseModalLabel">选择房屋</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="houseSearchInput" style="width: 150px;display: inline-block;" class="form-control" placeholder="输入房屋编号">
                                    <span class="input-group-btn" style="display: inline-block;">
                                        <button type="button" class="btn btn-primary" onclick="searchHouse()">
                                            <i class="fa fa-search"></i> 查询
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="resetHouseSearch()">
                                            <i class="fa fa-refresh"></i> 重置
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="house-table" data-mobile-responsive="true"></table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 绑定房屋弹窗 -->
    <div id="bindHouseModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="bindHouseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="bindHouseModalLabel">绑定房屋</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" id="bindHouseForm">
                        <input type="hidden" id="bindHouseId" name="houseId">
                        <input type="hidden" id="bindOwnerId" name="ownerId">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">房屋信息：</label>
                            <div class="col-sm-8">
                                <input type="text" id="bindHouseInfo" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">关系类型：</label>
                            <div class="col-sm-8">
                                <select id="relType" name="relType" class="form-control">
                                    <option value="1">业主</option>
                                    <option value="2">家庭成员</option>
                                    <option value="3">租户</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">是否默认：</label>
                            <div class="col-sm-8">
                                <label class="toggle-switch switch-solid">
                                    <input type="checkbox" id="isDefault" name="isDefault" checked>
                                    <span></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">备注：</label>
                            <div class="col-sm-8">
                                <textarea id="bindRemark" name="remark" class="form-control"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitBindHouse()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/owner";
        var housePrefix = ctx + "oc/house";
        var ownerId = "";

        $(function() {
            // 从URL中获取业主ID
            var pathArray = window.location.pathname.split('/');
            ownerId = pathArray[pathArray.length - 1];
            
            // 页面加载完成后执行
            loadOwnerInfo();
            loadHouseList();
            loadVehicleList();
        });

        /* 加载业主信息 */
        function loadOwnerInfo() {
            $.ajax({
                url: prefix + "/record",
                type: "post",
                data: { owner_id: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var owner = res.data;
                        // 使用fillRecord填充业主信息
                        fillRecord(owner, "", ",", "#ownerInfo");
                        
                        // 处理性别显示
                        var genderText = owner.gender == 'M' ? '男' : '女';
                        $("#gender_text").text(genderText);
                    } else {
                        $.modal.alertError("加载业主信息失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载业主信息失败");
                }
            });
        }
        
        /* 加载房屋列表 */
        function loadHouseList() {
            $.ajax({
                url: prefix + "/houseList",
                type: "post",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var houses = res.rows;
                        var html = '';
                        if (houses && houses.length > 0) {
                            for (var i = 0; i < houses.length; i++) {
                                var house = houses[i];
                                html += '<tr>';
                                html += '<td>' + (house.building_name || '-') + '/' + (house.unit_name || '-') + '</td>';
                                html += '<td>' + (house.room || '-') + '</td>';
                                html += '<td>' + (house.total_area || '0') + '㎡</td>';
                                html += '<td>' + (house.is_default == 1 ? '<span class="badge badge-primary">默认</span>' : '<span class="badge badge-default">-</span>') + '</td>';
                                html += '<td>' + (house.rel_type == 1 ? '业主' : (house.rel_type == 2 ? '家庭成员' : (house.rel_type == 3 ? '租户' : '-'))) + '</td>';
                                html += '<td>' + (house.check_status == 0 ? '未审核' : (house.check_status == 1 ? '已审核' : (house.check_status == 2 ? '审核不通过' : '-'))) + '</td>';
                                html += '<td>' + (house.house_status || '-') + '</td>';
                                html += '<td>';
                                html += '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="unbindHouse(\'' + house.rel_id + '\')"><i class="fa fa-unlink"></i> 解绑</a> ';
                                if (house.is_default != 1) {
                                    html += '<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="setDefault(\'' + house.rel_id + '\', \'' + house.house_id + '\')"><i class="fa fa-check"></i> 设为默认</a>';
                                }
                                html += '</td>';
                                html += '</tr>';
                            }
                        } else {
                            html = '<tr><td colspan="8" class="text-center">暂无房屋信息</td></tr>';
                        }
                        $('#houseListBody').html(html);
                    }
                }
            });
        }
        
        /* 加载车辆列表 */
        function loadVehicleList() {
            $.ajax({
                url: prefix + "/vehicleList",
                type: "post",
                data: { ownerId: ownerId },
                success: function(res) {
                    if (res.code == 0) {
                        var vehicles = res.rows;
                        var html = '';
                        if (vehicles && vehicles.length > 0) {
                            for (var i = 0; i < vehicles.length; i++) {
                                var vehicle = vehicles[i];
                                html += '<tr>';
                                html += '<td>' + (vehicle.plate_no || '-') + '</td>';
                                html += '<td>' + (vehicle.vehicle_brand || '-') + '</td>';
                                html += '<td>' + (vehicle.vehicle_model || '-') + '</td>';
                                html += '<td>' + (vehicle.is_default == 1 ? '<span class="badge badge-primary">默认</span>' : '<span class="badge badge-default">-</span>') + '</td>';
                                html += '<td>' + (vehicle.check_status == '0' ? '未审核' : (vehicle.check_status == '1' ? '已审核' : (vehicle.check_status == '2' ? '审核不通过' : '-'))) + '</td>';
                                html += '</tr>';
                            }
                        } else {
                            html = '<tr><td colspan="5" class="text-center">暂无车辆信息</td></tr>';
                        }
                        $('#vehicleListBody').html(html);
                    }
                }
            });
        }

        /* 编辑业主 */
        function editOwner() {
            var url = prefix + '/edit/' + ownerId;
            $.modal.open("修改业主", url);
        }
        
        /* 绑定房屋 */
        function bindHouse() {
            // 初始化房屋选择表格
            initHouseSelectTable();
            // 显示选择房屋弹窗
            $("#selectHouseModal").modal("show");
        }
        
        /* 初始化房屋选择表格 */
        function initHouseSelectTable() {
            var options = {
                id: "house-table",
                url: housePrefix + "/list",
                method: "post",
                modalName: "房屋",
                pagination: true,
                pageSize: 10,
                pageList: [10, 20, 50],
                sidePagination: "server",
                queryParams: function(params) {
                    var search = {};
                    search.pageSize = params.limit;
                    search.pageNum = params.offset / params.limit + 1;
                    search.room = $("#houseSearchInput").val();
                    return search;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'building_name',
                    title: '楼栋'
                },
                {
                    field: 'unit_name',
                    title: '单元'
                },
                {
                    field: 'room',
                    title: '房间号'
                },
                {
                    field: 'total_area',
                    title: '建筑面积',
                    formatter: function(value) {
                        return value + '㎡';
                    }
                },
                {
                    field: 'house_status',
                    title: '房屋状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return '<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="selectHouse(\'' + row.house_id + '\', \'' + (row.building_name || '-') + '/' + (row.unit_name || '-') + '/' + row.room + '\')"><i class="fa fa-check"></i>选择</a>';
                    }
                }]
            };
            $.table.init(options);
        }
        
        /* 搜索房屋 */
        function searchHouse() {
            $("#house-table").bootstrapTable('refresh');
        }
        
        /* 重置房屋搜索 */
        function resetHouseSearch() {
            $("#houseSearchInput").val("");
            $("#house-table").bootstrapTable('refresh');
        }
        
        /* 选择房屋 */
        function selectHouse(houseId, houseInfo) {
            // 关闭选择房屋弹窗
            $("#selectHouseModal").modal("hide");
            
            // 填充绑定表单
            $("#bindHouseId").val(houseId);
            $("#bindOwnerId").val(ownerId);
            $("#bindHouseInfo").val(houseInfo);
            
            // 显示绑定弹窗
            $("#bindHouseModal").modal("show");
        }
        
        /* 提交绑定房屋 */
        function submitBindHouse() {
            var data = {
                houseId: $("#bindHouseId").val(),
                ownerId: $("#bindOwnerId").val(),
                relType: $("#relType").val(),
                isDefault: $("#isDefault").is(":checked") ? 1 : 0,
                remark: $("#bindRemark").val()
            };
            
            $.ajax({
                url: housePrefix + "/bindOwner",
                type: "post",
                data: data,
                success: function(res) {
                    if (res.code == 0) {
                        $.modal.msgSuccess("绑定成功");
                        $("#bindHouseModal").modal("hide");
                        // 刷新房屋列表
                        loadHouseList();
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }
        
        /* 解绑房屋 */
        function unbindHouse(relId) {
            $.modal.confirm("确定解绑该房屋吗？", function() {
                $.ajax({
                    url: housePrefix + "/unbindOwner",
                    type: "post",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("解绑成功");
                            // 刷新房屋列表
                            loadHouseList();
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }
        
        /* 设置默认房屋 */
        function setDefault(relId, houseId) {
            $.modal.confirm("确定将该房屋设为默认房屋吗？", function() {
                $.ajax({
                    url: housePrefix + "/setDefaultOwner",
                    type: "post",
                    data: { relId: relId, houseId: houseId },
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.msgSuccess("设置成功");
                            // 刷新房屋列表
                            loadHouseList();
                        } else {
                            $.modal.alertError("设置失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("设置失败");
                    }
                });
            });
        }

        pageBack = function(){
            $.modal.close();
            $.modal.closeTab();
        }
        
    </script>
</body>
</html> 