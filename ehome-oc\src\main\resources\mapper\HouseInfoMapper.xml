<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.HouseInfoMapper">

    <resultMap type="com.ehome.oc.domain.HouseInfo" id="HouseInfoResult">
        <id     property="houseId"      column="house_id"/>
        <result property="wxUserId"     column="wx_user_id"/>
        <result property="communityId"  column="community_id"/>
        <result property="ownerId"      column="owner_id"/>
        <result property="communityName" column="community_name"/>
        <result property="buildingId"   column="building_id"/>
        <result property="building"     column="building"/>
        <result property="unit"         column="unit"/>
        <result property="room"         column="room"/>
        <result property="area"         column="area"/>
        <result property="ownerName"    column="owner_name"/>
        <result property="ownerPhone"   column="owner_phone"/>
        <result property="idCard"       column="id_card"/>
        <result property="status"       column="status"/>
        <result property="isDefault"    column="is_default"/>
        <result property="remark"       column="remark"/>
        <result property="createTime"   column="create_time"/>
        <result property="updateTime"   column="update_time"/>
    </resultMap>

    <sql id="selectHouseVo">
        select house_id, wx_user_id, community_id, owner_id, community_name, building_id, building, unit, room, 
        area, owner_name, owner_phone, id_card, status, is_default, remark, create_time, update_time
        from eh_house_info
    </sql>

    <select id="selectHouseList" parameterType="Long" resultMap="HouseInfoResult">
        <include refid="selectHouseVo"/>
        where wx_user_id = #{wxUserId}
        order by is_default desc, create_time desc
    </select>

    <select id="selectHouseById" parameterType="Long" resultMap="HouseInfoResult">
        <include refid="selectHouseVo"/>
        where house_id = #{houseId}
    </select>

    <select id="selectHouseCount" parameterType="Long" resultType="Integer">
        select count(*) from eh_house_info where wx_user_id = #{wxUserId}
    </select>

    <insert id="insertHouse" parameterType="com.ehome.oc.domain.HouseInfo" useGeneratedKeys="true" keyProperty="houseId">
        insert into eh_house_info (
            wx_user_id, community_id, owner_id, community_name, building_id, building, unit, room, area,
            owner_name, owner_phone, id_card, status, is_default, remark, create_time
        ) values (
            #{wxUserId}, #{communityId}, #{ownerId}, #{communityName}, #{buildingId}, #{building}, #{unit}, #{room}, #{area},
            #{ownerName}, #{ownerPhone}, #{idCard}, #{status}, #{isDefault}, #{remark}, sysdate()
        )
    </insert>

    <update id="updateHouse" parameterType="com.ehome.oc.domain.HouseInfo">
        update eh_house_info
        <set>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="building != null">building = #{building},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="room != null">room = #{room},</if>
            <if test="area != null">area = #{area},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="ownerPhone != null">owner_phone = #{ownerPhone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where house_id = #{houseId}
    </update>

    <delete id="deleteHouseById" parameterType="Long">
        delete from eh_house_info where house_id = #{houseId}
    </delete>

    <update id="resetDefaultHouse" parameterType="Long">
        update eh_house_info set is_default = 0 where wx_user_id = #{wxUserId}
    </update>

    <update id="setDefaultHouse" parameterType="Long">
        update eh_house_info set is_default = 1 where house_id = #{houseId}
    </update>

    <select id="countHouseByUserId" parameterType="Long" resultType="Integer">
        select count(1) from eh_house_info where wx_user_id = #{wxUserId}
    </select>

</mapper> 