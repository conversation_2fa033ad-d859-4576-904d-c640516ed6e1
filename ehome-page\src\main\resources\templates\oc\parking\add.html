<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增车位')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-parking-add">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">车位号：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="parking_no" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">车位类型：</label>
                <div class="col-sm-8">
                    <select name="parking_type" class="form-control" required>
                        <option value="">请选择车位类型</option>
                        <option value="1">私人车位</option>
                        <option value="2">子母车位</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">车位状态：</label>
                <div class="col-sm-8">
                    <select name="parking_status" class="form-control" required>
                        <option value="">请选择车位状态</option>
                        <option value="1">出售</option>
                        <option value="2">出租</option>
                        <option value="3">自用</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">审核状态：</label>
                <div class="col-sm-8">
                    <select name="check_status" class="form-control">
                        <option value="0">未审核</option>
                        <option value="1">已审核</option>
                        <option value="2">审核不通过</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/parking";
        
        $("#form-parking-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-parking-add').serialize());
            }
        }
    </script>
</body>
</html> 