package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.service.IBuildingService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 楼栋信息Controller
 */
@Controller
@RequestMapping("/oc/building")
public class BuildingController extends BaseController {

    private static final String PREFIX = "oc/building";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @Autowired
    private IBuildingService buildingService;

    @GetMapping("/tree")
    @ResponseBody
    public AjaxResult getBuildingUnitTree() {
        try {
            String communityId = getSysUser().getCommunityId();
            List<Map<String, Object>> tree = buildingService.getBuildingUnitTree(communityId);
            return AjaxResult.success("获取楼栋单元树成功", tree);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return AjaxResult.error("获取楼栋单元树失败: " + e.getMessage());
        }
    }

    @GetMapping("/edit/{buildingId}")
    public String edit(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        Record building = Db.findFirst("select * from eh_building where building_id = ?", buildingId);
        mmap.put("building", building.toMap());
        return PREFIX + "/edit";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String buildingId = params.getString("building_id");
        if (StringUtils.isEmpty(buildingId)) {
            return AjaxResult.error("楼栋ID不能为空");
        }
        Record building = Db.findFirst("select * from eh_building where building_id = ?", buildingId);
        return AjaxResult.success(null, building.toMap());
    }

    @Log(title = "新增楼栋", businessType = BusinessType.INSERT)
    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        JSONObject params = getParams();
        Record building = new Record();
        building.setColumns(params);
        building.set("building_id", Seq.getId());
        setCreateAndUpdateInfo(building);
        Db.save("eh_building","building_id",building);
        return AjaxResult.success();
    }

    @Log(title = "修改楼栋", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record building = new Record();
        building.setColumns(params);
        setUpdateInfo(building);
        return toAjax(Db.update("eh_building","building_id",building));
    }

    @Log(title = "删除楼栋", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById("eh_building","building_id",id);
        }
        return success();
    }

    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String name = params.getString("name");
        String communityId = getSysUser().getCommunityId();
        String buildingId = params.getString("building_id");
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(communityId)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_building where 1=1");
        sql.append(name, "and name = ?");
        sql.append(buildingId, "and building_id != ?");
        sql.append(communityId, "and community_id = ?");
        Record building = Db.findFirst(sql.getSQL(), sql.getParams());
        return building == null;
    }

    @PostMapping("/queryCommunity")
    @ResponseBody
    public AjaxResult queryCommunity() {
        List<Record> list = Db.find("select * from eh_community where pms_id = ?", getSysUser().getPmsId());
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("oc_id"), record.getStr("oc_name"));
        });
        return AjaxResult.success(map);
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_building t1");
        sql.append("where 1=1");
        
        sql.append(params.getString("community_id"), "and t1.community_id = ?");
        sql.appendLike(params.getString("name"), "and t1.name like ?");
        sql.append(params.getString("total_units"), "and t1.total_units = ?");
        sql.appendLike(params.get("manager"), "and t1.manager like ?");
        
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and t1.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and t1.create_time <= ?");

        sql.append("order by t1.create_time desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
        record.set("community_id", getSysUser().getCommunityId());
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
} 