/* 用户协议页面样式 */
.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 40rpx;
}

.header {
  padding: 40rpx 30rpx;
  background: linear-gradient(to bottom, #f8f8f8, #fff);
  text-align: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.content {
  padding: 0 30rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background: #07c160;
  border-radius: 4rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.item {
  margin-top: 16rpx;
  padding-left: 20rpx;
  position: relative;
}

.item:first-child {
  margin-top: 20rpx;
} 