<!DOCTYPE html>
<html lang="zh">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <th:block th:include="include :: header('编辑账目分类')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-edit">
        <input type="hidden" name="id" id="id" th:value="${id}">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">分类名称：</label>
            <div class="col-sm-8">
                <input name="name" id="name" class="form-control" type="text" required placeholder="分类名称" title="分类名称">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">收支方向：</label>
            <div class="col-sm-8">
                <select name="direction" id="direction" class="form-control" required title="收支方向">
                    <option value="in">收入</option>
                    <option value="out">支出</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">所属小区：</label>
            <div class="col-sm-8">
                <input name="community_id" id="community_id" class="form-control" type="text" placeholder="所属小区" title="所属小区">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">状态：</label>
            <div class="col-sm-8">
                <select name="status" id="status" class="form-control" title="状态">
                    <option value="enabled">启用</option>
                    <option value="disabled">停用</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <input name="remark" id="remark" class="form-control" type="text" placeholder="备注" title="备注">
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/accountType";

    $(function(){
        $('#form-edit').renderForm({url:prefix+'/record'});
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/editSave", $('#form-edit').serialize());
        }
    }
</script>
</body>
</html> 