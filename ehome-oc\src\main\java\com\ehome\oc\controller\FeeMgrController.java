package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/oc/fee")
public class FeeMgrController extends BaseController {

    private static final String PREFIX = "oc/fee";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return AjaxResult.error("收费标准ID不能为空");
        }
        Record fee = Db.findFirst("select * from fee_standard where id = ? and is_deleted = false", id);
        return AjaxResult.success(null, fee.toMap());
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Record fee = Db.findFirst("select * from fee_standard where id = ? and is_deleted = false", id);
        mmap.put("fee", fee.toMap());
        return PREFIX + "/edit";
    }

    @Log(title = "新增收费标准", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        Record fee = new Record();
        fee.setColumns(params);
        setCreateAndUpdateInfo(fee);
        Db.save("fee_standard", "id", fee);
        return AjaxResult.success();
    }

    @Log(title = "修改收费标准", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record fee = new Record();
        fee.setColumns(params);
        setUpdateInfo(fee);
        return toAjax(Db.update("fee_standard", "id", fee));
    }

    @Log(title = "删除收费标准", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Record fee = new Record();
            fee.set("id", id);
            fee.set("is_deleted", true);
            setUpdateInfo(fee);
            Db.update("fee_standard", "id", fee);
        }
        return success();
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from fee_standard where is_deleted = false");
        
        sql.appendLike(params.getString("feeName"), "and fee_name like ?");
        sql.append(params.getString("feeType"), "and fee_type = ?");
        sql.append(params.getString("collectionMethod"), "and collection_method = ?");
        sql.append(params.getString("isActive"), "and is_active = ?");
        
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and created_at >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and created_at <= ?");

        sql.append("order by created_at desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("created_at", now);
        record.set("updated_at", now);
        record.set("created_by", loginName);
        record.set("updated_by", loginName);
        record.set("is_deleted", false);
        record.set("is_active", true);
    }

    private void setUpdateInfo(Record record) {
        record.set("updated_at", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("updated_by", getSysUser().getLoginName());
    }
} 