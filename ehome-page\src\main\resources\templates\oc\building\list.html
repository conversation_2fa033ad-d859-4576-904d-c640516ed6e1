<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('楼栋信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>楼栋序号：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>楼栋管家：</label>
                                <input type="text" name="manager"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
      <script th:inline="javascript">
      
        var prefix = ctx + "oc/building";

        $(function() {
            $('#formId').renderSelect({prefix:prefix});
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "楼栋信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'building_id',
                    title: '楼栋ID',
                    visible: false
                },
                {
                    field: 'name',
                    title: '楼栋名称'
                },
                {
                    field: 'total_units',
                    title: '单元总数'
                },
                {
                    field: 'house_count',
                    title: '房屋数量'
                },
                {
                    field: 'house_area',
                    title: '房屋面积',
                    formatter: function(value, row, index) {
                        if(row.house_area == '0'){
                            return "-";
                        }
                        return row.house_area + "㎡";
                    }
                },
                {
                    field: 'manager',
                    title: '楼栋管家'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewUnits(\'' + row.building_id + '\', \'' + row.name + '\')"><i class="fa fa-edit"></i> 单元</a> ');
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.building_id + '\')"><i class="fa fa-edit"></i> 编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.building_id + '\')"><i class="fa fa-remove"></i> 删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function queryCommunity(obj){
            $.table.search();
        }

        function viewUnits(buildingId, buildingName) {
            var url = ctx + 'oc/unit/list/' + buildingId;
            $.modal.openTab(buildingName + "单元管理", url);
        }
    </script>
</body>
</html> 