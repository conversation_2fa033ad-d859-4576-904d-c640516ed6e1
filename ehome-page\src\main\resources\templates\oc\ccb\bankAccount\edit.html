<!DOCTYPE html>
<html lang="zh">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <th:block th:include="include :: header('编辑银行账户')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-edit">
            <input type="hidden" name="id" id="id" th:value="${bankAccount.id}">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">银行名称：</label>
                <div class="col-sm-8">
                    <select name="bank_type" id="bank_type" class="form-control" required>
                        <option value="CCB">中国建设银行</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">客户号：</label>
                <div class="col-sm-8">
                    <input name="cust_id" id="cust_id" class="form-control" type="text" required placeholder="客户号" title="客户号">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">用户号：</label>
                <div class="col-sm-8">
                    <input name="user_id" id="user_id" class="form-control" type="text" required placeholder="用户号" title="用户号">
                </div>
            </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">登录密码：</label>
            <div class="col-sm-8">
                <input name="password" id="password" class="form-control" type="password" required placeholder="登录密码" title="登录密码">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">账号：</label>
            <div class="col-sm-8">
                <input name="acc_no" id="acc_no" class="form-control" type="text" required placeholder="账号" title="账号">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">语言：</label>
            <div class="col-sm-8">
                <input name="language" id="language" class="form-control" type="text" value="CN" required placeholder="语言" title="语言">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">银行接口地址：</label>
            <div class="col-sm-8">
                <input name="api_url" id="api_url" class="form-control" type="text" required placeholder="如http://ip:port" title="银行接口地址">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">状态：</label>
            <div class="col-sm-8">
                <select name="status" id="status" class="form-control" title="状态">
                    <option value="0">正常</option>
                    <option value="1">停用</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <input name="remark" id="remark" class="form-control" type="text" placeholder="备注" title="备注">
            </div>
        </div>      
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/bankAccount";
 
    $(function(){
        $('#form-edit').renderForm({url:prefix+'/record'});
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/editSave", $('#form-edit').serialize());
        }
    }
</script>
</body>
</html> 