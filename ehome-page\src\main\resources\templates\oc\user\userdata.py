import os
import json

def sql_str(val):
    if val is None:
        return 'NULL'
    if isinstance(val, (int, float)):
        return str(val)
    return "'{}'".format(str(val).replace("'", "''"))

def get_rel_type(house_name):
    # 房屋名称里有“业主”就是 1，否则 2
    if "业主" in house_name:
        return 1
    else:
        return 2

def main():
    base_dir = os.path.dirname(__file__)
    data_file = os.path.join(base_dir, 'ownerdata.txt')
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"未找到 {data_file}，请确认 ownerdata.txt 和 userdata.py 在同一目录下。")
        return

    owner_ids = set()
    owner_sqls = []
    rel_sqls = []

    for line in lines:
        if not line.strip():
            continue
        try:
            obj = json.loads(line)
        except Exception as e:
            print(f"解析JSON失败: {e}\n内容: {line[:100]}")
            continue
        owner_list = obj.get('data', {}).get('list', [])
        for owner in owner_list:
            owner_id = owner.get('id')
            if not owner_id or owner_id in owner_ids:
                continue
            owner_ids.add(owner_id)
            # owner 字段映射
            owner_sql = (
                "INSERT INTO eh_owner (owner_id, owner_name, pms_id, community_id, mobile, id_card, gender, address, face_photo, door_key_count, contract, house_count, member_count, parking_count, car_count, complaint_count, repair_count, arrears_count, remark, is_live, role, create_time, update_time, creator, updater) VALUES ("
                f"{sql_str(owner.get('id'))}, "
                f"{sql_str(owner.get('name', ''))}, "
                f"{sql_str(owner.get('pms_id', ''))}, "
                f"{sql_str(owner.get('communityID', ''))}, "
                f"{sql_str(owner.get('phone', ''))}, "
                f"{sql_str(owner.get('id_card', ''))}, "
                f"{sql_str(owner.get('gender', ''))}, "
                f"{sql_str(owner.get('address', ''))}, "
                f"{sql_str(owner.get('face_photo', ''))}, "
                f"{sql_str(owner.get('door_key_count', 0))}, "
                f"{sql_str(owner.get('contract', ''))}, "
                f"{sql_str(owner.get('house_count', 0))}, "
                f"{sql_str(owner.get('member_count', 0))}, "
                f"{sql_str(owner.get('parking_count', 0))}, "
                f"{sql_str(owner.get('car_count', 0))}, "
                f"{sql_str(owner.get('complaint_count', 0))}, "
                f"{sql_str(owner.get('repair_count', 0))}, "
                f"{sql_str(owner.get('arrears_count', 0))}, "
                f"{sql_str(owner.get('remark', ''))}, "
                f"{sql_str(owner.get('is_live', 0))}, "
                f"{sql_str(owner.get('role', 1))}, "
                f"{sql_str(owner.get('create_time', ''))}, "
                f"{sql_str(owner.get('update_time', ''))}, "
                f"{sql_str(owner.get('creator', ''))}, "
                f"{sql_str(owner.get('updater', ''))}"
                ");"
            )
            owner_sqls.append(owner_sql)

            # house_owner_rel
            house_list = owner.get('houseList', [])
            for house in house_list:
                house_id = house.get('ID')
                house_name = house.get('name', '')
                rel_id = f"{house_id}_{owner_id}"
                rel_type = get_rel_type(house_name)
                is_default = 1 if rel_type == 1 else 0
                rel_sql = (
                    "INSERT INTO eh_house_owner_rel (rel_id, house_id, owner_id, community_id, rel_type, is_default, check_status, remark, create_time, create_by, update_time, update_by) VALUES ("
                    f"{sql_str(rel_id)}, "
                    f"{sql_str(house_id)}, "
                    f"{sql_str(owner_id)}, "
                    f"{sql_str(owner.get('communityID', ''))}, "
                    f"{sql_str(rel_type)}, "
                    f"{sql_str(is_default)}, "
                    f"{sql_str(1)}, "  # check_status
                    f"{sql_str('')}, "  # remark
                    f"{sql_str('')}, "  # create_time
                    f"{sql_str('')}, "  # create_by
                    f"{sql_str('')}, "  # update_time
                    f"{sql_str('')}"   # update_by
                    ");"
                )
                rel_sqls.append(rel_sql)

    output_file = os.path.join(base_dir, 'output.sql')
    with open(output_file, 'w', encoding='utf-8') as f:
        for sql in owner_sqls + rel_sqls:
            f.write(sql + '\n')

if __name__ == '__main__':
    main()