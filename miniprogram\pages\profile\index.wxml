<view class="container">
  <view class="profile-list">
    <view class="profile-item">
      <text class="label">头像</text>
      <view class="value">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      </view>
    </view>

    <view class="profile-item">
      <text class="label">昵称</text>
      <text class="value">{{userInfo.nickName}}</text>
    </view>

    <view class="profile-item">
      <text class="label">姓名</text>
      <text class="value">{{ownerInfo.ownerName}}</text>
    </view>

    <view class="profile-item">
      <text class="label">手机号</text>
      <view class="value">
        <block wx:if="{{ownerInfo.mobile}}">
          <text>{{ownerInfo.mobile}}</text>
          <text class="btn-text" bindtap="updatePhone">更换</text>
        </block>
        <block wx:else>
          <text class="btn-text" bindtap="bindPhone">去绑定</text>
        </block>
      </view>
    </view>

    <view class="profile-item">
      <text class="label">我的小区</text>
      <text class="value">{{communityInfo.communityName}}</text>
    </view>

    <view class="profile-item">
      <text class="label">我的房屋</text>
      <view class="value">
        <text class="auth-text">{{ownerInfo.houseStr}}</text>
        <text class="btn-text" bindtap="goToAuth" wx:if="{{!isAuth}}">去认证</text>
      </view>
    </view>
  </view>

  <!-- 手机号绑定弹窗 -->
  <view class="popup-mask" wx:if="{{showBindPhonePopup}}" bindtap="closeBindPhonePopup">
    <view class="popup-content" catchtap="stopPropagation">
      <view class="popup-title">绑定手机号</view>
      <view class="popup-desc">为了给您提供更好的服务，请绑定手机号</view>
      <button class="bind-phone-btn" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">一键绑定手机号</button>
      <view class="popup-close" bindtap="closeBindPhonePopup">暂不绑定</view>
    </view>
  </view>
</view> 