import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { formatTime, debounce } from '../../utils/common.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    // 基础数据
    years: [],
    currentYear: '',
    
    // 年度数据
    yearData: [],
    
    // 当前显示的年度详情
    currentYearDetail: null,
    
    // 账户信息
    accountInfo: {
      balance: '',
      updateTime: '',
      dataSource: '',
      accountNumber: ''
    },

    // 页面状态
    loading: false,
    refreshing: false
  },

  onLoad() {
    this.checkLoginAndInit()
  },

  onShow() {
    this.setTabBarSelected()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 检查登录状态并初始化
  checkLoginAndInit() {
    if (!this.checkLoginStatus()) return
    this.initializePage()
  },

  // 设置 TabBar 选中状态
  setTabBarSelected() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 1 })
    }
  },

  // 初始化页面
  async initializePage() {
    await this.loadYears()
  },

  // 刷新数据
  async refreshData() {
    this.setData({ refreshing: true })
    
    try {
      await this.loadYears()
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      handleError(error, '刷新数据')
    } finally {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }
  },

  // 加载年份列表
  async loadYears() {
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: '/api/wx/ccbv2/years',
        method: 'GET'
      })
      
      if (res.code === 0) {
        const years = res.data.years || []
        const currentYear = years[0] || ''
        
        this.setData({
          years,
          currentYear,
          accountInfo: {
            balance: res.data.balance || '--',
            updateTime: res.data.updateTime || '',
            dataSource: res.data.dataSource || '建设银行',
            accountNumber: res.data.accountNumber || '--'
          }
        })

        // 初始化年度数据
        const yearData = years.map((year, index) => ({
          year,
          expanded: index === 0, // 默认展开第一年
          yearIncome: '--',
          yearExpense: '--',
          months: [],
          loading: false,
          loaded: false
        }))

        this.setData({ yearData })

        // 自动加载第一年的数据
        if (currentYear) {
          await this.loadYearDetail(currentYear, 0)
        }
      } else {
        throw new Error(res.msg || '获取年份列表失败')
      }
    } catch (error) {
      handleError(error, '获取年份列表')
    } finally {
      loadingManager.hide()
    }
  },

  // 加载年度详情
  async loadYearDetail(year, yearIndex) {
    const yearData = [...this.data.yearData]
    
    // 如果已经加载过，直接返回
    if (yearData[yearIndex].loaded) {
      return
    }

    try {
      // 设置加载状态
      yearData[yearIndex].loading = true
      this.setData({ yearData })

      const res = await app.request({
        url: '/api/wx/ccbv2/yearDetail',
        method: 'GET',
        data: { year }
      })

      if (res.code === 0) {
        const detail = res.data
        
        // 处理月份数据
        const months = (detail.months || []).map(month => ({
          ...month,
          expanded: false,
          loading: false,
          details: []
        }))

        // 更新年度数据
        yearData[yearIndex] = {
          ...yearData[yearIndex],
          yearIncome: detail.yearIncome || '0.00',
          yearExpense: detail.yearExpense || '0.00',
          months,
          loading: false,
          loaded: true
        }

        this.setData({ yearData })
      } else {
        throw new Error(res.msg || '获取年度详情失败')
      }
    } catch (error) {
      // 恢复加载状态
      yearData[yearIndex].loading = false
      this.setData({ yearData })
      
      handleError(error, '获取年度详情')
    }
  },

  // 切换年份展开状态
  async toggleYear(e) {
    const year = e.currentTarget.dataset.year
    const yearIndex = e.currentTarget.dataset.index
    
    const yearData = [...this.data.yearData]
    const targetYear = yearData[yearIndex]
    
    // 如果当前年份已展开，则收起
    if (targetYear.expanded) {
      yearData[yearIndex].expanded = false
    } else {
      // 收起其他年份，展开当前年份
      yearData.forEach((item, index) => {
        item.expanded = index === yearIndex
      })
      
      // 如果数据未加载，则加载数据
      if (!targetYear.loaded) {
        await this.loadYearDetail(year, yearIndex)
      }
    }
    
    this.setData({ yearData })
  },

  // 切换月份展开状态
  toggleMonth: debounce(async function(e) {
    const year = e.currentTarget.dataset.year
    const month = e.currentTarget.dataset.month
    const yearIndex = e.currentTarget.dataset.yearIndex
    const monthIndex = e.currentTarget.dataset.monthIndex
    
    const yearData = [...this.data.yearData]
    const months = [...yearData[yearIndex].months]
    const targetMonth = months[monthIndex]
    
    // 如果当前月份已展开，则收起
    if (targetMonth.expanded) {
      months[monthIndex].expanded = false
      months[monthIndex].details = []
    } else {
      // 收起其他月份，展开当前月份
      months.forEach((item, index) => {
        item.expanded = index === monthIndex
        if (index !== monthIndex) {
          item.details = []
        }
      })
      
      // 加载月份详情
      await this.loadMonthDetail(month, yearIndex, monthIndex)
    }
    
    yearData[yearIndex].months = months
    this.setData({ yearData })
  }, 300),

  // 加载月份详情
  async loadMonthDetail(month, yearIndex, monthIndex) {
    const yearData = [...this.data.yearData]
    const months = [...yearData[yearIndex].months]
    
    try {
      // 设置加载状态
      months[monthIndex].loading = true
      months[monthIndex].expanded = true
      yearData[yearIndex].months = months
      this.setData({ yearData })

      const res = await app.request({
        url: '/api/wx/ccbv2/monthDetail',
        method: 'GET',
        data: { month }
      })

      if (res.code === 0) {
        // 处理交易详情数据
        const details = (res.data.list || []).map(item => ({
          ...item,
          amount: this.formatAmount(item.amount),
          date: formatTime(item.date, 'MM-DD')
        }))

        months[monthIndex].details = details
        months[monthIndex].loading = false
        months[monthIndex].monthIncome = res.data.monthIncome || '0.00'
        months[monthIndex].monthExpense = res.data.monthExpense || '0.00'
      } else {
        throw new Error(res.msg || '获取月份详情失败')
      }
    } catch (error) {
      months[monthIndex].loading = false
      months[monthIndex].details = []
      handleError(error, '获取月份详情')
    }
    
    yearData[yearIndex].months = months
    this.setData({ yearData })
  },

  // 查看交易详情
  viewTransactionDetail(e) {
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '交易ID无效',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/finance/detail?id=${id}`
    })
  },

  // 格式化金额
  formatAmount(amount) {
    if (!amount) return '0.00'
    const num = parseFloat(amount)
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }
})