<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择停车位')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="parking-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                停车场编码：<input type="text" name="parking_code"/>
                            </li>
                            <li>
                                车位状态：
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="0">空闲</option>
                                    <option value="1">已占用</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/vehicle";
        
        $(function() {
            var options = {
                url: prefix + "/parkingSpaceList",
                modalName: "停车位",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'parking_id',
                    title: '停车位ID',
                    visible: false
                },
                {
                    field: 'parking_code',
                    title: '停车位编码'
                },
                {
                    field: 'parking_area',
                    title: '面积',
                    formatter: function(value, row, index) {
                        return value + "㎡";
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="badge badge-success">空闲</span>';
                        } else if (value == '1') {
                            return '<span class="badge badge-danger">已占用</span>';
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field: 'parking_type',
                    title: '车位类型',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '地上停车位';
                        } else if (value == '2') {
                            return '地下停车位';
                        } else {
                            return value;
                        }
                    }
                }]
            };
            $.table.init(options);
        });
        
        // 停车位选择确认
        function submitHandler(index, layero) {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
            if (row.status == '1') {
                $.modal.alertWarning("该停车位已被占用，请选择空闲停车位");
                return;
            }
            var parking_id = row.parking_id;
            var parking_code = row.parking_code;
            parent.$("input[name='parking_space_id']").val(parking_id);
            parent.$("input[name='parking_space']").val(parking_code);
            parent.layer.close(index);
        }
    </script>
</body>
</html> 