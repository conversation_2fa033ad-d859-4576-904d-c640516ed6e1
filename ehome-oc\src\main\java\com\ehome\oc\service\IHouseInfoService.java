package com.ehome.oc.service;

import com.ehome.oc.domain.HouseInfo;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface IHouseInfoService {

    public HouseInfo recordToObj(Record record);

    /**
     * 查询房屋列表
     */
    List<HouseInfo> selectHouseList(HouseInfo houseInfo);

    /**
     * 新增房屋
     */
    int addHouse(HouseInfo house);

    /**
     * 修改房屋
     */
    int updateHouse(HouseInfo house);

    /**
     * 删除房屋
     */
    int deleteHouse(Long houseId);

    /**
     * 根据ID查询房屋
     */
    HouseInfo selectHouseById(Long houseId);

    /**
     * 统计用户的房屋数量
     */
    int countHouseByUserId(Long wxUserId);

    /**
     * 设置默认房屋
     * @param houseId 房屋ID
     * @param wxUserId 用户ID
     * @return 影响的行数
     */
    int setDefaultHouse(Long houseId, Long wxUserId);
} 