<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <th:block th:include="include :: header('账目分类列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- 搜索区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>分类名称：</label>
                            <input type="text" name="name" title="分类名称" placeholder="请输入分类名称"/>
                        </li>
                        <li>
                            <label>收支方向：</label>
                            <select name="direction" title="收支方向">
                                <option value="">所有</option>
                                <option value="in">收入</option>
                                <option value="out">支出</option>
                            </select>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" title="状态">
                                <option value="">所有</option>
                                <option value="enabled">启用</option>
                                <option value="disabled">停用</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索
                            </a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置
                            </a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- 工具栏 -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <!-- 表格 -->
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/accountType";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            modalName: "账目分类",
            columns: [ {checkbox: true},
                {field: 'id', title: 'ID', visible: false},
                {field: 'name', title: '分类名称'},
                {field: 'direction', title: '收支方向', formatter: function (v) {
                    if (v == 'in') return '<span class="badge badge-success">收入</span>';
                    if (v == 'out') return '<span class="badge badge-danger">支出</span>';
                    return v;
                }},
                {field: 'community_id', title: '所属小区'},
                {field: 'status', title: '状态', formatter: function (v) {
                    if (v == 'enabled') return '<span class="badge badge-primary">启用</span>';
                    if (v == 'disabled') return '<span class="badge badge-default">停用</span>';
                    return v;
                }},
                {field: 'remark', title: '备注'},
                {field: 'create_time', title: '创建时间'},
                {field: 'update_time', title: '更新时间'},
                {
                    title: '操作', align: 'center', 
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editAccount(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');                       
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function editAccount(id) {
        $.operate.edit(id);
    }
</script>
</body>
</html> 