
DROP TABLE IF EXISTS `eh_announcement`;
CREATE TABLE `eh_announcement`  (
  `announcement_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `community_id` int(11) NOT NULL COMMENT '小区ID',
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `publish_date` date NOT NULL COMMENT '发布日期',
  PRIMARY KEY (`announcement_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_building
-- ----------------------------
DROP TABLE IF EXISTS `eh_building`;
CREATE TABLE `eh_building` (
  `building_id` varchar(32) NOT NULL COMMENT '楼栋ID',
  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
  `community_name` varchar(100) DEFAULT '' COMMENT '小区名称',
  `name` varchar(50) DEFAULT NULL COMMENT '楼栋名称或编号',
  `total_units` int(11) DEFAULT '0' COMMENT '单元总数',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人呢',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `manager` varchar(50) DEFAULT '' COMMENT '楼栋管家',
  `house_count` int(11) DEFAULT '0' COMMENT '户数',
  `house_area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
  `order_index` int(11) DEFAULT '100' COMMENT '排序',
  PRIMARY KEY (`building_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='楼栋信息表';
-- ----------------------------
-- Table structure for eh_community
-- ----------------------------
DROP TABLE IF EXISTS `eh_community`;
CREATE TABLE `eh_community`  (
  `oc_id` varchar(32) NOT NULL COMMENT '小区唯一标识',
  `oc_code` varchar(50) NULL DEFAULT '' COMMENT '小区代码',
  `oc_name` varchar(50) NULL DEFAULT '' COMMENT '小区名称',
  `oc_address` varchar(255) NULL DEFAULT '' COMMENT '小区地址',
  `oc_link` varchar(255) NULL DEFAULT '' COMMENT '小区联系人信息',
  `oc_state` int(11) NULL DEFAULT 0 COMMENT '小区状态（0:正常, 1:维护中, 2:关闭）',
  `create_time` varchar(19) NULL DEFAULT '' COMMENT '创建时间',
  `create_by` varchar(30) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) NULL DEFAULT '' COMMENT '修改时间',
  `update_by` varchar(30) NULL DEFAULT '' COMMENT '修改人',
  `owner_count` int(11) NULL DEFAULT 0 COMMENT '业主数量',
  `building_num` int(11) NULL DEFAULT 0 COMMENT '楼宇栋数',
  `community_name` varchar(100) NULL DEFAULT '' COMMENT '所属社区',
  `oc_area` varchar(20) NULL DEFAULT '' COMMENT '小区面积',
  `pms_id` varchar(32) NULL DEFAULT '' COMMENT '物业ID',
  `pms_name` varchar(50) NULL DEFAULT '' COMMENT '物业名称',
  `manager_id` varchar(32) NULL DEFAULT '' COMMENT '负责该小区的员工ID',
  PRIMARY KEY (`oc_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小区信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_complaint
-- ----------------------------
DROP TABLE IF EXISTS `eh_complaint`;
CREATE TABLE `eh_complaint`  (
  `complaint_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '投诉ID',
  `owner_id` int(11) NOT NULL COMMENT '业主ID',
  `community_id` int(11) NOT NULL COMMENT '小区ID',
  `description` text NOT NULL COMMENT '投诉内容',
  `submit_date` date NOT NULL COMMENT '提交日期',
  `status` enum('未处理','处理中','已处理') NOT NULL COMMENT '处理状态',
  PRIMARY KEY (`complaint_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '投诉与建议表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_employee
-- ----------------------------
DROP TABLE IF EXISTS `eh_employee`;
CREATE TABLE `eh_employee`  (
  `employee_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `name` varchar(100) NOT NULL COMMENT '员工姓名',
  `position` varchar(50) NOT NULL COMMENT '职位',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `hire_date` date NOT NULL COMMENT '入职日期',
  `salary` decimal(10, 2) NOT NULL COMMENT '工资',
  PRIMARY KEY (`employee_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_fee
-- ----------------------------
DROP TABLE IF EXISTS `eh_fee`;
CREATE TABLE `eh_fee`  (
  `fee_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '收费项目ID',
  `name` varchar(100) NOT NULL COMMENT '收费项目名称',
  `amount` decimal(10, 2) NOT NULL COMMENT '收费金额',
  `community_id` int(11) NOT NULL COMMENT '小区ID',
  `due_date` date NOT NULL COMMENT '应缴日期',
  PRIMARY KEY (`fee_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '费用项目表' ROW_FORMAT = Dynamic;

CREATE TABLE `eh_vehicle_house_rel` (
  `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
  `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
  `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认(0:否 1:是)',
  `check_status` char(1) DEFAULT '0' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_owner_id` (`house_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆房屋关联表';

-- ----------------------------
-- Table structure for eh_house_info
-- ----------------------------
DROP TABLE IF EXISTS `eh_house_info`;
CREATE TABLE `eh_house_info` (
  `house_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '房屋ID',
  `wx_user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
  `community_name` varchar(50) DEFAULT NULL COMMENT '小区名称',
  `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋ID',
  `building_name` varchar(20) DEFAULT NULL COMMENT '楼栋号',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '单元ID',
  `unit_name` varchar(20) DEFAULT NULL COMMENT '单元名称',
  `combina_name` varchar(255) DEFAULT '' COMMENT '房屋全称',
  `floor` varchar(10) DEFAULT NULL COMMENT '楼层',
  `room` varchar(20) DEFAULT NULL COMMENT '房间号',
  `room_tag` varchar(30) DEFAULT '' COMMENT '房屋标签',
  `use_area` decimal(10,2) DEFAULT '0.00' COMMENT '使用面积',
  `total_area` decimal(10,2) DEFAULT '0.00' COMMENT '建筑面积',
  `house_type` varchar(10) DEFAULT NULL COMMENT '房屋类型',
  `house_status` varchar(10) DEFAULT NULL COMMENT '房屋状态',
  `check_status` int(11) DEFAULT '0' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
  `is_default` int(11) DEFAULT '0' COMMENT '是否默认（0否 1是）',
  `area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  `owner_count` int(11) DEFAULT '0' COMMENT '绑定住户数量',
  PRIMARY KEY (`house_id`) USING BTREE,
  KEY `idx_user_id` (`wx_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='房屋信息表';
-- ----------------------------
-- Table structure for eh_house_owner_rel
-- ----------------------------
DROP TABLE IF EXISTS `eh_house_owner_rel`;
CREATE TABLE `eh_house_owner_rel`  (
  `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
  `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `community_id` varchar(32) NULL DEFAULT NULL,
  `rel_type` tinyint(1) NULL DEFAULT 1 COMMENT '关系类型（1业主 2家庭成员 3租户）',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认（0否 1是）',
  `check_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_time` varchar(19) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`rel_id`) USING BTREE,
  INDEX `idx_house_id`(`house_id`) USING BTREE,
  INDEX `idx_owner_id`(`owner_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '房屋业主关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_maintenance
-- ----------------------------
DROP TABLE IF EXISTS `eh_maintenance`;
CREATE TABLE `eh_maintenance`  (
  `maintenance_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '维修记录ID',
  `house_id` int(11) NOT NULL COMMENT '房屋ID',
  `description` text NOT NULL COMMENT '维修描述',
  `request_date` date NOT NULL COMMENT '维修申请日期',
  `completion_date` date NULL DEFAULT NULL COMMENT '维修完成日期',
  `employee_id` int(11) NULL DEFAULT NULL COMMENT '维修员工ID',
  `status` enum('未处理','处理中','已完成') NOT NULL COMMENT '维修状态',
  PRIMARY KEY (`maintenance_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '维修记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_owner
-- ----------------------------
DROP TABLE IF EXISTS `eh_owner`;
CREATE TABLE `eh_owner`  (
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `owner_name` varchar(50) NULL DEFAULT '' COMMENT '业主姓名',
  `pms_id` varchar(32) NULL DEFAULT '' COMMENT '物业ID',
  `community_id` varchar(32) NULL DEFAULT '' COMMENT '小区ID',
  `mobile` varchar(11) NULL DEFAULT '' COMMENT '手机号码',
  `id_card` varchar(18) NULL DEFAULT '' COMMENT '身份证号码',
  `gender` varchar(1) NULL DEFAULT '' COMMENT '性别:M-男,F-女',
  `address` varchar(200) NULL DEFAULT '' COMMENT '家庭住址',
  `face_photo` varchar(200) NULL DEFAULT '' COMMENT '业主人脸照片地址',
  `door_key_count` int(11) NULL DEFAULT 0 COMMENT '门禁钥匙数量',
  `contract` varchar(200) NULL DEFAULT '' COMMENT '业主合同',
  `house_count` int(11) NULL DEFAULT 0 COMMENT '房屋数量',
  `member_count` int(11) NULL DEFAULT 0 COMMENT '业主成员数',
  `parking_count` int(11) NULL DEFAULT 0 COMMENT '车位数量',
  `car_count` int(11) NULL DEFAULT 0 COMMENT '车辆数量',
  `complaint_count` int(11) NULL DEFAULT 0 COMMENT '投诉次数',
  `repair_count` int(11) NULL DEFAULT 0 COMMENT '报修次数',
  `arrears_count` int(11) NULL DEFAULT 0 COMMENT '欠费次数',
  `remark` varchar(500) NULL DEFAULT '' COMMENT '备注',
  `is_live` int(11) NULL DEFAULT 0 COMMENT '入住状态',
  `create_time` varchar(19) NULL DEFAULT '' COMMENT '创建时间',
  `update_time` varchar(19) NULL DEFAULT '' COMMENT '修改时间',
  `creator` varchar(32) NULL DEFAULT '' COMMENT '创建人',
  `updater` varchar(32) NULL DEFAULT '' COMMENT '修改人',
  PRIMARY KEY (`owner_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '业主信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_parking_owner_rel
-- ----------------------------
DROP TABLE IF EXISTS `eh_parking_owner_rel`;
CREATE TABLE `eh_parking_owner_rel`  (
  `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
  `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `rel_type` tinyint(1) NULL DEFAULT 1 COMMENT '关系类型:1业主 2租户',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认:0否 1是',
  `check_status` tinyint(1) NULL DEFAULT 0 COMMENT '审核状态:0未审核 1已审核 2审核不通过',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  UNIQUE INDEX `uk_parking_owner`(`parking_id`, `owner_id`) USING BTREE,
  INDEX `idx_owner`(`owner_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车位业主关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_parking_space
-- ----------------------------
DROP TABLE IF EXISTS `eh_parking_space`;
CREATE TABLE `eh_parking_space`  (
  `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
  `parking_no` varchar(20) NOT NULL COMMENT '车位编号',
  `parking_type` int(11) NOT NULL COMMENT '车位类型:1私人车位 2子母车位',
  `parking_status` int(11) NOT NULL COMMENT '车位状态:1出售 2出租 3自用',
  `check_status` int(11) NULL DEFAULT 0 COMMENT '审核状态:0未审核 1已审核 2审核不通过',
  `owner_count` int(11) NULL DEFAULT 0 COMMENT '绑定业主数',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(30) NULL DEFAULT '' COMMENT '创建者',
  `create_time` varchar(19) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(30) NULL DEFAULT '' COMMENT '更新者',
  `update_time` varchar(19) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`parking_id`) USING BTREE,
  INDEX `idx_community`(`community_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_payment
-- ----------------------------
DROP TABLE IF EXISTS `eh_payment`;
CREATE TABLE `eh_payment`  (
  `payment_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '缴费记录ID',
  `owner_id` int(11) NOT NULL COMMENT '业主ID',
  `fee_id` int(11) NOT NULL COMMENT '收费项目ID',
  `amount` decimal(10, 2) NOT NULL COMMENT '缴费金额',
  `payment_date` date NOT NULL COMMENT '缴费日期',
  `status` enum('已缴','未缴') NOT NULL COMMENT '缴费状态',
  PRIMARY KEY (`payment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '缴费记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_pms_info
-- ----------------------------
DROP TABLE IF EXISTS `eh_pms_info`;
CREATE TABLE `eh_pms_info`  (
  `pms_id` varchar(32) NOT NULL COMMENT '唯一标识符，主键',
  `pms_name` varchar(255) NULL DEFAULT '' COMMENT '物业名称',
  `address` varchar(255) NULL DEFAULT '' COMMENT '物业地址',
  `manager` varchar(255) NULL DEFAULT '' COMMENT '物业管理员',
  `phone` varchar(20) NULL DEFAULT '' COMMENT '联系电话',
  `legal_person` varchar(255) NULL DEFAULT '' COMMENT '公司法人',
  `establishment_date` varchar(20) NULL DEFAULT '' COMMENT '成立日期',
  `landmark` varchar(255) NULL DEFAULT '' COMMENT '地标',
  `create_time` varchar(20) NULL DEFAULT '' COMMENT '记录创建时间',
  `status` int(11) NULL DEFAULT 0 COMMENT '状态 0 正常 1暂停',
  `employee_count` int(11) NULL DEFAULT NULL COMMENT '员工数量',
  `registration_number` varchar(50) NULL DEFAULT '' COMMENT '注册号',
  PRIMARY KEY (`pms_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物业信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eh_unit
-- ----------------------------
DROP TABLE IF EXISTS `eh_unit`;
CREATE TABLE `eh_unit` (
  `unit_id` varchar(32) NOT NULL COMMENT '单元ID',
  `building_id` varchar(32) NOT NULL COMMENT '楼栋ID',
  `name` varchar(50) NOT NULL COMMENT '单元名称或编号',
  `house_count` int(11) DEFAULT '0' COMMENT '户数',
  `house_area` decimal(10,2) DEFAULT '0.00' COMMENT '房屋面积',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`unit_id`),
  KEY `idx_building_id` (`building_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单元信息表';
-- ----------------------------
-- Table structure for eh_vehicle
-- ----------------------------
DROP TABLE IF EXISTS `eh_vehicle`;
CREATE TABLE `eh_vehicle` (
  `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
  `plate_no` varchar(20) NOT NULL COMMENT '车牌号',
  `vehicle_type` int(11) NOT NULL COMMENT '车辆类型(1:业主车辆 2:公共车辆)',
  `vehicle_brand` varchar(50) DEFAULT NULL COMMENT '车辆品牌',
  `vehicle_model` varchar(50) DEFAULT NULL COMMENT '车辆型号',
  `owner_count` int(11) DEFAULT '0' COMMENT '绑定业主数',
  `check_status` int(11) DEFAULT '0' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(30) DEFAULT '' COMMENT '创建者',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(30) DEFAULT '' COMMENT '更新者',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `plate_type` varchar(50) DEFAULT NULL COMMENT '车牌类型',
  `owner_id` varchar(32) DEFAULT NULL COMMENT '业主ID',
  `owner_name` varchar(100) DEFAULT '' COMMENT '车主姓名',
  `parking_space` varchar(50) DEFAULT '' COMMENT '车位信息',
  `is_owner_ticket` int(11) DEFAULT '0' COMMENT '是否为业主车辆票',
  `parking_space_id` varchar(32) DEFAULT NULL COMMENT '车位ID',
  `house_id` varchar(32) DEFAULT NULL COMMENT '房屋ID',
  `house_name` varchar(100) DEFAULT '' COMMENT '房屋名称',
  PRIMARY KEY (`vehicle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';
-- ----------------------------
-- Table structure for eh_vehicle_owner_rel
-- ----------------------------
DROP TABLE IF EXISTS `eh_vehicle_owner_rel`;
CREATE TABLE `eh_vehicle_owner_rel`  (
  `rel_id` varchar(32) NOT NULL COMMENT '关联ID',
  `vehicle_id` varchar(32) NOT NULL COMMENT '车辆ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认(0:否 1:是)',
  `check_status` char(1) NULL DEFAULT '0' COMMENT '审核状态(0:未审核 1:已审核 2:审核不通过)',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  INDEX `idx_vehicle_id`(`vehicle_id`) USING BTREE,
  INDEX `idx_owner_id`(`owner_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '车辆业主关联表' ROW_FORMAT = Dynamic;
