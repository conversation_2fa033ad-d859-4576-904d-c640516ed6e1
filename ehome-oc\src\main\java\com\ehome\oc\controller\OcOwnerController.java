package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.jfinal.model.OcOwnerModel;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业主信息管理 控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/owner")
public class OcOwnerController extends BaseController {
    
    private static final String PREFIX = "oc/user";

    /**
     * 跳转到业主管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list-owner";
    }

    /**
     * 跳转到新增业主页面
     */
    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add-owner";
    }

    @GetMapping("/importPage")
    public String importPage() {
        return PREFIX + "/import-page";
    }

    /**
     * 跳转到编辑业主页面
     */
    @GetMapping("/edit/{ownerId}")
    public String edit(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());
        return PREFIX + "/edit-owner";
    }

    /**
     * 跳转到业主详情页面
     */
    @GetMapping("/detail/{ownerId}")
    public String detail(@PathVariable("ownerId") String ownerId, ModelMap mmap) {
        // 获取业主信息
        OcOwnerModel owner = OcOwnerModel.dao.findById(ownerId);
        mmap.put("owner", owner.toMap());
        
        // 获取业主关联的房屋列表
        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC", 
            ownerId
        );
        mmap.put("houses", houses);
        
        // 获取业主关联的车辆列表
        List<Record> vehicles = Db.find(
            "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.check_status " +
            "FROM eh_vehicle_owner_rel r " +
            "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC", 
            ownerId
        );
        mmap.put("vehicles", vehicles);
        
        return PREFIX + "/detail-owner";
    }

    /**
     * 查询业主列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"), 
            params.getIntValue("pageSize"), 
            "select *", 
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String ownerId = params.getString("owner_id");
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        Record owner = Db.findFirst("select * from eh_owner where owner_id = ?", ownerId);
        return AjaxResult.success(null, owner.toMap());
    }

    /**
     * 新增业主信息
     */
    @PostMapping("/add")
    @ResponseBody
    @Log(title = "新增业主信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);
        model.set("owner_id", Seq.getId());
        setCreateAndUpdateInfo(model);
        model.save();
        return AjaxResult.success();
    }

    /**
     * 修改业主信息
     */
    @PostMapping("/edit")
    @ResponseBody
    @Log(title = "修改业主信息", businessType = BusinessType.UPDATE)
    public AjaxResult edit() {
        JSONObject params = getParams();
        OcOwnerModel model = new OcOwnerModel();
        model.setColumns(params);
        setUpdateInfo(model);
        return toAjax(model.update());
    }

    /**
     * 删除业主信息
     */
    @PostMapping("/remove")
    @ResponseBody
    @Log(title = "删除业主信息", businessType = BusinessType.DELETE)
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数ids不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            OcOwnerModel.dao.deleteById(id);
        }
        return success();
    }
    
    /**
     * 获取业主关联的房屋列表
     */
    @PostMapping("/houseList")
    @ResponseBody
    public TableDataInfo houseList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }
        
        List<Record> houses = Db.find(
            "SELECT r.*, h.building_name, h.unit_name, h.room, h.total_area, h.house_status " +
            "FROM eh_house_owner_rel r " +
            "LEFT JOIN eh_house_info h ON r.house_id = h.house_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC", 
            ownerId
        );
        
        return getDataList(houses);
    }
    
    /**
     * 获取业主关联的车辆列表
     */
    @PostMapping("/vehicleList")
    @ResponseBody
    public TableDataInfo vehicleList() {
        JSONObject params = getParams();
        String ownerId = params.getString("ownerId");
        if (StringUtils.isEmpty(ownerId)) {
            return getDataTable(new Page<>());
        }
        
        List<Record> vehicles = Db.find(
            "SELECT r.*, v.plate_no, v.vehicle_brand, v.vehicle_model, v.check_status " +
            "FROM eh_vehicle_owner_rel r " +
            "LEFT JOIN eh_vehicle v ON r.vehicle_id = v.vehicle_id " +
            "WHERE r.owner_id = ? " +
            "ORDER BY r.is_default DESC, r.create_time DESC", 
            ownerId
        );
        
        return getDataList(vehicles);
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_owner where 1=1");

        if(!getSysUser().isAdmin()){
            sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        }

        // 业主姓名 - 模糊查询
        sql.appendLike(params.getString("owner_name"), "and owner_name like ?");
        // 手机号码 - 精确匹配
        sql.appendLike(params.getString("mobile"), "and mobile like ?");
        // 身份证号码 - 精确匹配
        sql.append(params.getString("id_card"), "and id_card = ?");
        // 性别 - 精确匹配
        sql.append(params.getString("gender"), "and gender = ?");
        
        // 创建时间范围查询
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and create_time <= ?");

        // 默认按创建时间倒序
        sql.append("order by create_time desc");
        return sql;
    }

    /**
     * 设置创建和更新信息
     */
    private void setCreateAndUpdateInfo(OcOwnerModel model) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        model.set("create_time", now);
        model.set("update_time", now);
        model.set("creator", loginName);
        model.set("updater", loginName);
        model.set("pms_id", getSysUser().getPmsId());
        model.set("community_id", getSysUser().getCommunityId());

    }

    /**
     * 设置更新信息
     */
    private void setUpdateInfo(OcOwnerModel model) {
        model.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        model.set("updater", getSysUser().getLoginName());
    }
}
