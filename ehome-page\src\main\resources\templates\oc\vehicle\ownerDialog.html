<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择业主')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="owner-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                业主姓名：<input type="text" name="owner_name"/>
                            </li>
                            <li>
                                联系方式：<input type="text" name="mobile"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/vehicle";
        
        $(function() {
            var options = {
                url: prefix + "/ownerList",
                modalName: "业主",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'owner_id',
                    title: '业主ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '业主姓名'
                },
                {
                    field: 'mobile',
                    title: '联系方式'
                },
                {
                    field: 'user_type',
                    title: '人员类型',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '业主';
                        } else if (value == '2') {
                            return '家庭成员';
                        } else {
                            return value;
                        }
                    }
                }]
            };
            $.table.init(options);
        });
        
        // 业主选择确认
        function submitHandler(index, layero) {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var row = $("#bootstrap-table").bootstrapTable('getSelections')[0];
            var owner_id = row.owner_id;
            var owner_name = row.owner_name;
            parent.$("input[name='owner_id']").val(owner_id);
            parent.$("input[name='owner_name']").val(owner_name);
            parent.layer.close(index);
        }
    </script>
</body>
</html> 