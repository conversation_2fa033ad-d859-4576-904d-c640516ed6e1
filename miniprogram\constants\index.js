// 常量定义文件
export const USER_ROLES = {
  OWNER: '1',           // 业主
  FAMILY_MEMBER: '2',   // 家庭成员
  TENANT: '3'           // 租户
}

export const USER_ROLE_NAMES = {
  [USER_ROLES.OWNER]: '业主',
  [USER_ROLES.FAMILY_MEMBER]: '家庭成员',
  [USER_ROLES.TENANT]: '租户'
}

export const API_CODES = {
  SUCCESS: 0,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  SERVER_ERROR: 500
}

export const FEEDBACK_TYPES = {
  REPAIR: 'bx',         // 报修
  COMPLAINT: 'complaint' // 投诉建议
}

export const REPAIR_OPTIONS = [
  { label: '公共区域报修', value: '公共区域报修' },
  { label: '入户维修', value: '入户维修' },
  { label: '上门安装', value: '上门安装' },
  { label: '管道疏通', value: '管道疏通' },
  { label: '其他', value: '其他' }
]

export const COMPLAINT_OPTIONS = [
  { label: '投诉', value: '投诉' },
  { label: '建议', value: '建议' }
]

export const STORAGE_KEYS = {
  TOKEN: 'token',
  WX_USER_INFO: 'wxUserInfo',
  OWNER_INFO: 'ownerInfo',
  COMMUNITY_INFO: 'communityInfo',
  HOUSE_INFO: 'houseInfo',
  TOKEN_USER: 'tokenUser'
}

export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  LOGIN_EXPIRED: '登录已过期，请重新登录',
  PERMISSION_DENIED: '权限不足，请联系管理员',
  SERVER_BUSY: '服务器繁忙，请稍后重试',
  OPERATION_FAILED: '操作失败，请重试'
} 