<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 顶部社区信息 -->
    <view class="community-header">
      <image class="community-banner" src="{{communityInfo.communityBanner}}" mode="aspectFill"></image>
      <view class="community-info">
        <view class="community-name">{{communityInfo.communityName}}</view>
        <view class="weather-info" wx:if="{{weather}}">
          <text>{{weather.temp}}°C</text>
          <text>{{weather.text}}</text>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card" wx:if="{{isLogin}}">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-info">
        <view class="user-row">
          <text class="nickname">{{ownerInfo.ownerName}}</text>
          <text class="phone" wx:if="{{ownerInfo.mobile}}">{{ownerInfo.mobile}}</text>
          <text class="status {{ownerInfo.role}}">
            <block wx:if="{{ownerInfo.role == '1'}}">业主</block>
            <block wx:elif="{{ownerInfo.role == '2'}}">家庭成员</block>
            <block wx:elif="{{ownerInfo.role == '3'}}">租户</block>
            <block wx:else>业主</block>
          </text>
        </view>
        <text class="house-info" wx:if="{{ownerInfo.houseStr}}">{{ownerInfo.houseStr}}</text>
      </view>
    </view>


    <!-- 功能模块 -->
    <view class="function-grid">
      <view class="grid-item" bindtap="goToRepair">
        <view class="iconfont icon-repair"></view>
        <text>物业报修</text>
      </view>
      <view class="grid-item" bindtap="goToPayment">
        <view class="iconfont icon-payment"></view>
        <text>物业缴费</text>
      </view>
      <view class="grid-item" bindtap="goToVisitor">
        <view class="iconfont icon-visitor"></view>
        <text>邀请住户</text>
      </view>
      <view class="grid-item" bindtap="goToOcInfo">
        <view class="iconfont icon-package"></view>
        <text>小区信息</text>
      </view>
      <view class="grid-item" bindtap="goToServicePhone">
        <view class="iconfont icon-fuwudianhua"></view>
        <text>服务电话</text>
      </view>
      <view class="grid-item" bindtap="goToComplaint">
        <view class="iconfont icon-complaint"></view>
        <text>投诉建议</text>
      </view>
    </view>

    <!-- 社区动态 -->
    <view class="community-news">
      <view class="section-title">
        <text>公告动态</text>
        <text class="more" bindtap="{{isLogin ? 'goToNoticeList' : 'goToLogin'}}">更多</text>
      </view>
      <view class="news-list">
        <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="{{isLogin ? 'goToNewsDetail' : 'goToLogin'}}" data-id="{{item.id}}">
          <view class="news-icon iconfont {{index === 0 ? 'icon-gonggao' : index === 1 ? 'icon-tongzhi' : 'icon-xinwen'}}"></view>
          <view class="news-content">
            <view class="news-title">{{item.title}}</view>
            <view class="news-footer">
              <text class="news-time">{{item.time}}</text>
              <text class="news-type {{item.type === 'notice' ? 'notice' : ''}}">{{item.type === 'notice' ? '通知' : '公告'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
