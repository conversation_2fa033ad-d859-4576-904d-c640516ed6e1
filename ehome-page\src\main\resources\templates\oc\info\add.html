<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增小区')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-add" name="form-config-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">小区名称：</label>
                <div class="col-sm-8">
                    <input id="oc_name" name="oc_name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">小区地址：</label>
                <div class="col-sm-8">
                    <input id="oc_address" name="oc_address" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系人信息：</label>
                <div class="col-sm-8">
                    <input id="oc_link" name="oc_link" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">小区状态：</label>
                <div class="col-sm-8">
                    <select name="oc_state" class="form-control">
                        <option value="0">正常</option>
                        <option value="1">维护中</option>
                        <option value="2">关闭</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">楼宇栋数：</label>
                <div class="col-sm-8">
                    <input id="building_num" name="building_num" class="form-control" type="number" value="0">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">所属社区：</label>
                <div class="col-sm-8">
                    <input id="community_name" name="community_name" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    var prefix = ctx + "oc/info";
	
	    $("#form-config-add").validate({
	    	onkeyup: false,
	        rules: {
	            oc_name: {
	                remote: {
	                    url: prefix + "/checkName",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "oc_name": function() {
	                            return $.common.trim($("#oc_name").val());
	                        }
	                    }
	                }
	            },
	        },
	        messages: {
	            "oc_name": {
	                remote: "小区名称已经存在"
	            }
	        },
	        focusCleanup: true
	    });
	    
	    function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/addData", $('#form-config-add').serialize());
	        }
	    }
    </script>
</body>
</html>
