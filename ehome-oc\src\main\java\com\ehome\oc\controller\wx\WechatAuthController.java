package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.SecurityUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.domain.OwnerInfo;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.IWechatAuthService;
import com.ehome.oc.service.IWxUserService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/wx/auth")
public class WechatAuthController extends BaseWxController {

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private IWechatAuthService wechatAuthService;

    /**
     * 微信登录
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody WxLoginDTO loginDTO) {
        try {
            String code = loginDTO.getCode();
            if (code == null || code.isEmpty()) {
                return AjaxResult.error("code不能为空");
            }

            // 调用微信登录服务获取用户信息，根据Openid查询用户信息
            WxUser wxUser = wxUserService.wxLogin(loginDTO);
            if (wxUser == null) {
                return AjaxResult.error("登录失败");
            }

            // 创建登录用户信息
            LoginUser loginUser = new LoginUser();
            loginUser.setOpenId(wxUser.getOpenId());
            loginUser.setUserId(wxUser.getUserId());
            loginUser.setUsername(wxUser.getNickName());
            loginUser.setMobile(wxUser.getMobile());
            loginUser.setUserType("1"); // 1表示小程序用户
            if(StringUtils.isNotBlank(wxUser.getMobile())){
                OwnerInfo ownerInfo = getOwnerInfo(wxUser.getMobile());
                loginUser.setCommunityId(ownerInfo.getCommunityId());
            }

            // 生成token
            String token = SecurityUtils.createToken(loginUser);

            // 返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("userInfo", wxUser);
            data.put("tokenUser", loginUser);
            data.put("hasBindPhone", StringUtils.isNotEmpty(wxUser.getMobile()));
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("微信登录失败: " + e.getMessage(),e);
            return AjaxResult.error("登录失败: " + e.getMessage());
        }
    }

    @RequestMapping("/check")
    public AjaxResult check() {
        Map<String, Object> data = new HashMap<>();
        JSONObject params =  getParams();
        try {
            LoginUser currentUser = getCurrentUser();
            if(currentUser==null&&params!=null){
                JSONObject tokenUser = params.getJSONObject("tokenUser");
                if(tokenUser!=null){
                    LoginUser loginUser = new LoginUser();
                    loginUser.setOpenId(tokenUser.getString("openId"));
                    loginUser.setUserId(tokenUser.getLong("userId"));
                    loginUser.setUsername(tokenUser.getString("username"));
                    loginUser.setMobile(tokenUser.getString("mobile"));
                    loginUser.setCommunityId(tokenUser.getString("communityId"));
                    loginUser.setUserType("1");
                    if(StringUtils.isNotBlank(loginUser.getMobile())&&StringUtils.isNotBlank(loginUser.getCommunityId())){
                        String token = SecurityUtils.createToken(loginUser);
                        data.put("token", token);
                        data.put("tokenUser", loginUser);
                        return AjaxResult.success(data);
                    }
                }
            }
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }else{
                data.put("tokenUser", currentUser);
                return AjaxResult.success(data);
            }
        }catch (Exception e) {
            logger.error("获取认证状态失败: " + e.getMessage(),e);
            return AjaxResult.error("用户未登录");
        }
    }

    private OwnerInfo getOwnerInfo(String mobile) {
        try {
            Record ownerInfo = Db.findFirst("select * from eh_owner where mobile = ?", mobile);
            if (ownerInfo != null) {
                OwnerInfo owner = new OwnerInfo();
                owner.setMobile(ownerInfo.getStr("mobile"));
                owner.setOwnerId(ownerInfo.getStr("owner_id"));
                owner.setOwnerName(ownerInfo.getStr("owner_name"));
                owner.setCommunityId(ownerInfo.getStr("community_id"));
                return owner;
            }
        } catch (Exception e) {
            logger.error("获取业主信息失败: " + e.getMessage(), e);
        }
        return null;
    }


    @PostMapping("/phoneDecrypt")
    public AjaxResult decryptPhoneNumber(@RequestBody Map<String, Object> params) {
        try {
            logger.info("decryptPhoneNumber params: " + params);
            String code = (String) params.get("code");
            String phoneCode = (String) params.get("phoneCode");
            AjaxResult  result = wechatAuthService.decryptPhoneNumber(code, phoneCode);
            return result;
        } catch (Exception e) {
            return AjaxResult.error("解密手机号失败: " + e.getMessage(),e);
        }
    }

    @PostMapping("/updatePhone")
    public AjaxResult updateUserPhone(@RequestBody Map<String, String> params) {
        try {
            logger.info("updateUserPhone params: " + params);
            String userId = params.get("userId");
            String phoneNumber = params.get("phoneNumber");
            setCurrentUserMobile(phoneNumber);
            AjaxResult result = wechatAuthService.updateUserPhone(userId, phoneNumber);
            if (result.isSuccess()) {
                // 重新生成带新手机号的 token
                LoginUser loginUser = getCurrentUser();
                loginUser.setMobile(phoneNumber);
                if(StringUtils.isNotBlank(phoneNumber)){
                    OwnerInfo ownerInfo = getOwnerInfo(phoneNumber);
                    loginUser.setCommunityId(ownerInfo.getCommunityId());
                }
                String oldToken = loginUser.getToken();
                String newToken = SecurityUtils.createToken(loginUser);
                result.put("token", newToken);
                result.put("tokenUser",getCurrentUser());
                logger.info("更新手机号成功，旧token: " + oldToken + ", 新token: " + newToken);
            }
            return result;
        } catch (Exception e) {
            logger.error("更新手机号失败: " + e.getMessage(), e);
            return AjaxResult.error("更新手机号失败: " + e.getMessage());
        }
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout() {
        try {
            LoginUser loginUser = getCurrentUser();
            if (loginUser != null) {
                // 记录退出日志
                String username = loginUser.getUsername();
                String userId = String.valueOf(loginUser.getUserId());
                logger.info("用户退出登录: " + username + "（" + userId + "）");
                // 清除用户信息
                currentUser.remove();
                return AjaxResult.success("退出成功");
            }
            return AjaxResult.error("用户未登录");
        } catch (Exception e) {
            logger.error("退出登录失败: " + e.getMessage(),e);
            return AjaxResult.error("退出失败: " + e.getMessage());
        }
    }


    /**
     * 更新用户信息
     */
    @PostMapping("/update")
    public AjaxResult updateUserInfo(@RequestBody WxUser wxUser) {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return AjaxResult.error("用户未登录");
            }

            // 设置用户ID，防止篡改其他用户信息
            wxUser.setUserId(userId);
            
            int rows = wxUserService.updateWxUser(wxUser);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("更新失败");
        } catch (Exception e) {
            logger.error("更新用户信息失败: " + e.getMessage(),e);
            return AjaxResult.error("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 统一登录完成接口 - 包含房屋认证检查
     */
    @PostMapping("/loginComplete")
    public AjaxResult loginComplete(@RequestBody WxLoginDTO loginDTO) {
        try {
            String code = loginDTO.getCode();
            if (code == null || code.isEmpty()) {
                return AjaxResult.error("code不能为空");
            }

            // 调用微信登录服务获取用户信息
            WxUser wxUser = wxUserService.wxLogin(loginDTO);
            if (wxUser == null) {
                return AjaxResult.error("登录失败");
            }

            // 创建登录用户信息
            LoginUser loginUser = new LoginUser();
            loginUser.setOpenId(wxUser.getOpenId());
            loginUser.setUserId(wxUser.getUserId());
            loginUser.setUsername(wxUser.getNickName());
            loginUser.setMobile(wxUser.getMobile());
            loginUser.setUserType("1"); // 1表示小程序用户

            boolean hasBindPhone = StringUtils.isNotEmpty(wxUser.getMobile());
            boolean isHouseAuth = false;
            OwnerInfo ownerInfo = null;
            Map<String, Object> communityInfo = null;

            if (hasBindPhone) {
                ownerInfo = getOwnerInfo(wxUser.getMobile());
                if (ownerInfo != null) {
                    loginUser.setCommunityId(ownerInfo.getCommunityId());
                    isHouseAuth = true;
                    
                    // 获取社区信息
                    communityInfo = getCommunityInfo(ownerInfo.getCommunityId());
                }
            }

            // 生成token
            String token = SecurityUtils.createToken(loginUser);

            // 返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("userInfo", wxUser);
            data.put("tokenUser", loginUser);
            data.put("hasBindPhone", hasBindPhone);
            data.put("isHouseAuth", isHouseAuth);
            data.put("ownerInfo", ownerInfo);
            data.put("communityInfo", communityInfo);

            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("统一登录失败: " + e.getMessage(), e);
            return AjaxResult.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 绑定手机号接口
     */
    @PostMapping("/bindPhone")
    public AjaxResult bindPhone(@RequestBody Map<String, String> params) {
        try {
            logger.info("bindPhone params: " + params);
            String phoneCode = params.get("phoneCode");
            String userId = params.get("userId");

            if (StringUtils.isEmpty(phoneCode)) {
                return AjaxResult.error("手机号授权码不能为空");
            }

            // 解密手机号
            AjaxResult decryptResult = wechatAuthService.decryptPhoneNumber(null, phoneCode);
            if (!decryptResult.isSuccess()) {
                return decryptResult;
            }

            Map<String, Object> decryptData = (Map<String, Object>) decryptResult.get("data");
            String phoneNumber = (String) decryptData.get("phoneNumber");

            // 更新用户手机号
            setCurrentUserMobile(phoneNumber);
            AjaxResult updateResult = wechatAuthService.updateUserPhone(userId, phoneNumber);
            if (!updateResult.isSuccess()) {
                return updateResult;
            }

            // 检查房屋认证
            OwnerInfo ownerInfo = getOwnerInfo(phoneNumber);
            boolean isHouseAuth = ownerInfo != null;
            Map<String, Object> communityInfo = null;

            // 重新生成token
            LoginUser loginUser = getCurrentUser();
            loginUser.setMobile(phoneNumber);
            if (isHouseAuth) {
                loginUser.setCommunityId(ownerInfo.getCommunityId());
                
                // 获取社区信息
                communityInfo = getCommunityInfo(ownerInfo.getCommunityId());
            }

            String newToken = SecurityUtils.createToken(loginUser);

            Map<String, Object> result = new HashMap<>();
            result.put("token", newToken);
            result.put("tokenUser", loginUser);
            result.put("hasBindPhone", true);
            result.put("isHouseAuth", isHouseAuth);
            result.put("ownerInfo", ownerInfo);
            result.put("communityInfo", communityInfo);

            return AjaxResult.success("绑定成功", result);
        } catch (Exception e) {
            logger.error("绑定手机号失败: " + e.getMessage(), e);
            return AjaxResult.error("绑定手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取社区信息
     */
    private Map<String, Object> getCommunityInfo(String communityId) {
        try {
            if (StringUtils.isEmpty(communityId)) {
                return null;
            }
            
            Record communityRecord = Db.findFirst("select * from eh_community where oc_id = ?", communityId);
            if (communityRecord != null) {
                Map<String, Object> communityInfo = new HashMap<>();
                communityInfo.put("communityId", communityId);
                communityInfo.put("communityName", communityRecord.getStr("oc_name"));
                communityInfo.put("address", communityRecord.getStr("oc_address"));
                communityInfo.put("servicePhone", communityRecord.getStr("service_phone"));
                communityInfo.put("communityBanner", "/static/images/banner.png");
                return communityInfo;
            }
        } catch (Exception e) {
            logger.error("获取社区信息失败: " + e.getMessage(), e);
        }
        return null;
    }
} 