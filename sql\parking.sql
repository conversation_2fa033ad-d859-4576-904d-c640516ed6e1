 -- 车位信息表
CREATE TABLE `eh_parking_space` (
  `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
  `community_id` varchar(32) NOT NULL COMMENT '小区ID',
  `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋ID',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '单元ID', 
  `parking_no` varchar(20) NOT NULL COMMENT '车位编号',
  `parking_type` tinyint(1) NOT NULL COMMENT '车位类型:1私人车位 2子母车位',
  `parking_status` tinyint(1) NOT NULL COMMENT '车位状态:1出售 2出租 3自用',
  `check_status` tinyint(1) DEFAULT '0' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
  `owner_count` int(11) DEFAULT '0' COMMENT '绑定业主数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者', 
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`parking_id`),
  KEY `idx_community` (`community_id`),
  KEY `idx_building` (`building_id`),
  KEY `idx_unit` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位信息表';

-- 车位业主关系表 
CREATE TABLE `eh_parking_owner_rel` (
  `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
  `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `rel_type` tinyint(1) DEFAULT '1' COMMENT '关系类型:1业主 2租户',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认:0否 1是',
  `check_status` tinyint(1) DEFAULT '0' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  UNIQUE KEY `uk_parking_owner` (`parking_id`,`owner_id`),
  KEY `idx_owner` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位业主关系表';