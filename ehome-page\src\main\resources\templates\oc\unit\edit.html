<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改单元')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-unit-edit" th:object="${unit}">
        <input name="unitId" type="hidden" th:field="*{unitId}" />
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">单元序号：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="name" th:field="*{name}" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">房屋数量：</label>
            <div class="col-sm-8">
                <input class="form-control" type="number" name="houseCount" th:field="*{houseCount}" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">建筑面积/㎡：</label>
            <div class="col-sm-8">
                <input class="form-control" type="number" name="houseArea" th:field="*{houseArea}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="remark" class="form-control" th:field="*{remark}"></textarea>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script type="text/javascript">
    var prefix = ctx + "oc/unit";

    $("#form-unit-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-unit-edit').serialize());
        }
    }
</script>
</body>
</html>