.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.detail-header {
  border-bottom: 1rpx solid #eee;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.detail-date {
  font-size: 26rpx;
  color: #999;
}

.detail-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.income {
  color: #07c160;
}

.expense {
  color: #ff4d4f;
}

.detail-remark {
  margin-bottom: 30rpx;
}

.remark-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.remark-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 附件样式 */
.attachments {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.attach-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.attach-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.attach-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.attach-name {
  font-size: 28rpx;
  color: #333;
} 