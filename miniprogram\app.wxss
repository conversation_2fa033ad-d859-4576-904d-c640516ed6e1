/**app.wxss**/
page {
  --primary-color: #07c160;
  --danger-color: #ff4d4f;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-light: #999999;
  --border-color: #f5f5f5;
  --bg-color: #f7f8fa;
  --white: #ffffff;
  
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  background: var(--bg-color);
  color: var(--text-color);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

/* 全局容器 */
.container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}

/* 通用卡片样式 */
.card {
  background: var(--white);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

/* 通用按钮样式 */
.btn {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  text-align: center;
  border-radius: 44rpx;
  padding: 0 60rpx;
  font-weight: 500;
  transition: opacity 0.3s;
}

.btn:active {
  opacity: 0.9;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-danger {
  background: var(--danger-color);
  color: var(--white);
}

/* 通用列表样式 */
.list {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: #fafafa;
}

/* 通用标题样式 */
.title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: var(--text-color-secondary);
  margin-bottom: 16rpx;
}

/* 通用输入框样式 */
.input {
  height: 88rpx;
  background: var(--bg-color);
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: var(--text-color);
}

/* 通用标签样式 */
.tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.tag-primary {
  background: rgba(7, 193, 96, 0.1);
  color: var(--primary-color);
}

.tag-danger {
  background: rgba(255, 77, 79, 0.1);
  color: var(--danger-color);
}

/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 4812128 */
  src: url('//at.alicdn.com/t/c/font_4812128_r3odtvx7nlo.woff2?t=1737097354481') format('woff2'),
       url('//at.alicdn.com/t/c/font_4812128_r3odtvx7nlo.woff?t=1737097354481') format('woff'),
       url('//at.alicdn.com/t/c/font_4812128_r3odtvx7nlo.ttf?t=1737097354481') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标定义 */
.icon-gonggao:before {
  content: "\e609";
}

.icon-tongzhi:before {
  content: "\e759";
}

.icon-xinwen:before {
  content: "\e613";
}

.icon-arrow:before {
  content: "\e664";
}

.icon-right:before {
  content: "\e612";
}

.icon-up:before {
  content: "\e63c";
}

.icon-down:before {
  content: "\e632";
}

/* 功能图标 */
.icon-visitor:before { content: "\e72f"; }
.icon-repair:before { content: "\e615"; }
.icon-payment:before { content: "\e64a"; }
.icon-package:before { content: "\e8b6"; }
.icon-announcement:before { content: "\e656"; }
.icon-complaint:before { content: "\e656"; }
.icon-fuwudianhua:before {content:"\e602";}



/* 通用布局辅助类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-light {
  color: var(--text-color-light);
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.ml-20 {
  margin-left: 20rpx;
}

.mr-20 {
  margin-right: 20rpx;
} 
