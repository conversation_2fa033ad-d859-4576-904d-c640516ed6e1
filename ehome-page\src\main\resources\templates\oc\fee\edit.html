<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改收费标准')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-fee-edit">
            <input name="id" type="hidden" th:value="${fee.id}">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">收费名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="fee_name" th:value="${fee.fee_name}" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">收费类型：</label>
                <div class="col-sm-8">
                    <select name="fee_type" class="form-control" required>
                        <option value="">请选择收费类型</option>
                        <option value="临时性收费" th:selected="${fee.fee_type == '临时性收费'}">临时性收费</option>
                        <option value="周期性收费" th:selected="${fee.fee_type == '周期性收费'}">周期性收费</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">计费精度：</label>
                <div class="col-sm-8">
                    <select name="precision_type" class="form-control" required>
                        <option value="">请选择计费精度</option>
                        <option value="元" th:selected="${fee.precision_type == '元'}">元</option>
                        <option value="分" th:selected="${fee.precision_type == '分'}">分</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">收费方式：</label>
                <div class="col-sm-8">
                    <select name="collection_method" class="form-control" required>
                        <option value="">请选择收费方式</option>
                        <option value="一次性收取" th:selected="${fee.collection_method == '一次性收取'}">一次性收取</option>
                        <option value="按月生成账单" th:selected="${fee.collection_method == '按月生成账单'}">按月生成账单</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">金额计算方式：</label>
                <div class="col-sm-8">
                    <select name="calculation_method" class="form-control" required>
                        <option value="">请选择计算方式</option>
                        <option value="固定金额" th:selected="${fee.calculation_method == '固定金额'}">固定金额</option>
                        <option value="单价*计量方式" th:selected="${fee.calculation_method == '单价*计量方式'}">单价*计量方式</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">单价：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="number" name="unit_price" th:value="${fee.unit_price}" step="0.01">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">固定金额：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="number" name="fixed_amount" th:value="${fee.fixed_amount}" step="0.01">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">计量方式：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="measurement_type" th:value="${fee.measurement_type}">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">账单生成日：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="number" name="bill_generation_day" th:value="${fee.bill_generation_day}" min="1" max="31">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">违约金规则：</label>
                <div class="col-sm-8">
                    <textarea class="form-control" name="penalty_rule" th:text="${fee.penalty_rule}"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea class="form-control" name="remark" th:text="${fee.remark}"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/fee";
        
        $("#form-fee-edit").validate({
            focusCleanup: true
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-fee-edit').serialize());
            }
        }
    </script>
</body>
</html> 