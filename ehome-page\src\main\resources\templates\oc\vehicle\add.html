<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增车辆')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-vehicle-add">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">车牌号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="plate_no" required>
                </div>
                <label class="col-sm-2 control-label">车辆类型：</label>
                <div class="col-sm-4">
                    <select class="form-control" name="vehicle_type">
                        <option value="">请选择车辆类型</option>
                        <option value="业主车辆">业主车辆</option>
                        <option value="非业主车辆">非业主车辆</option>
                       
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">车主姓名：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_real_name">
                </div>
                <label class="col-sm-2 control-label">车主手机号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_phone">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">业主：</label>
                <div class="col-sm-4">
                    <input type="hidden" name="owner_id">
                    <input class="form-control" type="text" name="owner_name" onclick="selectOwner()" readonly>
                </div>  
                <label class="col-sm-2 control-label">车位：</label>
                <div class="col-sm-4">
                    <input type="hidden" name="parking_space_id">
                    <input class="form-control" type="text" name="parking_space" onclick="selectParkingSpace()" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/vehicle";
        
        $("#form-vehicle-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-vehicle-add').serialize());
            }
        }

        function selectOwner() {
            $.modal.openDialog('ownerDialog', prefix + '/ownerDialog');
        }

        function selectParkingSpace() {
            $.modal.openDialog('parkingSpaceDialog', prefix + '/parkingSpaceDialog');
        }
    </script>
</body>
</html>