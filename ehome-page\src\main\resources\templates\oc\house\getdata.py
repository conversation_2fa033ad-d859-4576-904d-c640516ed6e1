import requests
import time

url_template = "https://admin.xtzhwy.com/mkg/api/v2/Charge/getHouseList?communityID=10772&page={page}&pageSize=20&r=0.7600765709002444"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
    "Cookie": "hiido_ui=0.6490742136296532; osudb_lang=zh-cn; osudb_third=2eba153b5220194e710506da8a5a38842e6eb4c4280cefe1d4b154c5ee52d83d49454b5d0129ba27f5529c418b1bb4fff90b9f8c1616ebb0ae9b07e8d26b7dbb15518d477385577275858a4c0222ab380d2055ecf2b00d1d8fed341772cf4a16f1ce9c1600d72d6880291ff822954e3d0c272dc66ec4e353f6efd3d9482cbf36a34a266782cc82ce2e260d132c0031ac3615c41221ba977d1e221390e11cacef7e7665c012edacb499fd4fdd9399b3e15264f778dfe86dcd38978a019197773958310e61545321f91d041205c02f1777a6f4ba19391af1beaeffeac8a5c8f6a4f3a14f1d3ef3524f899a39890111779c431b25ba69845b35912262b56e82908f; osudb_uid=4806379939; osudb_appid=1435186595; osudb_oar=0f0154d341e743e2b2a80809fa117c9e39ce363de3fc35ab72d7528536f498e44a8023757c7b1d91a287a4faac6e2dbc0f58792ff9c5d7706205e77f1c15e44835ea4567bcef1b0cfc51e9d2ceaf7c837af0d41ca21c7bd3503ad09d0cd5421f989b88d00106d1768535b49992141af5b5405756750f1413875149f3829348387504b6f2bfa57e491ac7d4e043f78033edbd9107e8a5aa772096b4d1790e0ebda0e7c816d56d59120dcb03ea83f1113c2424436efa68a5149279319f79c6ba248c93e2821ce85f7a9b0f8da975b741ecda0c0657b10cb36b07a3573f1586012d82d131f0e9d367040af351d4ea6355eb4413838ab7d46d48c4fafcf3d70fe781; osudb_c=00804432107a0001700082fd2355bbfcf3cd6a9d11087a6f46bd0ae7b905d18550ec354d98991408eb69e1422c35640b528dba8ef9075ff54b82027900e6fe325105b463dfd1b1c516072e5837511b196a5e176ec9327607839f1dbe3645acda734d0fa67283c4ff0fb7a1d17d63004786ddfbeeac2f53976a48; osudb_sex=0; osudb_param=; osudb_ustate=1; osudb_nickname=Rocky;"
}

with open("getdata.txt", "w", encoding="utf-8") as f:
    for page in range(1, 29):
        url = url_template.format(page=page)
        try:
            resp = requests.get(url, headers=headers, timeout=10)
            resp.raise_for_status()
            f.write(resp.text + "\n")
            print(f"第{page}页数据写入成功")
        except Exception as e:
            print(f"第{page}页请求失败: {e}")
        time.sleep(1)
