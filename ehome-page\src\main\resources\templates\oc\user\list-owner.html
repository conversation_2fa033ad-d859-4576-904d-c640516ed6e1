<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('业主管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								姓名：<input type="text" name="owner_name"/>
							</li>
							<li>
								手机号码：<input type="text" name="mobile"/>
							</li>
							<li>
								住户状态：<select name="is_live">
									<option value="">全部</option>
									<option value="1">已入住</option>
									<option value="0">未入住</option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="$.operate.add()">
		            <i class="fa fa-plus"></i> 新增住户
		        </a>
		        <a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
		            <i class="fa fa-remove"></i> 删除
		        </a>
		        <a class="btn btn-info ml-10" onclick="importData()">
		            <i class="fa fa-send"></i> 批量导入
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/owner";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "业主",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'owner_id',
                    title: '业主ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '姓名'
                },
				{
					field: 'gender',
					title: '性别',
					formatter: function(value, row, index) {
						if (value == 'M') return '男';
						else if (value == 'F') return '女';
						return '-';
					}
				},
                {
                    field: 'mobile',
                    title: '手机号'
                },{
					field: 'house_count',
                    title: '房屋数',
					formatter: function(value, row, index) {
						return value + '套';
					}
                },{
                    field: 'house_info',
                    title: '房屋信息'
                },
				{
					field: 'is_live',
					title: '入住状态',
					formatter: function(value, row, index) {
						if (value == '1') return '已入住';
						else if (value == '0') return '未入住';
						return '-';
					}
				},
				{
					field: 'role',
					title: '人员角色',
					formatter: function(value, row, index) {
						var roleDatas = [
							{ dictValue: "1", dictLabel: "业主" },
							{ dictValue: "2", dictLabel: "家庭成员" },
							{ dictValue: "3", dictLabel: "租户" }
						];
						return $.table.selectDictLabel(roleDatas, value) || '-';
					}
				},
                {
                    field: 'car_count',
                    title: '绑定车辆',
                    align: 'center',
					formatter: function(value, row, index) {
						if(value>0){
							return value + '辆';
						}else{
							return '-';
						}
					}
                },
				{
					field: 'parking_count',
					title: '绑定车位',
					formatter: function(value, row, index) {
						if(value>0){
							return value + '个';
						}else{
							return '-';
						}
					}
				},
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
					formatter: function(value, row, index) {
						return value == null ? '-' : value.substring(0, 10);
					}
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
						actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.owner_id + '\')"><i class="fa fa-search"></i> 查看详情</a> ');
						actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.owner_id + '\')"><i class="fa fa-edit"></i> 编辑</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.owner_id + '\')"><i class="fa fa-remove"></i> 删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function viewDetail(ownerId) {
            var url = prefix + "/detail/" + ownerId;
            $.modal.openTab("住户详情", url);
        }

        function importData() {
			$.modal.open("导入业主数据", prefix + "/importPage");
		}

		function viewHouse(ownerId) {
			$.modal.open("查看业主房屋", prefix + "/viewHousePage?ownerId=" + ownerId);
		}

		function viewCar(ownerId) {
			$.modal.open("查看业主车辆", prefix + "/viewCarPage?ownerId=" + ownerId);
		}

		function viewParking(ownerId) {
			$.modal.open("查看业主车位", prefix + "/viewParkingPage?ownerId=" + ownerId);
		}

    </script>
</body>
</html>