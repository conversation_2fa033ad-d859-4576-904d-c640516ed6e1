.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 40rpx;
}

.header {
  padding: 60rpx 0;
  text-align: center;
  background: linear-gradient(to bottom, #f8f8f8, #fff);
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 24rpx;
}

.title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.version {
  font-size: 24rpx;
  color: #999;
}

.content {
  padding: 0 30rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background: #07c160;
  border-radius: 4rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.contact-item {
  display: flex;
  margin-bottom: 16rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item .label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.contact-item .value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.footer {
  margin-top: 60rpx;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}

.footer-link {
  color: #07c160;
  display: inline-block;
}

.divider {
  margin: 0 20rpx;
  color: #ddd;
} 