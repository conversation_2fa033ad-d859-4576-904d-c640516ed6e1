<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>选择房屋</title>
    <th:block th:include="include :: header('选择房屋')" />
    <style>
        .select-table th, .select-table td { text-align: center; }
        .select-btn { background: #409EFF; color: #fff; border: none; border-radius: 4px; padding: 2px 12px; cursor: pointer; transition: background 0.2s; }
        .select-btn:hover { background: #66b1ff; }
        .search-bar { margin-bottom: 16px; }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="search-bar">
            <input type="text" id="keyword" class="form-control" style="width:200px;display:inline-block;" placeholder="房号/关键字">
            <button class="btn btn-primary" onclick="loadHouseList()">搜索</button>
        </div>
        <table class="table table-bordered select-table">
            <thead>
                <tr>
                    <th>房屋ID</th>
                    <th>房号</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="house-list">
                <tr><td colspan="3">加载中...</td></tr>
            </tbody>
        </table>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        function loadHouseList() {
            var keyword = $("#keyword").val();
            $.ajax({
                url: ctx + "oc/house/selectList",
                type: "post",
                data: {keyword: keyword},
                success: function(res) {
                    var list = res.data || [];
                    var html = '';
                    if(list.length === 0) {
                        html = '<tr><td colspan="3">暂无数据</td></tr>';
                    } else {
                        for(var i=0;i<list.length;i++) {
                            var row = list[i];
                            html += '<tr>' +
                                '<td>' + row.house_id + '</td>' +
                                '<td>' + (row.room||'') + '</td>' +
                                '<td><button class="select-btn" onclick="selectHouse(\'' + row.house_id + '\',\'' + (row.room||'') + '\')">选择</button></td>' +
                                '</tr>';
                        }
                    }
                    $("#house-list").html(html);
                },
                error: function() {
                    $("#house-list").html('<tr><td colspan="3">加载失败</td></tr>');
                }
            });
        }
        function selectHouse(houseId, houseName) {
            if(window.parent && window.parent.setHouse) {
                window.parent.setHouse(houseId, houseName);
            }
        }
        $(function(){
            loadHouseList();
            $("#keyword").on('keydown', function(e){ if(e.keyCode===13) loadHouseList(); });
        });
    </script>
</body>
</html> 