// app.js
import 'umtrack-wx';
import { getStateManager } from './utils/stateManager.js'

// 全局页面拦截器
const originalPage = Page
Page = function(options) {
  // 不需要登录检查的页面白名单
  const noAuthPages = [
    'pages/login/index',
    'pages/about/index',
    'pages/about/privacy',
    'pages/about/agreement'
  ]

  const originalOnLoad = options.onLoad || function() {}
  const originalOnShow = options.onShow || function() {}

  // 重写 onLoad，在页面加载时检查是否需要登录验证
  options.onLoad = function(query) {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    // 如果不在白名单中，进行登录检查
    if (!noAuthPages.includes(currentRoute)) {
      if (!this.checkLoginStatus()) return
    }

    originalOnLoad.call(this, query)
  }

  // 重写 onShow，在页面显示时检查登录状态
  options.onShow = function() {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    // 如果不在白名单中，进行登录检查
    if (!noAuthPages.includes(currentRoute)) {
      if (!this.checkLoginStatus()) return
    }

    originalOnShow.call(this)
  }

  // 为所有页面添加通用方法
  options.checkLoginStatus = function() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      // 检查登录状态
      if (!state.isLogin) {
        wx.navigateTo({
          url: '/pages/login/index'
        })
        return false
      }
      return true
    } catch (error) {
      console.error('[LoginCheck] 登录检查失败:', error)
      // 如果状态管理器出错，跳转到登录页
      wx.navigateTo({
        url: '/pages/login/index'
      })
      return false
    }
  }

  options.checkLoginAndAuth = function() {
    if (!this.checkLoginStatus()) return false

    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      if (!state.isHouseAuth) {
        wx.showModal({
          title: '提示',
          content: '请先完成房屋认证',
          showCancel: false,
          confirmText: '去认证',
          success: () => {
            wx.switchTab({ url: '/pages/house/index' })
          }
        })
        return false
      }
      return true
    } catch (error) {
      console.error('[AuthCheck] 认证检查失败:', error)
      return false
    }
  }

  options.refreshPageState = function() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      this.setData({
        isLogin: state.isLogin || false,
        hasBindPhone: state.hasBindPhone || false,
        isHouseAuth: state.isHouseAuth || false,
        userInfo: state.userInfo || null,
        ownerInfo: state.ownerInfo || null,
        communityInfo: state.communityInfo || null,
        houseInfo: state.houseInfo || null
      })
    } catch (error) {
      console.error('[RefreshState] 刷新页面状态失败:', error)
    }
  }

  return originalPage(options)
}

App({
  umengConfig: {
    appKey: '683725ec79267e0210735d8f',
    useOpenid: false,
    autoGetOpenid: true,
    debug: false, // 生产环境关闭调试
    uploadUserInfo: true
  },
  globalData: {
    baseUrl: 'https://xtx.vip.cpolar.cn' // 实际开发中替换为真实的接口地址
  },

  onLaunch() {
    // 初始化状态管理器，它会自动处理登录状态检查
    getStateManager()

    // 检查登录状态
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('wxUserInfo')

    if (token && userInfo) {
      // 验证token是否有效
      this.checkToken()
    }
  },

  // 验证token是否有效
  async checkToken() {
    try {
      // 获取存储的token和tokenUser
      const token = wx.getStorageSync('token')
      const tokenUser = wx.getStorageSync('tokenUser')

      if (!token || !tokenUser) {
        this.clearLoginState(false) // 启动时不显示提示
        return
      }

      const checkRes = await this.request({
        url: '/api/wx/auth/check',
        method: 'POST',
        data: { tokenUser, token }
      })

      if(checkRes.code === 0){
        // 使用状态管理器更新登录状态
        const stateManager = getStateManager()
        stateManager.setState({ isLogin: true })

        // 更新token（如果服务器返回了新的token）
        if(checkRes.token){
          wx.setStorageSync('token', checkRes.token);
        }

        // 更新tokenUser（如果服务器返回了新的tokenUser）
        if(checkRes.tokenUser){
          stateManager.setState({ tokenUser: checkRes.tokenUser })
          wx.setStorageSync('tokenUser', checkRes.tokenUser);
        }
      } else {
        this.clearLoginState(false); // 启动时不显示提示
      }
    } catch (error) {
      console.error('[Auth] Token验证失败:', error)

      // 网络错误时不清除登录状态，给用户重试机会
      if (this.isNetworkError(error)) {
        return;
      }

      // token无效，清除登录状态
      this.clearLoginState(false) // 启动时不显示提示
    }
  },

  // 清除登录状态
  clearLoginState(showMessage = true) {
    // 防抖处理，避免多次跳转登录页
    if (this._clearingLoginState) return;
    this._clearingLoginState = true;

    // 延长防抖时间，确保不会重复执行
    setTimeout(() => { this._clearingLoginState = false; }, 5000);

    // 使用状态管理器清除状态
    const stateManager = getStateManager()
    stateManager.clearState()

    // 检查当前页面，避免重复跳转
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';

    // 如果已经在登录页，不需要跳转
    if (currentRoute === 'pages/login/index') {
      return;
    }

    // 显示用户友好的提示信息
    if (showMessage) {
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000,
        success: () => {
          // 延迟跳转，让用户看到提示
          setTimeout(() => {
            this.navigateToLogin();
          }, 1000);
        }
      });
    } else {
      this.navigateToLogin();
    }
  },

  // 跳转到登录页
  navigateToLogin() {
    const pages = getCurrentPages();

    // 如果页面栈过深，使用 reLaunch 清空页面栈
    if (pages.length > 3) {
      wx.reLaunch({
        url: '/pages/login/index'
      });
    } else {
      // 否则使用 redirectTo 替换当前页面
      wx.redirectTo({
        url: '/pages/login/index'
      });
    }
  },

  // 网络请求封装
  request(options) {
    const baseUrl = this.globalData.baseUrl
    return new Promise((resolve, reject) => {
      // 获取存储的token
      const token = wx.getStorageSync('token')
      
      // 设置请求头
      const headers = {
        'Content-Type': 'application/json',
        ...options.header
      }
      
      // 如果有token，添加到请求头，并加上Bearer前缀
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      wx.request({
        ...options,
        url: `${baseUrl}${options.url}`,
        header: headers,
        success: (res) => {
          if (res.statusCode === 401) {
            this.clearLoginState(true);
            reject(new Error('登录已过期，请重新登录'));
            return;
          }
          if (res.statusCode === 200) {
            if (res.data.code === 401) {
              // token 失效，清除登录状态
              this.clearLoginState(true)
              reject(new Error('登录已过期，请重新登录'))
            } else {
              resolve(res.data)
            }
          } else {
            const errorMessage = this.getErrorMessage(res.statusCode, res.data)
            reject(new Error(errorMessage))
          }
        },
        fail(err) {
          console.error('[Network] 请求失败:', err)
          reject(new Error(err.errMsg || '网络错误'))
        }
      })
    })
  },

  // 检测是否为网络错误
  isNetworkError(error) {
    if (!error) return false;

    // 检查错误消息
    const errorMessage = error.message || error.errMsg || '';
    const networkKeywords = ['网络', 'network', 'timeout', 'fail', 'request:fail'];

    // 检查是否包含网络相关关键词
    const hasNetworkKeyword = networkKeywords.some(keyword =>
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    );

    // 检查错误码
    const networkErrorCodes = ['NETWORK_ERROR', 'TIMEOUT', 'CONNECTION_FAILED'];
    const hasNetworkCode = networkErrorCodes.includes(error.code);

    return hasNetworkKeyword || hasNetworkCode;
  },

  // 获取友好的错误信息
  getErrorMessage(statusCode, data) {
    const errorMap = {
      400: '请求参数错误',
      401: '登录已过期，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      500: '服务器繁忙，请稍后重试',
      502: '网关错误',
      503: '服务暂不可用'
    }

    return errorMap[statusCode] || data?.msg || '请求失败'
  }
})
