/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */
!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document){throw Error("jQuery requires a window with a document")}return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e,t,n){n=n||Te;var r,i,o=n.createElement("script");if(o.text=e,t){for(r in we){i=t[r]||t.getAttribute&&t.getAttribute(r),i&&o.setAttribute(r,i)}}n.head.appendChild(o).parentNode.removeChild(o)}function r(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?de[he.call(e)]||"object":typeof e}function i(e){var t=!!e&&"length" in e&&e.length,n=r(e);return xe(e)||be(e)?!1:"array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e}function o(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}function a(e,t){return t?"\x00"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}function s(e,t,n){return xe(t)?ke.grep(e,function(e,r){return !!t.call(e,r,e)!==n}):t.nodeType?ke.grep(e,function(e){return e===t!==n}):"string"!=typeof t?ke.grep(e,function(e){return pe.call(t,e)>-1!==n}):ke.filter(t,e,n)}function u(e,t){for(;(e=e[t])&&1!==e.nodeType;){}return e}function l(e){var t={};return ke.each(e.match(_e)||[],function(e,n){t[n]=!0}),t}function c(e){return e}function f(e){throw e}function p(e,t,n,r){var i;try{e&&xe(i=e.promise)?i.call(e).done(t).fail(n):e&&xe(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}function d(){Te.removeEventListener("DOMContentLoaded",d),e.removeEventListener("load",d),ke.ready()}function h(e,t){return t.toUpperCase()}function g(e){return e.replace(Ve,"ms-").replace(Ge,h)}function v(){this.expando=ke.expando+v.uid++}function m(e){return"true"===e?!0:"false"===e?!1:"null"===e?null:e===+e+""?+e:Ke.test(e)?JSON.parse(e):e}function y(e,t,n){var r;if(void 0===n&&1===e.nodeType){if(r="data-"+t.replace(Ze,"-$&").toLowerCase(),n=e.getAttribute(r),"string"==typeof n){try{n=m(n)}catch(i){}Je.set(e,t,n)}else{n=void 0}}return n}function x(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return ke.css(e,t,"")},u=s(),l=n&&n[3]||(ke.cssNumber[t]?"":"px"),c=e.nodeType&&(ke.cssNumber[t]||"px"!==l&&+u)&&tt.exec(ke.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;){ke.style(e,t,c+l),(1-o)*(1-(o=s()/u||0.5))<=0&&(a=0),c/=o}c=2*c,ke.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}function b(e){var t,n=e.ownerDocument,r=e.nodeName,i=st[r];return i?i:(t=n.body.appendChild(n.createElement(r)),i=ke.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),st[r]=i,i)}function T(e,t){for(var n,r,i=[],o=0,a=e.length;a>o;o++){r=e[o],r.style&&(n=r.style.display,t?("none"===n&&(i[o]=Qe.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&at(r)&&(i[o]=b(r))):"none"!==n&&(i[o]="none",Qe.set(r,"display",n)))}for(o=0;a>o;o++){null!=i[o]&&(e[o].style.display=i[o])}return e}function w(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&o(e,t)?ke.merge([e],n):n}function C(e,t){for(var n=0,r=e.length;r>n;n++){Qe.set(e[n],"globalEval",!t||Qe.get(t[n],"globalEval"))}}function S(e,t,n,i,o){for(var a,s,u,l,c,f,p=t.createDocumentFragment(),d=[],h=0,g=e.length;g>h;h++){if(a=e[h],a||0===a){if("object"===r(a)){ke.merge(d,a.nodeType?[a]:a)}else{if(pt.test(a)){for(s=s||p.appendChild(t.createElement("div")),u=(lt.exec(a)||["",""])[1].toLowerCase(),l=ft[u]||ft._default,s.innerHTML=l[1]+ke.htmlPrefilter(a)+l[2],f=l[0];f--;){s=s.lastChild}ke.merge(d,s.childNodes),s=p.firstChild,s.textContent=""}else{d.push(t.createTextNode(a))}}}}for(p.textContent="",h=0;a=d[h++];){if(i&&ke.inArray(a,i)>-1){o&&o.push(a)}else{if(c=it(a),s=w(p.appendChild(a),"script"),c&&C(s),n){for(f=0;a=s[f++];){ct.test(a.type||"")&&n.push(a)}}}}return p}function k(){return !0}function E(){return !1}function j(e,t,n,r,i,o){var a,s;if("object"==typeof t){"string"!=typeof n&&(r=r||n,n=void 0);for(s in t){j(e,s,n,r,t[s],o)}return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),i===!1){i=E}else{if(!i){return e}}return 1===o&&(a=i,i=function(e){return ke().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=ke.guid++)),e.each(function(){ke.event.add(this,t,i,r,n)})}function A(e,t,n){return n?(Qe.set(e,t,!1),void ke.event.add(e,t,{namespace:!1,handler:function(e){var n,r=Qe.get(this,t);if(1&e.isTrigger&&this[t]){if(r){(ke.event.special[t]||{}).delegateType&&e.stopPropagation()}else{if(r=le.call(arguments),Qe.set(this,t,r),this[t](),n=Qe.get(this,t),Qe.set(this,t,!1),r!==n){return e.stopImmediatePropagation(),e.preventDefault(),n}}}else{r&&(Qe.set(this,t,ke.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=k)}}})):void (void 0===Qe.get(e,t)&&ke.event.add(e,t,k))}function D(e,t){return o(e,"table")&&o(11!==t.nodeType?t:t.firstChild,"tr")?ke(e).children("tbody")[0]||e:e}function N(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function q(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function L(e,t){var n,r,i,o,a,s,u;if(1===t.nodeType){if(Qe.hasData(e)&&(o=Qe.get(e),u=o.events)){Qe.remove(t,"handle events");for(i in u){for(n=0,r=u[i].length;r>n;n++){ke.event.add(t,i,u[i][n])}}}Je.hasData(e)&&(a=Je.access(e),s=ke.extend({},a),Je.set(t,s))}}function H(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ut.test(e.type)?t.checked=e.checked:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}function O(e,t,r,i){t=ce(t);var o,a,s,u,l,c,f=0,p=e.length,d=p-1,h=t[0],g=xe(h);if(g||p>1&&"string"==typeof h&&!ye.checkClone&&gt.test(h)){return e.each(function(n){var o=e.eq(n);g&&(t[0]=h.call(this,n,o.html())),O(o,t,r,i)})}if(p&&(o=S(t,e[0].ownerDocument,!1,e,i),a=o.firstChild,1===o.childNodes.length&&(o=a),a||i)){for(s=ke.map(w(o,"script"),N),u=s.length;p>f;f++){l=o,f!==d&&(l=ke.clone(l,!0,!0),u&&ke.merge(s,w(l,"script"))),r.call(e[f],l,f)}if(u){for(c=s[s.length-1].ownerDocument,ke.map(s,q),f=0;u>f;f++){l=s[f],ct.test(l.type||"")&&!Qe.access(l,"globalEval")&&ke.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?ke._evalUrl&&!l.noModule&&ke._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):n(l.textContent.replace(vt,""),l,c))}}}return e}function P(e,t,n){for(var r,i=t?ke.filter(t,e):e,o=0;null!=(r=i[o]);o++){n||1!==r.nodeType||ke.cleanData(w(r)),r.parentNode&&(n&&it(r)&&C(w(r,"script")),r.parentNode.removeChild(r))}return e}function M(e,t,n){var r,i,o,a,s=yt.test(t),u=e.style;return n=n||xt(e),n&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(Ne,"$1")||void 0),""!==a||it(e)||(a=ke.style(e,t)),!ye.pixelBoxStyles()&&mt.test(a)&&Tt.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function R(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function I(e){for(var t=e[0].toUpperCase()+e.slice(1),n=wt.length;n--;){if(e=wt[n]+t,e in Ct){return e}}}function W(e){var t=ke.cssProps[e]||St[e];return t?t:e in Ct?e:St[e]=I(e)||e}function F(e,t,n){var r=tt.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function B(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content")){return 0}for(;4>a;a+=2){"margin"===n&&(l+=ke.css(e,n+nt[a],!0,i)),r?("content"===n&&(u-=ke.css(e,"padding"+nt[a],!0,i)),"margin"!==n&&(u-=ke.css(e,"border"+nt[a]+"Width",!0,i))):(u+=ke.css(e,"padding"+nt[a],!0,i),"padding"!==n?u+=ke.css(e,"border"+nt[a]+"Width",!0,i):s+=ke.css(e,"border"+nt[a]+"Width",!0,i))}return !r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-0.5))||0),u+l}function _(e,t,n){var r=xt(e),i=!ye.boxSizingReliable()||n,a=i&&"border-box"===ke.css(e,"boxSizing",!1,r),s=a,u=M(e,t,r),l="offset"+t[0].toUpperCase()+t.slice(1);if(mt.test(u)){if(!n){return u}u="auto"}return(!ye.boxSizingReliable()&&a||!ye.reliableTrDimensions()&&o(e,"tr")||"auto"===u||!parseFloat(u)&&"inline"===ke.css(e,"display",!1,r))&&e.getClientRects().length&&(a="border-box"===ke.css(e,"boxSizing",!1,r),s=l in e,s&&(u=e[l])),u=parseFloat(u)||0,u+B(e,t,n||(a?"border":"content"),s,r,u)+"px"}function z(e,t,n,r,i){return new z.prototype.init(e,t,n,r,i)}function X(){Dt&&(Te.hidden===!1&&e.requestAnimationFrame?e.requestAnimationFrame(X):e.setTimeout(X,ke.fx.interval),ke.fx.tick())}function U(){return e.setTimeout(function(){At=void 0}),At=Date.now()}function V(e,t){var n,r=0,i={height:e};for(t=t?1:0;4>r;r+=2-t){n=nt[r],i["margin"+n]=i["padding"+n]=e}return t&&(i.opacity=i.width=e),i}function G(e,t,n){for(var r,i=(J.tweeners[t]||[]).concat(J.tweeners["*"]),o=0,a=i.length;a>o;o++){if(r=i[o].call(n,t,e)){return r}}}function Y(e,t,n){var r,i,o,a,s,u,l,c,f="width" in t||"height" in t,p=this,d={},h=e.style,g=e.nodeType&&at(e),v=Qe.get(e,"fxshow");n.queue||(a=ke._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always(function(){p.always(function(){a.unqueued--,ke.queue(e,"fx").length||a.empty.fire()})}));for(r in t){if(i=t[r],Nt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r]){continue}g=!0}d[r]=v&&v[r]||ke.style(e,r)}}if(u=!ke.isEmptyObject(t),u||!ke.isEmptyObject(d)){f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],l=v&&v.display,null==l&&(l=Qe.get(e,"display")),c=ke.css(e,"display"),"none"===c&&(l?c=l:(T([e],!0),l=e.style.display||l,c=ke.css(e,"display"),T([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===ke.css(e,"float")&&(u||(p.done(function(){h.display=l}),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),u=!1;for(r in d){u||(v?"hidden" in v&&(g=v.hidden):v=Qe.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&T([e],!0),p.done(function(){g||T([e]),Qe.remove(e,"fxshow");for(r in d){ke.style(e,r,d[r])}})),u=G(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}}}function Q(e,t){var n,r,i,o,a;for(n in e){if(r=g(n),i=t[r],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),a=ke.cssHooks[r],a&&"expand" in a){o=a.expand(o),delete e[r];for(n in o){n in e||(e[n]=o[n],t[n]=i)}}else{t[r]=i}}}function J(e,t,n){var r,i,o=0,a=J.prefilters.length,s=ke.Deferred().always(function(){delete u.elem}),u=function(){if(i){return !1}for(var t=At||U(),n=Math.max(0,l.startTime+l.duration-t),r=n/l.duration||0,o=1-r,a=0,u=l.tweens.length;u>a;a++){l.tweens[a].run(o)}return s.notifyWith(e,[l,o,n]),1>o&&u?n:(u||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:ke.extend({},t),opts:ke.extend(!0,{specialEasing:{},easing:ke.easing._default},n),originalProperties:t,originalOptions:n,startTime:At||U(),duration:n.duration,tweens:[],createTween:function(t,n){var r=ke.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i){return this}for(i=!0;r>n;n++){l.tweens[n].run(1)}return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(Q(c,l.opts.specialEasing);a>o;o++){if(r=J.prefilters[o].call(l,e,c,l.opts)){return xe(r.stop)&&(ke._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r}}return ke.map(c,G,l),xe(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),ke.fx.timer(ke.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}function K(e){var t=e.match(_e)||[];return t.join(" ")}function Z(e){return e.getAttribute&&e.getAttribute("class")||""}function ee(e){return Array.isArray(e)?e:"string"==typeof e?e.match(_e)||[]:[]}function te(e,t,n,i){var o;if(Array.isArray(t)){ke.each(t,function(t,r){n||Bt.test(e)?i(e,r):te(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)})}else{if(n||"object"!==r(t)){i(e,t)}else{for(o in t){te(e+"["+o+"]",t[o],n,i)}}}}function ne(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(_e)||[];if(xe(n)){for(;r=o[i++];){"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}}}function re(e,t,n,r){function i(s){var u;return o[s]=!0,ke.each(e[s]||[],function(e,s){var l=s(t,n,r);return"string"!=typeof l||a||o[l]?a?!(u=l):void 0:(t.dataTypes.unshift(l),i(l),!1)}),u}var o={},a=e===en;return i(t.dataTypes[0])||!o["*"]&&i("*")}function ie(e,t){var n,r,i=ke.ajaxSettings.flatOptions||{};for(n in t){void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n])}return r&&ke.extend(!0,e,r),e}function oe(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];){u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"))}if(r){for(i in s){if(s[i]&&s[i].test(r)){u.unshift(i);break}}}if(u[0] in n){o=u[0]}else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}function ae(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1]){for(a in e.converters){l[a.toLowerCase()]=e.converters[a]}}for(o=c.shift();o;){if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift()){if("*"===o){o=u}else{if("*"!==u&&u!==o){if(a=l[u+" "+o]||l["* "+o],!a){for(i in l){if(s=i.split(" "),s[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){a===!0?a=l[i]:l[i]!==!0&&(o=s[0],c.unshift(s[1]));break}}}if(a!==!0){if(a&&e["throws"]){t=a(t)}else{try{t=a(t)}catch(f){return{state:"parsererror",error:a?f:"No conversion from "+u+" to "+o}}}}}}}}return{state:"success",data:t}}var se=[],ue=Object.getPrototypeOf,le=se.slice,ce=se.flat?function(e){return se.flat.call(e)}:function(e){return se.concat.apply([],e)},fe=se.push,pe=se.indexOf,de={},he=de.toString,ge=de.hasOwnProperty,ve=ge.toString,me=ve.call(Object),ye={},xe=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},be=function(e){return null!=e&&e===e.window},Te=e.document,we={type:!0,src:!0,nonce:!0,noModule:!0},Ce="3.7.1",Se=/HTML$/i,ke=function(e,t){return new ke.fn.init(e,t)};ke.fn=ke.prototype={jquery:Ce,constructor:ke,length:0,toArray:function(){return le.call(this)},get:function(e){return null==e?le.call(this):0>e?this[e+this.length]:this[e]},pushStack:function(e){var t=ke.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return ke.each(this,e)},map:function(e){return this.pushStack(ke.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(le.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(ke.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(ke.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:fe,sort:se.sort,splice:se.splice},ke.extend=ke.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||xe(a)||(a={}),s===u&&(a=this,s--);u>s;s++){if(null!=(e=arguments[s])){for(t in e){r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(ke.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||ke.isPlainObject(n)?n:{},i=!1,a[t]=ke.extend(l,o,r)):void 0!==r&&(a[t]=r))}}}return a},ke.extend({expando:"jQuery"+(Ce+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return e&&"[object Object]"===he.call(e)?(t=ue(e))?(n=ge.call(t,"constructor")&&t.constructor,"function"==typeof n&&ve.call(n)===me):!0:!1},isEmptyObject:function(e){var t;for(t in e){return !1}return !0},globalEval:function(e,t,r){n(e,{nonce:t&&t.nonce},r)},each:function(e,t){var n,r=0;if(i(e)){for(n=e.length;n>r&&t.call(e[r],r,e[r])!==!1;r++){}}else{for(r in e){if(t.call(e[r],r,e[r])===!1){break}}}return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i){for(;t=e[r++];){n+=ke.text(t)}}return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(i(Object(e))?ke.merge(n,"string"==typeof e?[e]:e):fe.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:pe.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return !Se.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;n>r;r++){e[i++]=t[r]}return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;a>o;o++){r=!t(e[o],o),r!==s&&i.push(e[o])}return i},map:function(e,t,n){var r,o,a=0,s=[];if(i(e)){for(r=e.length;r>a;a++){o=t(e[a],a,n),null!=o&&s.push(o)}}else{for(a in e){o=t(e[a],a,n),null!=o&&s.push(o)}}return ce(s)},guid:1,support:ye}),"function"==typeof Symbol&&(ke.fn[Symbol.iterator]=se[Symbol.iterator]),ke.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){de["[object "+t+"]"]=t.toLowerCase()});var Ee=se.pop,je=se.sort,Ae=se.splice,De="[\\x20\\t\\r\\n\\f]",Ne=RegExp("^"+De+"+|((?:^|[^\\\\])(?:\\\\.)*)"+De+"+$","g");ke.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var qe=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;ke.escapeSelector=function(e){return(e+"").replace(qe,a)};var Le=Te,He=fe;!function(){function t(){try{return N.activeElement}catch(e){}}function n(e,t,r,i){var o,a,s,u,l,c,d,v=t&&t.ownerDocument,m=t?t.nodeType:9;if(r=r||[],"string"!=typeof e||!e||1!==m&&9!==m&&11!==m){return r}if(!i&&(p(t),t=t||N,L)){if(11!==m&&(l=re.exec(e))){if(o=l[1]){if(9===m){if(!(s=t.getElementById(o))){return r}if(s.id===o){return P.call(r,s),r}}else{if(v&&(s=v.getElementById(o))&&n.contains(t,s)&&s.id===o){return P.call(r,s),r}}}else{if(l[2]){return P.apply(r,t.getElementsByTagName(e)),r}if((o=l[3])&&t.getElementsByClassName){return P.apply(r,t.getElementsByClassName(o)),r}}}if(!(B[e+" "]||H&&H.test(e))){if(d=e,v=t,1===m&&(J.test(e)||Q.test(e))){for(v=ie.test(e)&&f(t.parentNode)||t,v==t&&ye.scope||((u=t.getAttribute("id"))?u=ke.escapeSelector(u):t.setAttribute("id",u=M)),c=h(e),a=c.length;a--;){c[a]=(u?"#"+u:":scope")+" "+g(c[a])}d=c.join(",")}try{return P.apply(r,v.querySelectorAll(d)),r}catch(y){B(e,!0)}finally{u===M&&t.removeAttribute("id")}}}return S(e.replace(Ne,"$1"),t,r,i)}function r(){function e(n,r){return t.push(n+" ")>E.cacheLength&&delete e[t.shift()],e[n+" "]=r}var t=[];return e}function i(e){return e[M]=!0,e}function a(e){var t=N.createElement("fieldset");try{return !!e(t)}catch(n){return !1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function s(e){return function(t){return o(t,"input")&&t.type===e}}function u(e){return function(t){return(o(t,"input")||o(t,"button"))&&t.type===e}}function l(e){return function(t){return"form" in t?t.parentNode&&t.disabled===!1?"label" in t?"label" in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&ce(t)===e:t.disabled===e:"label" in t?t.disabled===e:!1}}function c(e){return i(function(t){return t=+t,i(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;){n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}})})}function f(e){return e&&void 0!==e.getElementsByTagName&&e}function p(e){var t,r=e?e.ownerDocument||e:Le;return r!=N&&9===r.nodeType&&r.documentElement?(N=r,q=N.documentElement,L=!ke.isXMLDoc(N),O=q.matches||q.webkitMatchesSelector||q.msMatchesSelector,q.msMatchesSelector&&Le!=N&&(t=N.defaultView)&&t.top!==t&&t.addEventListener("unload",ue),ye.getById=a(function(e){return q.appendChild(e).id=ke.expando,!N.getElementsByName||!N.getElementsByName(ke.expando).length}),ye.disconnectedMatch=a(function(e){return O.call(e,"*")}),ye.scope=a(function(){return N.querySelectorAll(":scope")}),ye.cssHas=a(function(){try{return N.querySelector(":has(*,:jqfake)"),!1}catch(e){return !0}}),ye.getById?(E.filter.ID=function(e){var t=e.replace(oe,ae);return function(e){return e.getAttribute("id")===t}},E.find.ID=function(e,t){if(void 0!==t.getElementById&&L){var n=t.getElementById(e);return n?[n]:[]}}):(E.filter.ID=function(e){var t=e.replace(oe,ae);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},E.find.ID=function(e,t){if(void 0!==t.getElementById&&L){var n,r,i,o=t.getElementById(e);if(o){if(n=o.getAttributeNode("id"),n&&n.value===e){return[o]}for(i=t.getElementsByName(e),r=0;o=i[r++];){if(n=o.getAttributeNode("id"),n&&n.value===e){return[o]}}}return[]}}),E.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},E.find.CLASS=function(e,t){return void 0!==t.getElementsByClassName&&L?t.getElementsByClassName(e):void 0},H=[],a(function(e){var t;q.appendChild(e).innerHTML="<a id='"+M+"' href='' disabled='disabled'></a><select id='"+M+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||H.push("\\["+De+"*(?:value|"+z+")"),e.querySelectorAll("[id~="+M+"-]").length||H.push("~="),e.querySelectorAll("a#"+M+"+*").length||H.push(".#.+[+~]"),e.querySelectorAll(":checked").length||H.push(":checked"),t=N.createElement("input"),t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),q.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&H.push(":enabled",":disabled"),t=N.createElement("input"),t.setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||H.push("\\["+De+"*name"+De+"*="+De+"*(?:''|\"\")")}),ye.cssHas||H.push(":has"),H=H.length&&RegExp(H.join("|")),_=function(e,t){if(e===t){return D=!0,0}var r=!e.compareDocumentPosition-!t.compareDocumentPosition;return r?r:(r=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&r||!ye.sortDetached&&t.compareDocumentPosition(e)===r?e===N||e.ownerDocument==Le&&n.contains(Le,e)?-1:t===N||t.ownerDocument==Le&&n.contains(Le,t)?1:A?pe.call(A,e)-pe.call(A,t):0:4&r?-1:1)},N):N}function d(){}function h(e,t){var r,i,o,a,s,u,l,c=F[e+" "];if(c){return t?0:c.slice(0)}for(s=e,u=[],l=E.preFilter;s;){(!r||(i=Y.exec(s)))&&(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),r=!1,(i=Q.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(Ne," ")}),s=s.slice(r.length));for(a in E.filter){!(i=ee[a].exec(s))||l[a]&&!(i=l[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length))}if(!r){break}}return t?s.length:s?n.error(e):F(e,u).slice(0)}function g(e){for(var t=0,n=e.length,r="";n>t;t++){r+=e[t].value}return r}function v(e,t,n){var r=t.dir,i=t.next,a=i||r,s=n&&"parentNode"===a,u=I++;return t.first?function(t,n,i){for(;t=t[r];){if(1===t.nodeType||s){return e(t,n,i)}}return !1}:function(t,n,l){var c,f,p=[R,u];if(l){for(;t=t[r];){if((1===t.nodeType||s)&&e(t,n,l)){return !0}}}else{for(;t=t[r];){if(1===t.nodeType||s){if(f=t[M]||(t[M]={}),i&&o(t,i)){t=t[r]||t}else{if((c=f[a])&&c[0]===R&&c[1]===u){return p[2]=c[2]}if(f[a]=p,p[2]=e(t,n,l)){return !0}}}}}return !1}}function m(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;){if(!e[i](t,n,r)){return !1}}return !0}:e[0]}function y(e,t,r){for(var i=0,o=t.length;o>i;i++){n(e,t[i],r)}return r}function x(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;u>s;s++){(o=e[s])&&(!n||n(o,r,i))&&(a.push(o),l&&t.push(s))}return a}function b(e,t,n,r,o,a){return r&&!r[M]&&(r=b(r)),o&&!o[M]&&(o=b(o,a)),i(function(i,a,s,u){var l,c,f,p,d=[],h=[],g=a.length,v=i||y(t||"*",s.nodeType?[s]:s,[]),m=!e||!i&&t?v:x(v,d,e,s,u);if(n?(p=o||(i?e:g||r)?[]:a,n(m,p,s,u)):p=m,r){for(l=x(p,h),r(l,[],s,u),c=l.length;c--;){(f=l[c])&&(p[h[c]]=!(m[h[c]]=f))}}if(i){if(o||e){if(o){for(l=[],c=p.length;c--;){(f=p[c])&&l.push(m[c]=f)}o(null,p=[],l,u)}for(c=p.length;c--;){(f=p[c])&&(l=o?pe.call(i,f):d[c])>-1&&(i[l]=!(a[l]=f))}}}else{p=x(p===a?p.splice(g,p.length):p),o?o(null,a,p,u):P.apply(a,p)}})}function T(e){for(var t,n,r,i=e.length,o=E.relative[e[0].type],a=o||E.relative[" "],s=o?1:0,u=v(function(e){return e===t},a,!0),l=v(function(e){return pe.call(t,e)>-1},a,!0),c=[function(e,n,r){var i=!o&&(r||n!=j)||((t=n).nodeType?u(e,n,r):l(e,n,r));return t=null,i}];i>s;s++){if(n=E.relative[e[s].type]){c=[v(m(c),n)]}else{if(n=E.filter[e[s].type].apply(null,e[s].matches),n[M]){for(r=++s;i>r&&!E.relative[e[r].type];r++){}return b(s>1&&m(c),s>1&&g(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(Ne,"$1"),n,r>s&&T(e.slice(s,r)),i>r&&T(e=e.slice(r)),i>r&&g(e))}c.push(n)}}return m(c)}function w(e,t){var n=t.length>0,r=e.length>0,o=function(i,o,a,s,u){var l,c,f,d=0,h="0",g=i&&[],v=[],m=j,y=i||r&&E.find.TAG("*",u),b=R+=null==m?1:Math.random()||0.1,T=y.length;for(u&&(j=o==N||o||u);h!==T&&null!=(l=y[h]);h++){if(r&&l){for(c=0,o||l.ownerDocument==N||(p(l),a=!L);f=e[c++];){if(f(l,o||N,a)){P.call(s,l);break}}u&&(R=b)}n&&((l=!f&&l)&&d--,i&&g.push(l))}if(d+=h,n&&h!==d){for(c=0;f=t[c++];){f(g,v,o,a)}if(i){if(d>0){for(;h--;){g[h]||v[h]||(v[h]=Ee.call(s))}}v=x(v)}P.apply(s,v),u&&!i&&v.length>0&&d+t.length>1&&ke.uniqueSort(s)}return u&&(R=b,j=m),g};return n?i(o):o}function C(e,t){var n,r=[],i=[],o=$[e+" "];if(!o){for(t||(t=h(e)),n=t.length;n--;){o=T(t[n]),o[M]?r.push(o):i.push(o)}o=$(e,w(i,r)),o.selector=e}return o}function S(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&h(e=l.selector||e);if(n=n||[],1===c.length){if(o=c[0]=c[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&9===t.nodeType&&L&&E.relative[o[1].type]){if(t=(E.find.ID(a.matches[0].replace(oe,ae),t)||[])[0],!t){return n}l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=ee.needsContext.test(e)?0:o.length;i--&&(a=o[i],!E.relative[s=a.type]);){if((u=E.find[s])&&(r=u(a.matches[0].replace(oe,ae),ie.test(o[0].type)&&f(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&g(o),!e){return P.apply(n,r),n}break}}}return(l||C(e,c))(r,t,!L,n,!t||ie.test(e)&&f(t.parentNode)||t),n}var k,E,j,A,D,N,q,L,H,O,P=He,M=ke.expando,R=0,I=0,W=r(),F=r(),$=r(),B=r(),_=function(e,t){return e===t&&(D=!0),0},z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",X="(?:\\\\[\\da-fA-F]{1,6}"+De+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\x00-\\x7f])+",U="\\["+De+"*("+X+")(?:"+De+"*([*^$|!~]?=)"+De+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+X+"))|)"+De+"*\\]",V=":("+X+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+U+")*)|.*)\\)|)",G=RegExp(De+"+","g"),Y=RegExp("^"+De+"*,"+De+"*"),Q=RegExp("^"+De+"*([>+~]|"+De+")"+De+"*"),J=RegExp(De+"|>"),K=RegExp(V),Z=RegExp("^"+X+"$"),ee={ID:RegExp("^#("+X+")"),CLASS:RegExp("^\\.("+X+")"),TAG:RegExp("^("+X+"|[*])"),ATTR:RegExp("^"+U),PSEUDO:RegExp("^"+V),CHILD:RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+De+"*(even|odd|(([+-]|)(\\d*)n|)"+De+"*(?:([+-]|)"+De+"*(\\d+)|))"+De+"*\\)|)","i"),bool:RegExp("^(?:"+z+")$","i"),needsContext:RegExp("^"+De+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+De+"*((?:-\\d)?\\d*)"+De+"*\\)|)(?=[^-]|$)","i")},te=/^(?:input|select|textarea|button)$/i,ne=/^h\d$/i,re=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ie=/[+~]/,oe=RegExp("\\\\[\\da-fA-F]{1,6}"+De+"?|\\\\([^\\r\\n\\f])","g"),ae=function(e,t){var n="0x"+e.slice(1)-65536;return t?t:0>n?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)},ue=function(){p()},ce=v(function(e){return e.disabled===!0&&o(e,"fieldset")},{dir:"parentNode",next:"legend"});try{P.apply(se=le.call(Le.childNodes),Le.childNodes),se[Le.childNodes.length].nodeType}catch(fe){P={apply:function(e,t){He.apply(e,le.call(t))},call:function(e){He.apply(e,le.call(arguments,1))}}}n.matches=function(e,t){return n(e,null,null,t)},n.matchesSelector=function(e,t){if(p(e),L&&!B[t+" "]&&(!H||!H.test(t))){try{var r=O.call(e,t);if(r||ye.disconnectedMatch||e.document&&11!==e.document.nodeType){return r}}catch(i){B(t,!0)}}return n(t,N,null,[e]).length>0},n.contains=function(e,t){return(e.ownerDocument||e)!=N&&p(e),ke.contains(e,t)},n.attr=function(e,t){(e.ownerDocument||e)!=N&&p(e);var n=E.attrHandle[t.toLowerCase()],r=n&&ge.call(E.attrHandle,t.toLowerCase())?n(e,t,!L):void 0;return void 0!==r?r:e.getAttribute(t)},n.error=function(e){throw Error("Syntax error, unrecognized expression: "+e)},ke.uniqueSort=function(e){var t,n=[],r=0,i=0;if(D=!ye.sortStable,A=!ye.sortStable&&le.call(e,0),je.call(e,_),D){for(;t=e[i++];){t===e[i]&&(r=n.push(i))}for(;r--;){Ae.call(e,n[r],1)}}return A=null,e},ke.fn.uniqueSort=function(){return this.pushStack(ke.uniqueSort(le.apply(this)))},E=ke.expr={cacheLength:50,createPseudo:i,match:ee,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(oe,ae),e[3]=(e[3]||e[4]||e[5]||"").replace(oe,ae),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||n.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&n.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return ee.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&K.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(oe,ae).toLowerCase();return"*"===e?function(){return !0}:function(e){return o(e,t)}},CLASS:function(e){var t=W[e+" "];return t||(t=RegExp("(^|"+De+")"+e+"("+De+"|$)"))&&W(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,r){return function(i){var o=n.attr(i,e);return null==o?"!="===t:t?(o+="","="===t?o===r:"!="===t?o!==r:"^="===t?r&&0===o.indexOf(r):"*="===t?r&&o.indexOf(r)>-1:"$="===t?r&&o.slice(-r.length)===r:"~="===t?(" "+o.replace(G," ")+" ").indexOf(r)>-1:"|="===t?o===r||o.slice(0,r.length+1)===r+"-":!1):!0}},CHILD:function(e,t,n,r,i){var a="nth"!==e.slice(0,3),s="last"!==e.slice(-4),u="of-type"===t;return 1===r&&0===i?function(e){return !!e.parentNode}:function(t,n,l){var c,f,p,d,h,g=a!==s?"nextSibling":"previousSibling",v=t.parentNode,m=u&&t.nodeName.toLowerCase(),y=!l&&!u,x=!1;if(v){if(a){for(;g;){for(p=t;p=p[g];){if(u?o(p,m):1===p.nodeType){return !1}}h=g="only"===e&&!h&&"nextSibling"}return !0}if(h=[s?v.firstChild:v.lastChild],s&&y){for(f=v[M]||(v[M]={}),c=f[e]||[],d=c[0]===R&&c[1],x=d&&c[2],p=d&&v.childNodes[d];p=++d&&p&&p[g]||(x=d=0)||h.pop();){if(1===p.nodeType&&++x&&p===t){f[e]=[R,d,x];break}}}else{if(y&&(f=t[M]||(t[M]={}),c=f[e]||[],d=c[0]===R&&c[1],x=d),x===!1){for(;(p=++d&&p&&p[g]||(x=d=0)||h.pop())&&((u?!o(p,m):1!==p.nodeType)||!++x||(y&&(f=p[M]||(p[M]={}),f[e]=[R,x]),p!==t));){}}}return x-=i,x===r||x%r===0&&x/r>=0}}},PSEUDO:function(e,t){var r,o=E.pseudos[e]||E.setFilters[e.toLowerCase()]||n.error("unsupported pseudo: "+e);return o[M]?o(t):o.length>1?(r=[e,e,"",t],E.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,n){for(var r,i=o(e,t),a=i.length;a--;){r=pe.call(e,i[a]),e[r]=!(n[r]=i[a])}}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=C(e.replace(Ne,"$1"));return r[M]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;){(o=a[s])&&(e[s]=!(t[s]=o))}}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:i(function(e){return function(t){return n(e,t).length>0}}),contains:i(function(e){return e=e.replace(oe,ae),function(t){return(t.textContent||ke.text(t)).indexOf(e)>-1}}),lang:i(function(e){return Z.test(e||"")||n.error("unsupported lang: "+e),e=e.replace(oe,ae).toLowerCase(),function(t){var n;do{if(n=L?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang")){return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}}while((t=t.parentNode)&&1===t.nodeType);return !1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===q},focus:function(e){return e===t()&&N.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:l(!1),disabled:l(!0),checked:function(e){return o(e,"input")&&!!e.checked||o(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling){if(e.nodeType<6){return !1}}return !0},parent:function(e){return !E.pseudos.empty(e)},header:function(e){return ne.test(e.nodeName)},input:function(e){return te.test(e.nodeName)},button:function(e){return o(e,"input")&&"button"===e.type||o(e,"button")},text:function(e){var t;return o(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[0>n?n+t:n]}),even:c(function(e,t){for(var n=0;t>n;n+=2){e.push(n)}return e}),odd:c(function(e,t){for(var n=1;t>n;n+=2){e.push(n)}return e}),lt:c(function(e,t,n){var r;for(r=0>n?n+t:n>t?t:n;--r>=0;){e.push(r)}return e}),gt:c(function(e,t,n){for(var r=0>n?n+t:n;++r<t;){e.push(r)}return e})}},E.pseudos.nth=E.pseudos.eq;for(k in {radio:!0,checkbox:!0,file:!0,password:!0,image:!0}){E.pseudos[k]=s(k)}for(k in {submit:!0,reset:!0}){E.pseudos[k]=u(k)}d.prototype=E.filters=E.pseudos,E.setFilters=new d,ye.sortStable=M.split("").sort(_).join("")===M,p(),ye.sortDetached=a(function(e){return 1&e.compareDocumentPosition(N.createElement("fieldset"))}),ke.find=n,ke.expr[":"]=ke.expr.pseudos,ke.unique=ke.uniqueSort,n.compile=C,n.select=S,n.setDocument=p,n.tokenize=h,n.escape=ke.escapeSelector,n.getText=ke.text,n.isXML=ke.isXMLDoc,n.selectors=ke.expr,n.support=ke.support,n.uniqueSort=ke.uniqueSort}();var Oe=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;){if(1===e.nodeType){if(i&&ke(e).is(n)){break}r.push(e)}}return r},Pe=function(e,t){for(var n=[];e;e=e.nextSibling){1===e.nodeType&&e!==t&&n.push(e)}return n},Me=ke.expr.match.needsContext,Re=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;ke.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?ke.find.matchesSelector(r,e)?[r]:[]:ke.find.matches(e,ke.grep(t,function(e){return 1===e.nodeType}))},ke.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e){return this.pushStack(ke(e).filter(function(){for(t=0;r>t;t++){if(ke.contains(i[t],this)){return !0}}}))}for(n=this.pushStack([]),t=0;r>t;t++){ke.find(e,i[t],n)}return r>1?ke.uniqueSort(n):n},filter:function(e){return this.pushStack(s(this,e||[],!1))},not:function(e){return this.pushStack(s(this,e||[],!0))},is:function(e){return !!s(this,"string"==typeof e&&Me.test(e)?ke(e):e||[],!1).length}});var Ie,We=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,Fe=ke.fn.init=function(e,t,n){var r,i;if(!e){return this}if(n=n||Ie,"string"==typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:We.exec(e),!r||!r[1]&&t){return !t||t.jquery?(t||n).find(e):this.constructor(t).find(e)}if(r[1]){if(t=t instanceof ke?t[0]:t,ke.merge(this,ke.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:Te,!0)),Re.test(r[1])&&ke.isPlainObject(t)){for(r in t){xe(this[r])?this[r](t[r]):this.attr(r,t[r])}}return this}return i=Te.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):xe(e)?void 0!==n.ready?n.ready(e):e(ke):ke.makeArray(e,this)};Fe.prototype=ke.fn,Ie=ke(Te);var $e=/^(?:parents|prev(?:Until|All))/,Be={children:!0,contents:!0,next:!0,prev:!0};ke.fn.extend({has:function(e){var t=ke(e,this),n=t.length;return this.filter(function(){for(var e=0;n>e;e++){if(ke.contains(this,t[e])){return !0}}})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&ke(e);if(!Me.test(e)){for(;i>r;r++){for(n=this[r];n&&n!==t;n=n.parentNode){if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&ke.find.matchesSelector(n,e))){o.push(n);break}}}}return this.pushStack(o.length>1?ke.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?pe.call(ke(e),this[0]):pe.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ke.uniqueSort(ke.merge(this.get(),ke(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ke.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return Oe(e,"parentNode")},parentsUntil:function(e,t,n){return Oe(e,"parentNode",n)},next:function(e){return u(e,"nextSibling")},prev:function(e){return u(e,"previousSibling")},nextAll:function(e){return Oe(e,"nextSibling")},prevAll:function(e){return Oe(e,"previousSibling")},nextUntil:function(e,t,n){return Oe(e,"nextSibling",n)},prevUntil:function(e,t,n){return Oe(e,"previousSibling",n)},siblings:function(e){return Pe((e.parentNode||{}).firstChild,e)},children:function(e){return Pe(e.firstChild)},contents:function(e){return null!=e.contentDocument&&ue(e.contentDocument)?e.contentDocument:(o(e,"template")&&(e=e.content||e),ke.merge([],e.childNodes))}},function(e,t){ke.fn[e]=function(n,r){var i=ke.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=ke.filter(r,i)),this.length>1&&(Be[e]||ke.uniqueSort(i),$e.test(e)&&i.reverse()),this.pushStack(i)}});var _e=/[^\x20\t\r\n\f]+/g;ke.Callbacks=function(e){e="string"==typeof e?l(e):ke.extend({},e);var t,n,i,o,a=[],s=[],u=-1,c=function(){for(o=o||e.once,i=t=!0;s.length;u=-1){for(n=s.shift();++u<a.length;){a[u].apply(n[0],n[1])===!1&&e.stopOnFalse&&(u=a.length,n=!1)}}e.memory||(n=!1),t=!1,o&&(a=n?[]:"")},f={add:function(){return a&&(n&&!t&&(u=a.length-1,s.push(n)),function i(t){ke.each(t,function(t,n){xe(n)?e.unique&&f.has(n)||a.push(n):n&&n.length&&"string"!==r(n)&&i(n)})}(arguments),n&&!t&&c()),this},remove:function(){return ke.each(arguments,function(e,t){for(var n;(n=ke.inArray(t,a,n))>-1;){a.splice(n,1),u>=n&&u--}}),this},has:function(e){return e?ke.inArray(e,a)>-1:a.length>0},empty:function(){return a&&(a=[]),this},disable:function(){return o=s=[],a=n="",this},disabled:function(){return !a},lock:function(){return o=s=[],n||t||(a=n=""),this},locked:function(){return !!o},fireWith:function(e,n){return o||(n=n||[],n=[e,n.slice?n.slice():n],s.push(n),t||c()),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return !!i}};return f},ke.extend({Deferred:function(t){var n=[["notify","progress",ke.Callbacks("memory"),ke.Callbacks("memory"),2],["resolve","done",ke.Callbacks("once memory"),ke.Callbacks("once memory"),0,"resolved"],["reject","fail",ke.Callbacks("once memory"),ke.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},"catch":function(e){return i.then(null,e)},pipe:function(){var e=arguments;return ke.Deferred(function(t){ke.each(n,function(n,r){var i=xe(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&xe(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(t,r,i){function o(t,n,r,i){return function(){var s=this,u=arguments,l=function(){var e,l;if(!(a>t)){if(e=r.apply(s,u),e===n.promise()){throw new TypeError("Thenable self-resolution")}l=e&&("object"==typeof e||"function"==typeof e)&&e.then,xe(l)?i?l.call(e,o(a,n,c,i),o(a,n,f,i)):(a++,l.call(e,o(a,n,c,i),o(a,n,f,i),o(a,n,c,n.notifyWith))):(r!==c&&(s=void 0,u=[e]),(i||n.resolveWith)(s,u))}},p=i?l:function(){try{l()}catch(e){ke.Deferred.exceptionHook&&ke.Deferred.exceptionHook(e,p.error),t+1>=a&&(r!==f&&(s=void 0,u=[e]),n.rejectWith(s,u))}};t?p():(ke.Deferred.getErrorHook?p.error=ke.Deferred.getErrorHook():ke.Deferred.getStackHook&&(p.error=ke.Deferred.getStackHook()),e.setTimeout(p))}}var a=0;return ke.Deferred(function(e){n[0][3].add(o(0,e,xe(i)?i:c,e.notifyWith)),n[1][3].add(o(0,e,xe(t)?t:c)),n[2][3].add(o(0,e,xe(r)?r:f))}).promise()},promise:function(e){return null!=e?ke.extend(e,i):i}},o={};return ke.each(n,function(e,t){var a=t[2],s=t[5];i[t[1]]=a.add,s&&a.add(function(){r=s},n[3-e][2].disable,n[3-e][3].disable,n[0][2].lock,n[0][3].lock),a.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=a.fireWith}),i.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=le.call(arguments),o=ke.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?le.call(arguments):n,--t||o.resolveWith(r,i)}};if(1>=t&&(p(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||xe(i[n]&&i[n].then))){return o.then()}for(;n--;){p(i[n],a(n),o.reject)}return o.promise()}});var ze=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;ke.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&ze.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},ke.readyException=function(t){e.setTimeout(function(){throw t})};var Xe=ke.Deferred();ke.fn.ready=function(e){return Xe.then(e)["catch"](function(e){ke.readyException(e)}),this},ke.extend({isReady:!1,readyWait:1,ready:function(e){(e===!0?--ke.readyWait:ke.isReady)||(ke.isReady=!0,e!==!0&&--ke.readyWait>0||Xe.resolveWith(Te,[ke]))}}),ke.ready.then=Xe.then,"complete"===Te.readyState||"loading"!==Te.readyState&&!Te.documentElement.doScroll?e.setTimeout(ke.ready):(Te.addEventListener("DOMContentLoaded",d),e.addEventListener("load",d));var Ue=function(e,t,n,i,o,a,s){var u=0,l=e.length,c=null==n;if("object"===r(n)){o=!0;for(u in n){Ue(e,t,u,n[u],!0,a,s)}}else{if(void 0!==i&&(o=!0,xe(i)||(s=!0),c&&(s?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(ke(e),n)})),t)){for(;l>u;u++){t(e[u],n,s?i:i.call(e[u],u,t(e[u],n)))}}}return o?e:c?t.call(e):l?t(e[0],n):a},Ve=/^-ms-/,Ge=/-([a-z])/g,Ye=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};v.uid=1,v.prototype={cache:function(e){var t=e[this.expando];return t||(t={},Ye(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t){i[g(t)]=n}else{for(r in t){i[g(r)]=t[r]}}return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][g(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(g):(t=g(t),t=t in r?[t]:t.match(_e)||[]),n=t.length;for(;n--;){delete r[t[n]]}}(void 0===t||ke.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!ke.isEmptyObject(t)}};var Qe=new v,Je=new v,Ke=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Ze=/[A-Z]/g;ke.extend({hasData:function(e){return Je.hasData(e)||Qe.hasData(e)},data:function(e,t,n){return Je.access(e,t,n)},removeData:function(e,t){Je.remove(e,t)},_data:function(e,t,n){return Qe.access(e,t,n)},_removeData:function(e,t){Qe.remove(e,t)}}),ke.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=Je.get(o),1===o.nodeType&&!Qe.get(o,"hasDataAttrs"))){for(n=a.length;n--;){a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=g(r.slice(5)),y(o,r,i[r])))}Qe.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){Je.set(this,e)}):Ue(this,function(t){var n;if(o&&void 0===t){if(n=Je.get(o,e),void 0!==n){return n}if(n=y(o,e),void 0!==n){return n}}else{this.each(function(){Je.set(this,e,t)})}},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){Je.remove(this,e)})}}),ke.extend({queue:function(e,t,n){var r;return e?(t=(t||"fx")+"queue",r=Qe.get(e,t),n&&(!r||Array.isArray(n)?r=Qe.access(e,t,ke.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=ke.queue(e,t),r=n.length,i=n.shift(),o=ke._queueHooks(e,t),a=function(){ke.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Qe.get(e,n)||Qe.access(e,n,{empty:ke.Callbacks("once memory").add(function(){Qe.remove(e,[t+"queue",n])})})}}),ke.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?ke.queue(this[0],e):void 0===t?this:this.each(function(){var n=ke.queue(this,e,t);ke._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&ke.dequeue(this,e)})},dequeue:function(e){return this.each(function(){ke.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=ke.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;){n=Qe.get(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s))}return s(),i.promise(t)}});var et=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,tt=RegExp("^(?:([+-])=|)("+et+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],rt=Te.documentElement,it=function(e){return ke.contains(e.ownerDocument,e)},ot={composed:!0};rt.getRootNode&&(it=function(e){return ke.contains(e.ownerDocument,e)||e.getRootNode(ot)===e.ownerDocument});var at=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&it(e)&&"none"===ke.css(e,"display")},st={};ke.fn.extend({show:function(){return T(this,!0)},hide:function(){return T(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){at(this)?ke(this).show():ke(this).hide()})}});var ut=/^(?:checkbox|radio)$/i,lt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ct=/^$|^module$|\/(?:java|ecma)script/i;!function(){var e=Te.createDocumentFragment(),t=e.appendChild(Te.createElement("div")),n=Te.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),ye.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",ye.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,t.innerHTML="<option></option>",ye.option=!!t.lastChild}();var ft={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ft.tbody=ft.tfoot=ft.colgroup=ft.caption=ft.thead,ft.th=ft.td,ye.option||(ft.optgroup=ft.option=[1,"<select multiple='multiple'>","</select>"]);var pt=/<|&#?\w+;/,dt=/^([^.]*)(?:\.(.+)|)/;ke.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=Qe.get(e);if(Ye(e)){for(n.handler&&(o=n,n=o.handler,i=o.selector),i&&ke.find.matchesSelector(rt,i),n.guid||(n.guid=ke.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(t){return void 0!==ke&&ke.event.triggered!==t.type?ke.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(_e)||[""],l=t.length;l--;){s=dt.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d&&(f=ke.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=ke.event.special[d]||{},c=ke.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&ke.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||(p=u[d]=[],p.delegateCount=0,f.setup&&f.setup.call(e,r,h,a)!==!1||e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),ke.event.global[d]=!0)}}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=Qe.hasData(e)&&Qe.get(e);if(v&&(u=v.events)){for(t=(t||"").match(_e)||[""],l=t.length;l--;){if(s=dt.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d){for(f=ke.event.special[d]||{},d=(r?f.delegateType:f.bindType)||d,p=u[d]||[],s=s[2]&&RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;o--;){c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c))}a&&!p.length&&(f.teardown&&f.teardown.call(e,h,v.handle)!==!1||ke.removeEvent(e,d,v.handle),delete u[d])}else{for(d in u){ke.event.remove(e,d+t[l],n,r,!0)}}}ke.isEmptyObject(u)&&Qe.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=Array(arguments.length),u=ke.event.fix(e),l=(Qe.get(this,"events")||Object.create(null))[u.type]||[],c=ke.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++){s[t]=arguments[t]}if(u.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,u)!==!1){for(a=ke.event.handlers.call(this,u,l),t=0;(i=a[t++])&&!u.isPropagationStopped();){for(u.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!u.isImmediatePropagationStopped();){(!u.rnamespace||o.namespace===!1||u.rnamespace.test(o.namespace))&&(u.handleObj=o,u.data=o.data,r=((ke.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s),void 0!==r&&(u.result=r)===!1&&(u.preventDefault(),u.stopPropagation()))}}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1)){for(;l!==this;l=l.parentNode||this){if(1===l.nodeType&&("click"!==e.type||l.disabled!==!0)){for(o=[],a={},n=0;u>n;n++){r=t[n],i=r.selector+" ",void 0===a[i]&&(a[i]=r.needsContext?ke(i,this).index(l)>-1:ke.find(i,this,null,[l]).length),a[i]&&o.push(r)}o.length&&s.push({elem:l,handlers:o})}}}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(ke.Event.prototype,e,{enumerable:!0,configurable:!0,get:xe(t)?function(){return this.originalEvent?t(this.originalEvent):void 0}:function(){return this.originalEvent?this.originalEvent[e]:void 0},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[ke.expando]?e:new ke.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return ut.test(t.type)&&t.click&&o(t,"input")&&A(t,"click",!0),!1},trigger:function(e){var t=this||e;return ut.test(t.type)&&t.click&&o(t,"input")&&A(t,"click"),!0},_default:function(e){var t=e.target;return ut.test(t.type)&&t.click&&o(t,"input")&&Qe.get(t,"click")||o(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},ke.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},ke.Event=function(e,t){return this instanceof ke.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?k:E,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&ke.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),void (this[ke.expando]=!0)):new ke.Event(e,t)},ke.Event.prototype={constructor:ke.Event,isDefaultPrevented:E,isPropagationStopped:E,isImmediatePropagationStopped:E,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=k,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=k,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=k,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},ke.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,"char":!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},ke.event.addProp),ke.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(Te.documentMode){var n=Qe.get(this,"handle"),r=ke.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else{ke.event.simulate(t,e.target,ke.event.fix(e))}}ke.event.special[e]={setup:function(){var r;return A(this,e,!0),Te.documentMode?(r=Qe.get(this,t),r||this.addEventListener(t,n),Qe.set(this,t,(r||0)+1),void 0):!1},trigger:function(){return A(this,e),!0},teardown:function(){var e;return Te.documentMode?(e=Qe.get(this,t)-1,void (e?Qe.set(this,t,e):(this.removeEventListener(t,n),Qe.remove(this,t)))):!1},_default:function(t){return Qe.get(t.target,e)},delegateType:t},ke.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=Te.documentMode?this:r,o=Qe.get(i,t);o||(Te.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),Qe.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=Te.documentMode?this:r,o=Qe.get(i,t)-1;o?Qe.set(i,t,o):(Te.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),Qe.remove(i,t))}}}),ke.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){ke.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return(!i||i!==r&&!ke.contains(r,i))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),ke.fn.extend({on:function(e,t,n,r){return j(this,e,t,n,r)},one:function(e,t,n,r){return j(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj){return r=e.handleObj,ke(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this}if("object"==typeof e){for(i in e){this.off(i,t,e[i])}return this}return(t===!1||"function"==typeof t)&&(n=t,t=void 0),n===!1&&(n=E),this.each(function(){ke.event.remove(this,e,n,t)})}});var ht=/<script|<style|<link/i,gt=/checked\s*(?:[^=]|=\s*.checked.)/i,vt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;ke.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),u=it(e);if(!(ye.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ke.isXMLDoc(e))){for(a=w(s),o=w(e),r=0,i=o.length;i>r;r++){H(o[r],a[r])}}if(t){if(n){for(o=o||w(e),a=a||w(s),r=0,i=o.length;i>r;r++){L(o[r],a[r])}}else{L(e,s)}}return a=w(s,"script"),a.length>0&&C(a,!u&&w(e,"script")),s},cleanData:function(e){for(var t,n,r,i=ke.event.special,o=0;void 0!==(n=e[o]);o++){if(Ye(n)){if(t=n[Qe.expando]){if(t.events){for(r in t.events){i[r]?ke.event.remove(n,r):ke.removeEvent(n,r,t.handle)}}n[Qe.expando]=void 0}n[Je.expando]&&(n[Je.expando]=void 0)}}}}),ke.fn.extend({detach:function(e){return P(this,e,!0)},remove:function(e){return P(this,e)},text:function(e){return Ue(this,function(e){return void 0===e?ke.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=e)})},null,e,arguments.length)},append:function(){return O(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=D(this,e);t.appendChild(e)}})},prepend:function(){return O(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=D(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return O(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return O(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){1===e.nodeType&&(ke.cleanData(w(e,!1)),e.textContent="")}return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return ke.clone(this,e,t)})},html:function(e){return Ue(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType){return t.innerHTML}if("string"==typeof e&&!ht.test(e)&&!ft[(lt.exec(e)||["",""])[1].toLowerCase()]){e=ke.htmlPrefilter(e);try{for(;r>n;n++){t=this[n]||{},1===t.nodeType&&(ke.cleanData(w(t,!1)),t.innerHTML=e)}t=0}catch(i){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return O(this,arguments,function(t){var n=this.parentNode;ke.inArray(this,e)<0&&(ke.cleanData(w(this)),n&&n.replaceChild(t,this))},e)}}),ke.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){ke.fn[e]=function(e){for(var n,r=[],i=ke(e),o=i.length-1,a=0;o>=a;a++){n=a===o?this:this.clone(!0),ke(i[a])[t](n),fe.apply(r,n.get())}return this.pushStack(r)}});var mt=RegExp("^("+et+")(?!px)[a-z%]+$","i"),yt=/^--/,xt=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},bt=function(e,t,n){var r,i,o={};for(i in t){o[i]=e.style[i],e.style[i]=t[i]}r=n.call(e);for(i in t){e.style[i]=o[i]}return r},Tt=RegExp(nt.join("|"),"i");!function(){function t(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",rt.appendChild(l).appendChild(c);var t=e.getComputedStyle(c);r="1%"!==t.top,u=12===n(t.marginLeft),c.style.right="60%",a=36===n(t.right),i=36===n(t.width),c.style.position="absolute",o=12===n(c.offsetWidth/3),rt.removeChild(l),c=null}}function n(e){return Math.round(parseFloat(e))}var r,i,o,a,s,u,l=Te.createElement("div"),c=Te.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",ye.clearCloneStyle="content-box"===c.style.backgroundClip,ke.extend(ye,{boxSizingReliable:function(){return t(),i},pixelBoxStyles:function(){return t(),a},pixelPosition:function(){return t(),r},reliableMarginLeft:function(){return t(),u},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,n,r,i;return null==s&&(t=Te.createElement("table"),n=Te.createElement("tr"),r=Te.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="box-sizing:content-box;border:1px solid",n.style.height="1px",r.style.height="9px",r.style.display="block",rt.appendChild(t).appendChild(n).appendChild(r),i=e.getComputedStyle(n),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===n.offsetHeight,rt.removeChild(t)),s}}))}();var wt=["Webkit","Moz","ms"],Ct=Te.createElement("div").style,St={},kt=/^(none|table(?!-c[ea]).+)/,Et={position:"absolute",visibility:"hidden",display:"block"},jt={letterSpacing:"0",fontWeight:"400"};ke.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=M(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=g(t),u=yt.test(t),l=e.style;return u||(t=W(s)),a=ke.cssHooks[t]||ke.cssHooks[s],void 0===n?a&&"get" in a&&void 0!==(i=a.get(e,!1,r))?i:l[t]:(o=typeof n,"string"===o&&(i=tt.exec(n))&&i[1]&&(n=x(e,t,i),o="number"),null!=n&&n===n&&("number"!==o||u||(n+=i&&i[3]||(ke.cssNumber[s]?"":"px")),ye.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set" in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n)),void 0)}},css:function(e,t,n,r){var i,o,a,s=g(t),u=yt.test(t);return u||(t=W(s)),a=ke.cssHooks[t]||ke.cssHooks[s],a&&"get" in a&&(i=a.get(e,!0,n)),void 0===i&&(i=M(e,t,r)),"normal"===i&&t in jt&&(i=jt[t]),""===n||n?(o=parseFloat(i),n===!0||isFinite(o)?o||0:i):i}}),ke.each(["height","width"],function(e,t){ke.cssHooks[t]={get:function(e,n,r){return n?!kt.test(ke.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?_(e,t,r):bt(e,Et,function(){return _(e,t,r)}):void 0},set:function(e,n,r){var i,o=xt(e),a=!ye.scrollboxSize()&&"absolute"===o.position,s=a||r,u=s&&"border-box"===ke.css(e,"boxSizing",!1,o),l=r?B(e,t,r,u,o):0;return u&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-B(e,t,"border",!1,o)-0.5)),l&&(i=tt.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=ke.css(e,t)),F(e,n,l)}}}),ke.cssHooks.marginLeft=R(ye.reliableMarginLeft,function(e,t){return t?(parseFloat(M(e,"marginLeft"))||e.getBoundingClientRect().left-bt(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px":void 0}),ke.each({margin:"",padding:"",border:"Width"},function(e,t){ke.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];4>r;r++){i[e+nt[r]+t]=o[r]||o[r-2]||o[0]}return i}},"margin"!==e&&(ke.cssHooks[e+t].set=F)}),ke.fn.extend({css:function(e,t){return Ue(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=xt(e),i=t.length;i>a;a++){o[t[a]]=ke.css(e,t[a],!1,r)}return o}return void 0!==n?ke.style(e,t,n):ke.css(e,t)},e,t,arguments.length>1)}}),ke.Tween=z,z.prototype={constructor:z,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||ke.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(ke.cssNumber[n]?"":"px")},cur:function(){var e=z.propHooks[this.prop];return e&&e.get?e.get(this):z.propHooks._default.get(this)},run:function(e){var t,n=z.propHooks[this.prop];return this.options.duration?this.pos=t=ke.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):z.propHooks._default.set(this),this}},z.prototype.init.prototype=z.prototype,z.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=ke.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){ke.fx.step[e.prop]?ke.fx.step[e.prop](e):1!==e.elem.nodeType||!ke.cssHooks[e.prop]&&null==e.elem.style[W(e.prop)]?e.elem[e.prop]=e.now:ke.style(e.elem,e.prop,e.now+e.unit)}}},z.propHooks.scrollTop=z.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ke.easing={linear:function(e){return e},swing:function(e){return 0.5-Math.cos(e*Math.PI)/2},_default:"swing"},ke.fx=z.prototype.init,ke.fx.step={};var At,Dt,Nt=/^(?:toggle|show|hide)$/,qt=/queueHooks$/;ke.Animation=ke.extend(J,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return x(n.elem,e,tt.exec(t),n),n}]},tweener:function(e,t){xe(e)?(t=e,e=["*"]):e=e.match(_e);for(var n,r=0,i=e.length;i>r;r++){n=e[r],J.tweeners[n]=J.tweeners[n]||[],J.tweeners[n].unshift(t)}},prefilters:[Y],prefilter:function(e,t){t?J.prefilters.unshift(e):J.prefilters.push(e)}}),ke.speed=function(e,t,n){var r=e&&"object"==typeof e?ke.extend({},e):{complete:n||!n&&t||xe(e)&&e,duration:e,easing:n&&t||t&&!xe(t)&&t};return ke.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in ke.fx.speeds?r.duration=ke.fx.speeds[r.duration]:r.duration=ke.fx.speeds._default),(null==r.queue||r.queue===!0)&&(r.queue="fx"),r.old=r.complete,r.complete=function(){xe(r.old)&&r.old.call(this),r.queue&&ke.dequeue(this,r.queue)},r},ke.fn.extend({fadeTo:function(e,t,n,r){return this.filter(at).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=ke.isEmptyObject(e),o=ke.speed(t,n,r),a=function(){var t=J(this,ke.extend({},e),o);(i||Qe.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=ke.timers,a=Qe.get(this);if(i){a[i]&&a[i].stop&&r(a[i])}else{for(i in a){a[i]&&a[i].stop&&qt.test(i)&&r(a[i])}}for(i=o.length;i--;){o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1))}(t||!n)&&ke.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=Qe.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=ke.timers,a=r?r.length:0;for(n.finish=!0,ke.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;){o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1))}for(t=0;a>t;t++){r[t]&&r[t].finish&&r[t].finish.call(this)}delete n.finish})}}),ke.each(["toggle","show","hide"],function(e,t){var n=ke.fn[t];ke.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(V(t,!0),e,r,i)}}),ke.each({slideDown:V("show"),slideUp:V("hide"),slideToggle:V("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){ke.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),ke.timers=[],ke.fx.tick=function(){var e,t=0,n=ke.timers;for(At=Date.now();t<n.length;t++){e=n[t],e()||n[t]!==e||n.splice(t--,1)}n.length||ke.fx.stop(),At=void 0},ke.fx.timer=function(e){ke.timers.push(e),ke.fx.start()},ke.fx.interval=13,ke.fx.start=function(){Dt||(Dt=!0,X())},ke.fx.stop=function(){Dt=null},ke.fx.speeds={slow:600,fast:200,_default:400},ke.fn.delay=function(t,n){return t=ke.fx?ke.fx.speeds[t]||t:t,n=n||"fx",this.queue(n,function(n,r){var i=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(i)}})},function(){var e=Te.createElement("input"),t=Te.createElement("select"),n=t.appendChild(Te.createElement("option"));e.type="checkbox",ye.checkOn=""!==e.value,ye.optSelected=n.selected,e=Te.createElement("input"),e.value="t",e.type="radio",ye.radioValue="t"===e.value}();var Lt,Ht=ke.expr.attrHandle;ke.fn.extend({attr:function(e,t){return Ue(this,ke.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){ke.removeAttr(this,e)})}}),ke.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o){return void 0===e.getAttribute?ke.prop(e,t,n):(1===o&&ke.isXMLDoc(e)||(i=ke.attrHooks[t.toLowerCase()]||(ke.expr.match.bool.test(t)?Lt:void 0)),void 0!==n?null===n?void ke.removeAttr(e,t):i&&"set" in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get" in i&&null!==(r=i.get(e,t))?r:(r=ke.find.attr(e,t),null==r?void 0:r))}},attrHooks:{type:{set:function(e,t){if(!ye.radioValue&&"radio"===t&&o(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(_e);if(i&&1===e.nodeType){for(;n=i[r++];){e.removeAttribute(n)}}}}),Lt={set:function(e,t,n){return t===!1?ke.removeAttr(e,n):e.setAttribute(n,n),n}},ke.each(ke.expr.match.bool.source.match(/\w+/g),function(e,t){var n=Ht[t]||ke.find.attr;Ht[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=Ht[a],Ht[a]=i,i=null!=n(e,t,r)?a:null,Ht[a]=o),i}});var Ot=/^(?:input|select|textarea|button)$/i,Pt=/^(?:a|area)$/i;ke.fn.extend({prop:function(e,t){return Ue(this,ke.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[ke.propFix[e]||e]})}}),ke.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o){return 1===o&&ke.isXMLDoc(e)||(t=ke.propFix[t]||t,i=ke.propHooks[t]),void 0!==n?i&&"set" in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get" in i&&null!==(r=i.get(e,t))?r:e[t]}},propHooks:{tabIndex:{get:function(e){var t=ke.find.attr(e,"tabindex");return t?parseInt(t,10):Ot.test(e.nodeName)||Pt.test(e.nodeName)&&e.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),ye.optSelected||(ke.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),ke.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ke.propFix[this.toLowerCase()]=this}),ke.fn.extend({addClass:function(e){var t,n,r,i,o,a;return xe(e)?this.each(function(t){ke(this).addClass(e.call(this,t,Z(this)))}):(t=ee(e),t.length?this.each(function(){if(r=Z(this),n=1===this.nodeType&&" "+K(r)+" "){for(o=0;o<t.length;o++){i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ")}a=K(n),r!==a&&this.setAttribute("class",a)}}):this)},removeClass:function(e){var t,n,r,i,o,a;return xe(e)?this.each(function(t){ke(this).removeClass(e.call(this,t,Z(this)))}):arguments.length?(t=ee(e),t.length?this.each(function(){if(r=Z(this),n=1===this.nodeType&&" "+K(r)+" "){for(o=0;o<t.length;o++){for(i=t[o];n.indexOf(" "+i+" ")>-1;){n=n.replace(" "+i+" "," ")}}a=K(n),r!==a&&this.setAttribute("class",a)}}):this):this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,a=typeof e,s="string"===a||Array.isArray(e);return xe(e)?this.each(function(n){ke(this).toggleClass(e.call(this,n,Z(this),t),t)}):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=ee(e),this.each(function(){if(s){for(o=ke(this),i=0;i<n.length;i++){r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r)}}else{(void 0===e||"boolean"===a)&&(r=Z(this),r&&Qe.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||e===!1?"":Qe.get(this,"__className__")||""))}}))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];){if(1===n.nodeType&&(" "+K(Z(n))+" ").indexOf(t)>-1){return !0}}return !1}});var Mt=/\r/g;ke.fn.extend({val:function(e){var t,n,r,i=this[0];if(arguments.length){return r=xe(e),this.each(function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,ke(this).val()):e,null==i?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=ke.map(i,function(e){return null==e?"":e+""})),t=ke.valHooks[this.type]||ke.valHooks[this.nodeName.toLowerCase()],t&&"set" in t&&void 0!==t.set(this,i,"value")||(this.value=i))})}if(i){return t=ke.valHooks[i.type]||ke.valHooks[i.nodeName.toLowerCase()],t&&"get" in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(Mt,""):null==n?"":n)}}}),ke.extend({valHooks:{option:{get:function(e){var t=ke.find.attr(e,"value");return null!=t?t:K(ke.text(e))}},select:{get:function(e){var t,n,r,i=e.options,a=e.selectedIndex,s="select-one"===e.type,u=s?null:[],l=s?a+1:i.length;for(r=0>a?l:s?a:0;l>r;r++){if(n=i[r],(n.selected||r===a)&&!n.disabled&&(!n.parentNode.disabled||!o(n.parentNode,"optgroup"))){if(t=ke(n).val(),s){return t}u.push(t)}}return u},set:function(e,t){for(var n,r,i=e.options,o=ke.makeArray(t),a=i.length;a--;){r=i[a],(r.selected=ke.inArray(ke.valHooks.option.get(r),o)>-1)&&(n=!0)}return n||(e.selectedIndex=-1),o}}}}),ke.each(["radio","checkbox"],function(){ke.valHooks[this]={set:function(e,t){return Array.isArray(t)?e.checked=ke.inArray(ke(e).val(),t)>-1:void 0}},ye.checkOn||(ke.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Rt=e.location,It={guid:Date.now()},Wt=/\?/;ke.parseXML=function(t){var n,r;if(!t||"string"!=typeof t){return null}try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(i){}return r=n&&n.getElementsByTagName("parsererror")[0],(!n||r)&&ke.error("Invalid XML: "+(r?ke.map(r.childNodes,function(e){return e.textContent}).join("\n"):t)),n};var Ft=/^(?:focusinfocus|focusoutblur)$/,$t=function(e){e.stopPropagation()};ke.extend(ke.event,{trigger:function(t,n,r,i){var o,a,s,u,l,c,f,p,d=[r||Te],h=ge.call(t,"type")?t.type:t,g=ge.call(t,"namespace")?t.namespace.split("."):[];if(a=p=s=r=r||Te,3!==r.nodeType&&8!==r.nodeType&&!Ft.test(h+ke.event.triggered)&&(h.indexOf(".")>-1&&(g=h.split("."),h=g.shift(),g.sort()),l=h.indexOf(":")<0&&"on"+h,t=t[ke.expando]?t:new ke.Event(h,"object"==typeof t&&t),t.isTrigger=i?2:3,t.namespace=g.join("."),t.rnamespace=t.namespace?RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:ke.makeArray(n,[t]),f=ke.event.special[h]||{},i||!f.trigger||f.trigger.apply(r,n)!==!1)){if(!i&&!f.noBubble&&!be(r)){for(u=f.delegateType||h,Ft.test(u+h)||(a=a.parentNode);a;a=a.parentNode){d.push(a),s=a}s===(r.ownerDocument||Te)&&d.push(s.defaultView||s.parentWindow||e)}for(o=0;(a=d[o++])&&!t.isPropagationStopped();){p=a,t.type=o>1?u:f.bindType||h,c=(Qe.get(a,"events")||Object.create(null))[t.type]&&Qe.get(a,"handle"),c&&c.apply(a,n),c=l&&a[l],c&&c.apply&&Ye(a)&&(t.result=c.apply(a,n),t.result===!1&&t.preventDefault())}return t.type=h,i||t.isDefaultPrevented()||f._default&&f._default.apply(d.pop(),n)!==!1||!Ye(r)||l&&xe(r[h])&&!be(r)&&(s=r[l],s&&(r[l]=null),ke.event.triggered=h,t.isPropagationStopped()&&p.addEventListener(h,$t),r[h](),t.isPropagationStopped()&&p.removeEventListener(h,$t),ke.event.triggered=void 0,s&&(r[l]=s)),t.result}},simulate:function(e,t,n){var r=ke.extend(new ke.Event,n,{type:e,isSimulated:!0});ke.event.trigger(r,null,t)}}),ke.fn.extend({trigger:function(e,t){return this.each(function(){ke.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?ke.event.trigger(e,t,n,!0):void 0}});var Bt=/\[\]$/,_t=/\r?\n/g,zt=/^(?:submit|button|image|reset|file)$/i,Xt=/^(?:input|select|textarea|keygen)/i;ke.param=function(e,t){var n,r=[],i=function(e,t){var n=xe(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e){return""}if(Array.isArray(e)||e.jquery&&!ke.isPlainObject(e)){ke.each(e,function(){i(this.name,this.value)})}else{for(n in e){te(n,e[n],t,i)}}return r.join("&")},ke.fn.extend({serialize:function(){var e=this.serializeArray(),t=$("input[type=radio],input[type=checkbox]",this),n={};return $.each(t,function(){n.hasOwnProperty(this.name)||0==$("input[name='"+this.name+"']:checked").length&&(n[this.name]="",e.push({name:this.name,value:""}))}),ke.param(e)},serializeArray:function(){return this.map(function(){var e=ke.prop(this,"elements");return e?ke.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ke(this).is(":disabled")&&Xt.test(this.nodeName)&&!zt.test(e)&&(this.checked||!ut.test(e))}).map(function(e,t){var n=ke(this).val();return null==n?null:Array.isArray(n)?ke.map(n,function(e){return{name:t.name,value:e.replace(_t,"\r\n")}}):{name:t.name,value:n.replace(_t,"\r\n")}}).get()}});var Ut=/%20/g,Vt=/#.*$/,Gt=/([?&])_=[^&]*/,Yt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Qt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Jt=/^(?:GET|HEAD)$/,Kt=/^\/\//,Zt={},en={},tn="*/".concat("*"),nn=Te.createElement("a");nn.href=Rt.href,ke.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Rt.href,type:"GET",isLocal:Qt.test(Rt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":tn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":ke.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?ie(ie(e,ke.ajaxSettings),t):ie(ke.ajaxSettings,e)},ajaxPrefilter:ne(Zt),ajaxTransport:ne(en),ajax:function(t,n){function r(t,n,r,s){var l,p,d,b,T,w=n;c||(c=!0,u&&e.clearTimeout(u),i=void 0,a=s||"",C.readyState=t>0?4:0,l=t>=200&&300>t||304===t,r&&(b=oe(h,C,r)),!l&&ke.inArray("script",h.dataTypes)>-1&&ke.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),b=ae(h,b,C,l),l?(h.ifModified&&(T=C.getResponseHeader("Last-Modified"),T&&(ke.lastModified[o]=T),T=C.getResponseHeader("etag"),T&&(ke.etag[o]=T)),204===t||"HEAD"===h.type?w="nocontent":304===t?w="notmodified":(w=b.state,p=b.data,d=b.error,l=!d)):(d=w,(t||!w)&&(w="error",0>t&&(t=0))),C.status=t,C.statusText=(n||w)+"",l?m.resolveWith(g,[p,w,C]):m.rejectWith(g,[C,w,d]),C.statusCode(x),x=void 0,f&&v.trigger(l?"ajaxSuccess":"ajaxError",[C,h,l?p:d]),y.fireWith(g,[C,w]),f&&(v.trigger("ajaxComplete",[C,h]),--ke.active||ke.event.trigger("ajaxStop")))}"object"==typeof t&&(n=t,t=void 0),n=n||{};var i,o,a,s,u,l,c,f,p,d,h=ke.ajaxSetup({},n),g=h.context||h,v=h.context&&(g.nodeType||g.jquery)?ke(g):ke.event,m=ke.Deferred(),y=ke.Callbacks("once memory"),x=h.statusCode||{},b={},T={},w="canceled",C={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s){for(s={};t=Yt.exec(a);){s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2])}}t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?a:null},setRequestHeader:function(e,t){return null==c&&(e=T[e.toLowerCase()]=T[e.toLowerCase()]||e,b[e]=t),this},overrideMimeType:function(e){return null==c&&(h.mimeType=e),this},statusCode:function(e){var t;if(e){if(c){C.always(e[C.status])}else{for(t in e){x[t]=[x[t],e[t]]}}}return this},abort:function(e){var t=e||w;return i&&i.abort(t),r(0,t),this}};if(m.promise(C),h.url=((t||h.url||Rt.href)+"").replace(Kt,Rt.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(_e)||[""],null==h.crossDomain){l=Te.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=nn.protocol+"//"+nn.host!=l.protocol+"//"+l.host}catch(S){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=ke.param(h.data,h.traditional)),re(Zt,h,n,C),c){return C}f=ke.event&&h.global,f&&0===ke.active++&&ke.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Jt.test(h.type),o=h.url.replace(Vt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Ut,"+")):(d=h.url.slice(o.length),h.data&&(h.processData||"string"==typeof h.data)&&(o+=(Wt.test(o)?"&":"?")+h.data,delete h.data),h.cache===!1&&(o=o.replace(Gt,"$1"),d=(Wt.test(o)?"&":"?")+"_="+It.guid+++d),h.url=o+d),h.ifModified&&(ke.lastModified[o]&&C.setRequestHeader("If-Modified-Since",ke.lastModified[o]),ke.etag[o]&&C.setRequestHeader("If-None-Match",ke.etag[o])),(h.data&&h.hasContent&&h.contentType!==!1||n.contentType)&&C.setRequestHeader("Content-Type",h.contentType),C.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+tn+"; q=0.01":""):h.accepts["*"]);for(p in h.headers){C.setRequestHeader(p,h.headers[p])}if(h.beforeSend&&(h.beforeSend.call(g,C,h)===!1||c)){return C.abort()}if(w="abort",y.add(h.complete),C.done(h.success),C.fail(h.error),i=re(en,h,n,C)){if(C.readyState=1,f&&v.trigger("ajaxSend",[C,h]),c){return C}h.async&&h.timeout>0&&(u=e.setTimeout(function(){C.abort("timeout")},h.timeout));try{c=!1,i.send(b,r)}catch(S){if(c){throw S}r(-1,S)}}else{r(-1,"No Transport")}return C},getJSON:function(e,t,n){return ke.get(e,t,n,"json")},getScript:function(e,t){return ke.get(e,void 0,t,"script")}}),ke.each(["get","post"],function(e,t){ke[t]=function(e,n,r,i){return xe(n)&&(i=i||r,r=n,n=void 0),ke.ajax(ke.extend({url:e,type:t,dataType:i,data:n,success:r},ke.isPlainObject(e)&&e))}}),ke.ajaxPrefilter(function(e){var t;for(t in e.headers){"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}}),ke._evalUrl=function(e,t,n){return ke.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){ke.globalEval(e,t,n)}})},ke.fn.extend({wrapAll:function(e){var t;return this[0]&&(xe(e)&&(e=e.call(this[0])),t=ke(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;){e=e.firstElementChild}return e}).append(this)),this},wrapInner:function(e){return xe(e)?this.each(function(t){ke(this).wrapInner(e.call(this,t))}):this.each(function(){var t=ke(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=xe(e);return this.each(function(n){ke(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){ke(this).replaceWith(this.childNodes)}),this}}),ke.expr.pseudos.hidden=function(e){return !ke.expr.pseudos.visible(e)},ke.expr.pseudos.visible=function(e){return !!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},ke.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(t){}};var rn={0:200,1223:204},on=ke.ajaxSettings.xhr();ye.cors=!!on&&"withCredentials" in on,ye.ajax=on=!!on,ke.ajaxTransport(function(t){var n,r;return ye.cors||on&&!t.crossDomain?{send:function(i,o){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields){for(a in t.xhrFields){s[a]=t.xhrFields[a]}}t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");for(a in i){s.setRequestHeader(a,i[a])}n=function(e){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(rn[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout(function(){n&&r()})},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(u){if(n){throw u}}},abort:function(){n&&n()}}:void 0}),ke.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),ke.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return ke.globalEval(e),e}}}),ke.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),ke.ajaxTransport("script",function(e){if(e.crossDomain||e.scriptAttrs){var t,n;return{send:function(r,i){t=ke("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),Te.head.appendChild(t[0])},abort:function(){n&&n()}}}});var an=[],sn=/(=)\?(?=&|$)|\?\?/;ke.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=an.pop()||ke.expando+"_"+It.guid++;return this[e]=!0,e}}),ke.ajaxPrefilter("json jsonp",function(t,n,r){var i,o,a,s=t.jsonp!==!1&&(sn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&sn.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(i=t.jsonpCallback=xe(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(sn,"$1"+i):t.jsonp!==!1&&(t.url+=(Wt.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return a||ke.error(i+" was not called"),a[0]},t.dataTypes[0]="json",o=e[i],e[i]=function(){a=arguments},r.always(function(){void 0===o?ke(e).removeProp(i):e[i]=o,t[i]&&(t.jsonpCallback=n.jsonpCallback,an.push(i)),a&&xe(o)&&o(a[0]),a=o=void 0}),"script"):void 0}),ye.createHTMLDocument=function(){var e=Te.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),ke.parseHTML=function(e,t,n){if("string"!=typeof e){return[]}"boolean"==typeof t&&(n=t,t=!1);var r,i,o;return t||(ye.createHTMLDocument?(t=Te.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=Te.location.href,t.head.appendChild(r)):t=Te),i=Re.exec(e),o=!n&&[],i?[t.createElement(i[1])]:(i=S([e],t,o),o&&o.length&&ke(o).remove(),ke.merge([],i.childNodes))},ke.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=K(e.slice(s)),e=e.slice(0,s)),xe(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),a.length>0&&ke.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(r?ke("<div>").append(ke.parseHTML(e)).find(r):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},ke.expr.pseudos.animated=function(e){return ke.grep(ke.timers,function(t){return e===t.elem}).length},ke.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l,c=ke.css(e,"position"),f=ke(e),p={};"static"===c&&(e.style.position="relative"),s=f.offset(),o=ke.css(e,"top"),u=ke.css(e,"left"),l=("absolute"===c||"fixed"===c)&&(o+u).indexOf("auto")>-1,l?(r=f.position(),a=r.top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),xe(t)&&(t=t.call(e,n,ke.extend({},s))),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+i),"using" in t?t.using.call(e,p):f.css(p)}},ke.fn.extend({offset:function(e){if(arguments.length){return void 0===e?this:this.each(function(t){ke.offset.setOffset(this,e,t)})}var t,n,r=this[0];if(r){return r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}}},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===ke.css(r,"position")){t=r.getBoundingClientRect()}else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===ke.css(e,"position");){e=e.parentNode}e&&e!==r&&1===e.nodeType&&(i=ke(e).offset(),i.top+=ke.css(e,"borderTopWidth",!0),i.left+=ke.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-ke.css(r,"marginTop",!0),left:t.left-i.left-ke.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===ke.css(e,"position");){e=e.offsetParent}return e||rt})}}),ke.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;ke.fn[e]=function(r){return Ue(this,function(e,r,i){var o;return be(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i?o?o[t]:e[r]:void (o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i)},e,r,arguments.length)}}),ke.each(["top","left"],function(e,t){ke.cssHooks[t]=R(ye.pixelPosition,function(e,n){return n?(n=M(e,t),mt.test(n)?ke(e).position()[t]+"px":n):void 0})}),ke.each({Height:"height",Width:"width"},function(e,t){ke.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){ke.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(i===!0||o===!0?"margin":"border");return Ue(this,function(t,n,i){var o;return be(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?ke.css(t,n,s):ke.style(t,n,i,s)},t,a?i:void 0,a)}})}),ke.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ke.fn[t]=function(e){return this.on(t,e)}}),ke.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),ke.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){ke.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}});var un=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;ke.proxy=function(e,t){var n,r,i;return"string"==typeof t&&(n=e[t],t=e,e=n),xe(e)?(r=le.call(arguments,2),i=function(){return e.apply(t||this,r.concat(le.call(arguments)))},i.guid=e.guid=e.guid||ke.guid++,i):void 0},ke.holdReady=function(e){e?ke.readyWait++:ke.ready(!0)},ke.isArray=Array.isArray,ke.parseJSON=JSON.parse,ke.nodeName=o,ke.isFunction=xe,ke.isWindow=be,ke.camelCase=g,ke.type=r,ke.now=Date.now,ke.isNumeric=function(e){var t=ke.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},ke.trim=function(e){return null==e?"":(e+"").replace(un,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return ke});var ln=e.jQuery,cn=e.$;return ke.noConflict=function(t){return e.$===ke&&(e.$=cn),t&&e.jQuery===ke&&(e.jQuery=ln),ke},void 0===t&&(e.jQuery=e.$=ke),ke});