<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费标准列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- 搜索区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>客户号：</label>
                            <input type="text" name="cust_id"/>
                        </li>
                        <li>
                            <label>操作员：</label>
                            <input type="text" name="user_id"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索
                            </a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置
                            </a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- 工具栏 -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary" onclick="syncData()">
                <i class="fa fa-refresh"></i> 同步流水
            </a>
        </div>

        <!-- 表格 -->
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/bankAccount";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            modalName: "银行账号",
            columns: [ {checkbox: true},
                {field: 'id', title: 'ID', visible: false},
                {field: 'bank_type', title: '银行类型'},
                {field: 'cust_id', title: '客户号'},
                {field: 'user_id', title: '用户号'},
                {field: 'acc_no', title: '账号'},
                {field: 'language', title: '语言'},
                {field: 'api_url', title: '接口地址'},
                {field: 'status', title: '状态', formatter: function (v) {
                        if (v == 0) return '<span class="badge badge-primary">正常</span>';
                        if (v == 1) return '<span class="badge badge-danger">停用</span>';
                        return v;
                    }},
                {field: 'remark', title: '备注'},
                {field: 'create_time', title: '创建时间'},
                {field: 'update_time', title: '更新时间'},
                {
                    title: '操作', align: 'center', 
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editAccount(' + row.id + ')"><i class="fa fa-edit"></i>编辑</a> ');                       
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function editAccount(id) {
        $.operate.edit(id);
    }

    function removeAccount(id) {
        $.operate.remove(id);
    }   

    function addAccount() {
        $.operate.add();
    }

    function syncData() {
       $.ajax({
        url: prefix + "/syncData",
        type: "POST",
        success: function (result) {
            console.log(result);
            if (result.code == 200) {
                $.alert.success(result.message);
            } else {
                $.alert.error(result.message);
            }
        },
        error: function (result) {
            $.alert.error(result.message);
        }
       });
    }
</script>
</body>
</html>