import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { getUserRoleName } from '../../utils/common.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    isLogin: false,
    userInfo: null,
    ownerInfo: null,
    communityInfo: null,
    isHouseAuth: false
  },

  onLoad() {
    this.initializePage()
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 3 })
    }
    this.refreshUserData()
  },

  // 初始化页面
  initializePage() {
    this.refreshUserData()
  },

  // 刷新用户数据
  refreshUserData() {
    this.refreshPageState()
  },

  // 导航方法
  goToHouseInfo() {
    if (!this.checkLoginStatus()) return
    
    wx.switchTab({
      url: '/pages/house/index'
    })
  },

  goToProfile() {
    if (!this.checkLoginStatus()) return
    
    wx.navigateTo({
      url: '/pages/profile/index'
    })
  },

  goToSuggestion() {
    if (!this.checkLoginStatus()) return
    
    wx.navigateTo({
      url: '/pages/complaint/complaint'
    })
  },

  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/index'
    })
  },

  // 退出登录
  handleLogout() {
    if (!this.checkLoginStatus()) return
    
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录
  async performLogout() {
    try {
      loadingManager.show('退出中...')
      
      // 调用退出登录接口
      try {
        await app.request({
          url: '/api/wx/auth/logout',
          method: 'POST'
        })
      } catch (error) {
        // 即使接口调用失败，也要清除本地状态
        console.warn('[Logout] 退出登录接口调用失败:', error)
      }
      
      // 清除状态管理器和本地存储（会自动同步到 globalData）
      stateManager.clearState()
      
      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
      // 跳转到登录页
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/index'
        })
      }, 1500)
      
    } catch (error) {
      handleError(error, '退出登录')
    } finally {
      loadingManager.hide()
    }
  },

  // 获取用户角色名称
  getUserRoleText() {
    const role = this.data.ownerInfo?.role
    return getUserRoleName(role)
  }
})