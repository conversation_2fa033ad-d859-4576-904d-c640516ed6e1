<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.UnitMapper">
    
    <resultMap type="com.ehome.oc.domain.Unit" id="UnitResult">
        <id property="unitId" column="unit_id"/>
        <result property="buildingId" column="building_id"/>
        <result property="buildingName" column="building_name"/>
        <result property="name" column="name"/>
        <result property="houseCount" column="house_count"/>
        <result property="houseArea" column="house_area"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectUnitVo">
        select unit_id, building_id, name, house_count, house_area, create_by, create_time, update_by, update_time, remark 
        from eh_unit
    </sql>

    <select id="selectUnitList" parameterType="com.ehome.oc.domain.Unit" resultMap="UnitResult">
        <include refid="selectUnitVo"/>
        <where>
            <if test="buildingId != null and buildingId != ''">
                AND building_id = #{buildingId}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
    
    <select id="selectUnitById" parameterType="String" resultMap="UnitResult">
        <include refid="selectUnitVo"/>
        where unit_id = #{unitId}
    </select>
    
    <select id="selectUnitsByBuildingId" parameterType="String" resultMap="UnitResult">
        <include refid="selectUnitVo"/>
        where building_id = #{buildingId}
    </select>
        
    <insert id="insertUnit" parameterType="com.ehome.oc.domain.Unit">
        insert into eh_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitId != null">unit_id,</if>
            <if test="buildingId != null">building_id,</if>
            <if test="name != null">name,</if>
            <if test="houseCount != null">house_count,</if>
            <if test="houseArea != null">house_area,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitId != null">#{unitId},</if>
            <if test="buildingId != null">#{buildingId},</if>
            <if test="name != null">#{name},</if>
            <if test="houseCount != null">#{houseCount},</if>
            <if test="houseArea != null">#{houseArea},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateUnit" parameterType="com.ehome.oc.domain.Unit">
        update eh_unit
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="houseCount != null">house_count = #{houseCount},</if>
            <if test="houseArea != null">house_area = #{houseArea},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where unit_id = #{unitId}
    </update>

    <delete id="deleteUnitById" parameterType="String">
        delete from eh_unit where unit_id = #{unitId}
    </delete>

    <delete id="deleteUnitByIds" parameterType="String">
        delete from eh_unit where unit_id in 
        <foreach item="unitId" collection="array" open="(" separator="," close=")">
            #{unitId}
        </foreach>
    </delete>
</mapper> 