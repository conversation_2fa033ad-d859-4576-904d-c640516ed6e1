.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 24rpx;
  box-sizing: border-box;
}

.form {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.form-group {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: #f7f8fa;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #999;
}

.form-tips {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 2rpx solid #f5f5f5;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}

.submit-btn {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: calc(24rpx + env(safe-area-inset-bottom));
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.submit-btn.disabled {
  opacity: 0.6;
}

.picker {
  background-color: #fff;
  padding: 24rpx;
  border-radius: 8rpx;
  border: 1px solid #e5e5e5;
}

.placeholder {
  color: #999;
} 