<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增单元')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-unit-add">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">单元名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="name" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">房屋数量：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="number" name="houseCount" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">建筑面积/㎡：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="number" name="houseArea">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/unit";
        
        $(function() {
            $('#form-unit-add').renderSelect({prefix:prefix});
        });

        $("#form-unit-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-unit-add').serialize());
            }
        }
    </script>
</body>
</html> 