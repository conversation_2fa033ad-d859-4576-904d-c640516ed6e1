package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.quartz.service.impl.CCBJobService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/oc/ccbTran")
public class CCBTranController extends BaseController {

    private static final String PREFIX = "oc/ccb/tran";

    // 注入 CCBJobService
    @Autowired
    private CCBJobService ccbJobService;

    @RequestMapping("/tranRecord")
    public String mgr(ModelMap mmap) {
        String monthId = getRequest().getParameter("monthId");
        if(StringUtils.isNotBlank(monthId)){
            mmap.put("beginDate", monthId + "-01");
            mmap.put("endDate", monthId + "-31");
        }
        return PREFIX + "/tran-record";
    }

    @RequestMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @RequestMapping("/edit")
    public String edit() {
        return PREFIX + "/edit";
    }

    @RequestMapping("/detail")
    public String detail() {
        return PREFIX + "/detail";
    }

    @RequestMapping("/monthData")
    public String monthData() {
        return PREFIX + "/tran-month";
    }

    @RequestMapping("/monthDataStat")
    @ResponseBody
    public TableDataInfo monthDataStat() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("FROM eh_tran_record WHERE 1 = 1");
        sql.append(getSysUser().getCommunityId(),"AND community_id = ?");
        sql.append("AND STATUS = 'published'");
        String beginMonth = params.getString("begin_month");
        if(StringUtils.isNotBlank(beginMonth)){
            sql.append(beginMonth,"AND tran_month >= ?");
        }else{
            sql.append(getDateBeforeDays(365).replaceAll("-","").substring(0,6),"AND tran_month >= ?");
        }
        String endMonth = params.getString("end_month");
        if(StringUtils.isNotBlank(endMonth)){
            sql.append(endMonth,"AND tran_month <= ?");
        }
        sql.append("GROUP BY tran_month");
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "SELECT tran_month,SUM(CASE WHEN direction = 'in' THEN amt ELSE 0 END) AS income,SUM(CASE WHEN direction = 'out' THEN amt ELSE 0 END) AS expense",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }
    
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_tran_record t1");
        sql.append("where 1=1");
        if(getSysUser().isAdmin()){
            sql.append(params.getString("community_id"), "and t1.community_id = ?");
        }else{
            sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?");
        }
        sql.appendLike(params.getString("plate_no"), "and t1.plate_no like ?");
        sql.appendLike(params.getString("acct_no"), "and t1.acct_no like ?");
        sql.appendLike(params.getString("tran_flow_no"), "and t1.tran_flow_no like ?");
        sql.append(params.getString("direction"), "and t1.direction = ?");
        sql.append(params.getString("status"), "and t1.status = ?");
        sql.append(params.getString("account_type_id"), "and t1.account_type_id = ?");
        String beginDate = params.getString("tran_date_start");
        if(StringUtils.isNotBlank(beginDate)){
            sql.append(beginDate.replaceAll("-", ""),"and t1.tran_date_id >= ?");
        }else{
            sql.append(getDateBeforeDays(60),"and t1.tran_date_id >= ?");
        }
        String endDate = params.getString("tran_date_end");
        if(StringUtils.isNotBlank(endDate)){
            sql.append(endDate.replaceAll("-", ""),"and t1.tran_date_id <= ?");
        }
        sql.append("order by t1.tran_datetime desc");
        return sql;
    }

    @PostMapping("/initDate")
    @ResponseBody
    public AjaxResult initDate() {
        return AjaxResult.success(null,getDateBeforeDays(60));
    }

    public static String getDateBeforeDays(int daysAgo) {
        LocalDate targetDate = LocalDate.now().minusDays(daysAgo);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return targetDate.format(formatter);
    }

    @PostMapping("/editActive")
    @ResponseBody
    public AjaxResult editActive() {
        JSONObject params = getParams();
        Record record = new Record();
        record.set("trck_no", params.getString("trck_no"));
        record.set("status", params.getString("status"));
        Db.update("eh_tran_record", "trck_no", record);
        return AjaxResult.success();
    }

    @PostMapping("/batchEditStatus")
    @ResponseBody
    public AjaxResult batchEditStatus() {
        JSONObject params = getParams();
        String trckNos = params.getString("trckNos");
        String[] trckNoArr = trckNos.split(",");
        if (trckNoArr.length == 0) {
            return AjaxResult.error("请选择要修改状态的数据");
        }
        for (String trckNo : trckNoArr) {
            Record record = new Record();
            record.set("trck_no", trckNo);
            record.set("status", params.getString("status"));
            Db.update("eh_tran_record", "trck_no", record);
        }
        return AjaxResult.success();
    }

    private String getDateIdBeforeDays(int daysAgo) {
        LocalDate targetDate = LocalDate.now().minusDays(daysAgo);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return targetDate.format(formatter);
    }

    @PostMapping("/syncData")
    @ResponseBody
    public AjaxResult syncData() {
        ccbJobService.syncBalance();
        JSONObject params = getParams();
        String beginDate = params.getString("tran_date_start");
        if(StringUtils.isNotBlank(beginDate)){
            beginDate = beginDate.replaceAll("-","");
        }else{
            beginDate = getDateIdBeforeDays(10);
        }
        String endDate = params.getString("tran_date_end");
        if(StringUtils.isNotBlank(endDate)){
            endDate = endDate.replaceAll("-","");
        }else{
            endDate = getDateIdBeforeDays(0);
        }
        int rowRecord = 0;
        try {
            rowRecord = ccbJobService.synCardTransDetailList(beginDate,endDate);
        } catch (Exception e) {
            logger.error("同步数据失败",e);
        }
        return AjaxResult.success(rowRecord+"条记录同步完成");
    }

    @PostMapping("/editRemark")
    @ResponseBody
    public AjaxResult editRemark() {
        JSONObject params = getParams();
        String trckNo = params.getString("trck_no");
        String remark = params.getString("remark");
        if (trckNo == null || trckNo.isEmpty()) {
            return AjaxResult.error("trck_no不能为空");
        }
        Record record = new Record();
        record.set("trck_no", trckNo);
        record.set("remark", remark);
        Db.update("eh_tran_record", "trck_no", record);
        return AjaxResult.success();
    }

    @PostMapping("/batchUpdateAccountType")
    @ResponseBody
    public AjaxResult batchUpdateAccountType() {
        JSONObject params = getParams();
        String trckNos = params.getString("trckNos");
        String accountTypeId = params.getString("account_type_id");
        String accountTypeName = params.getString("account_type_name");
        if (trckNos == null || trckNos.isEmpty() || accountTypeId == null || accountTypeName == null) {
            return AjaxResult.error("参数不能为空");
        }
        String[] trckNoArr = trckNos.split(",");
        for (String trckNo : trckNoArr) {
            Record record = new Record();
            record.set("trck_no", trckNo);
            record.set("account_type_id", accountTypeId);
            record.set("account_type_name", accountTypeName);
            Db.update("eh_tran_record", "trck_no", record);
        }
        return AjaxResult.success();
    }
}
