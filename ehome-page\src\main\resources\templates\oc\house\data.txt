{"code": 0, "msg": "", "data": {"list": [{"id": 1999017, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1A", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.78, "buildingSize": 234.78, "type": 1, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "houseStatus": 1, "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999017, "id": 328924, "name": "豫ABH8091", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999017, "id": 328925, "name": "豫CV9693", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999017, "id": 328926, "name": "豫A53D7C", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999017, "id": 1423850, "name": "王莹(18803815015)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999017, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": [{"id": 6203, "name": "叠加洋房"}], "attachmentList": null, "houseTypeName": "住宅", "houseStatusName": "已入住"}, {"id": 1999018, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1B", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.39, "buildingSize": 234.39, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999018, "id": 328927, "name": "豫AYY899", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999018, "id": 328928, "name": "豫A99569", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999018, "id": 1423851, "name": "张钢强(13014513333)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999018, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": [{"id": 6203, "name": "叠加洋房"}], "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999019, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1C", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.9, "buildingSize": 234.9, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999019, "id": 328929, "name": "豫AD652M", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999019, "id": 328930, "name": "豫A7K7Q7", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999019, "id": 1423853, "name": "曹伟程(13603712115)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999019, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999020, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1D", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.43, "buildingSize": 234.43, "createUid": 3311393348, "expand": "{\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"},\"direction\":\"\"}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 1, "parkList": null, "carList": [{"originId": 1999020, "id": 328931, "name": "豫AMA539", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999020, "id": 1423854, "name": "李华(13903830204)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999020, "id": 1423855, "name": "马书强(13903830204)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999020, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999021, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1E", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.43, "buildingSize": 234.43, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999021, "id": 328932, "name": "豫A5KP51", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999021, "id": 328933, "name": "豫VG2991", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999021, "id": 1423856, "name": "柴燕(15537110565)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999021, "id": 1423857, "name": "杨小点(13838260270)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999021, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999022, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1F", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.43, "buildingSize": 234.43, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999022, "id": 328934, "name": "豫A88K9T", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999022, "id": 328935, "name": "豫A92JJ2", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999022, "id": 328936, "name": "豫A51NP6", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999022, "id": 1423858, "name": "程杰(13523052609)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999022, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999023, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1G", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.43, "buildingSize": 234.43, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999023, "id": 328937, "name": "豫AF03965", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999023, "id": 328938, "name": "豫ADA8796", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999023, "id": 328939, "name": "豫A81DX8", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999023, "id": 1423860, "name": "宋建营(13903812191)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999023, "id": 1423861, "name": "韩建英(13603842666)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999023, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999024, "communityID": 10772, "communityName": "家和万世", "buildingID": 56063, "buildingName": "东区1号楼", "unitID": 86481, "unitName": "1单元", "name": "1H", "combinaName": "东区1号楼/1单元", "layer": 1, "usableSize": 234.43, "buildingSize": 234.43, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 0, "parkList": null, "carList": null, "userList": [{"originId": 1999024, "id": 1423862, "name": "师俊昌(13673388102)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999024, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999025, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2A", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999025, "id": 328940, "name": "豫VW9M68", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999025, "id": 328941, "name": "豫JWW521", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999025, "id": 1423863, "name": "郭金玲(15639058677)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999025, "id": 1423864, "name": "高晓林(15639058677)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999025, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999026, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2B", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999026, "id": 328942, "name": "豫AF52785", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999026, "id": 328943, "name": "豫A377KW", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999026, "id": 328944, "name": "豫N45210", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999026, "id": 1423865, "name": "程辉(13503707396)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999026, "id": 1423866, "name": "王洁(15890958208)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999026, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999027, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2C", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999027, "id": 328945, "name": "豫A9030B", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999027, "id": 328946, "name": "豫A686B8", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999027, "id": 1423867, "name": "牛淑彩(13703712892)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999027, "id": 1423868, "name": "邱甜甜(15038315363)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999027, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999028, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2D", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999028, "id": 328947, "name": "豫AG39670", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999028, "id": 328948, "name": "豫AAQ8037", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999028, "id": 1423869, "name": "孟亚歌(13663830818)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999028, "id": 1423870, "name": "王永伟(13138517777)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999028, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999029, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2E", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 3, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999029, "id": 328949, "name": "豫AAN5215", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999029, "id": 328950, "name": "豫AZ88X9", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999029, "id": 328951, "name": "豫AZ317G", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999029, "id": 1423871, "name": "任乔奥成(17737777233)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999029, "id": 1423872, "name": "乔杰(19937380666)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999029, "id": 1423873, "name": "任贵生(13903826700)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999029, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999030, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2F", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.54, "buildingSize": 232.54, "createUid": 3311393348, "expand": "{\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"},\"direction\":\"\"}", "communityId": 0, "houseID": 0, "userNum": 1, "chargeNum": 1, "parkNum": 0, "carNum": 0, "parkList": null, "carList": null, "userList": [{"originId": 1999030, "id": 1423874, "name": "冯爱云(18638530287)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999030, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999031, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2G", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999031, "id": 328952, "name": "豫VX7X71", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999031, "id": 328953, "name": "豫AAL1699", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999031, "id": 328954, "name": "豫A83PH8", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999031, "id": 1423875, "name": "路向贞(13526631680)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999031, "id": 1423876, "name": "路子咸(17634330120)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999031, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999032, "communityID": 10772, "communityName": "家和万世", "buildingID": 56064, "buildingName": "东区2号楼", "unitID": 86482, "unitName": "1单元", "name": "2H", "combinaName": "东区2号楼/1单元", "layer": 1, "usableSize": 232.47, "buildingSize": 232.47, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999032, "id": 328955, "name": "豫ALX061", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999032, "id": 328956, "name": "京Q9TP70", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999032, "id": 1423877, "name": "徐涛(13838110002)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999032, "id": 1423878, "name": "<PERSON>(13903840002)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999032, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999033, "communityID": 10772, "communityName": "家和万世", "buildingID": 56065, "buildingName": "东区3号楼", "unitID": 86483, "unitName": "1单元", "name": "3A", "combinaName": "东区3号楼/1单元", "layer": 1, "usableSize": 232.05, "buildingSize": 232.05, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999033, "id": 328957, "name": "豫A095SR", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999033, "id": 328958, "name": "豫AF57688", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999033, "id": 1423879, "name": "王琨璋(13903811818)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999033, "id": 1423880, "name": "韩丽(15690880299)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999033, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999034, "communityID": 10772, "communityName": "家和万世", "buildingID": 56065, "buildingName": "东区3号楼", "unitID": 86483, "unitName": "1单元", "name": "3B", "combinaName": "东区3号楼/1单元", "layer": 1, "usableSize": 232.05, "buildingSize": 232.05, "createUid": 3311393348, "expand": "{\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"},\"direction\":\"\"}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 2, "parkList": null, "carList": [{"originId": 1999034, "id": 328959, "name": "豫A5S933", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999034, "id": 328960, "name": "豫A13976", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999034, "id": 1423881, "name": "刘传福(13683806777)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999034, "id": 1423882, "name": "任淑荣(13526830956)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999034, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999035, "communityID": 10772, "communityName": "家和万世", "buildingID": 56065, "buildingName": "东区3号楼", "unitID": 86483, "unitName": "1单元", "name": "3C", "combinaName": "东区3号楼/1单元", "layer": 1, "usableSize": 232.05, "buildingSize": 232.05, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999035, "id": 328961, "name": "豫AU357S", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999035, "id": 328962, "name": "豫ACB328", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999035, "id": 328963, "name": "豫ADP1022", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999035, "id": 1423883, "name": "郭炜(13838019466)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999035, "id": 1423884, "name": "刘玉敏(13838019466)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999035, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}, {"id": 1999036, "communityID": 10772, "communityName": "家和万世", "buildingID": 56065, "buildingName": "东区3号楼", "unitID": 86483, "unitName": "1单元", "name": "3D", "combinaName": "东区3号楼/1单元", "layer": 1, "usableSize": 232.02, "buildingSize": 232.02, "createUid": 3311393348, "expand": "{\"direction\":\"\",\"model\":{\"room\":\"\",\"office\":\"\",\"guard\":\"\"}}", "communityId": 0, "houseID": 0, "userNum": 2, "chargeNum": 1, "parkNum": 0, "carNum": 3, "parkList": null, "carList": [{"originId": 1999036, "id": 328964, "name": "豫V33FC9", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999036, "id": 328965, "name": "豫AAN2193", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999036, "id": 328966, "name": "豫RZ6368", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "userList": [{"originId": 1999036, "id": 1423885, "name": "孙征(13810869497)", "itemType": 1, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}, {"originId": 1999036, "id": 1423886, "name": "孙光禄(13922314658)", "itemType": 2, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "", "startTime": 0, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 0, "periodNum": 0}], "chargeItemList": [{"originId": 1999036, "id": 139863, "name": "联排别墅物业费", "itemType": 0, "licence": "", "parkName": "", "ownerName": "", "ownerPhone": "", "chargeItemInfo": "收费ID：139863\n类型：周期性收费\n计算精度：元（不保留小数），向上取整\n收费方式：按月收费,不足一月:按天收费\n金额计算方式：单价*计量方式:\n建筑面积*1.28\n账单生成日期：账期本月1号\n违约金：无\n备注：", "startTime": 1735660800, "endTime": 0, "communityName": "", "naturalPeriod": 0, "periodType": 101, "periodNum": 1}], "disable": false, "ExpandInfo": {"handingTime": 0, "direction": "", "model": {"room": "", "office": "", "guard": ""}}, "tagList": null, "attachmentList": null, "houseTypeName": "-", "houseStatusName": "-"}], "hasMore": true, "total": 541, "noCreateBillTotal": 0, "blockIndex": "", "maxAccessible": 0, "statusData": null, "tabNameInfo": {"communityNumName": "", "houseNumName": "", "houseSizeName": "", "userNumName": "", "parkNumName": "", "carNumName": ""}, "communityName": ""}}