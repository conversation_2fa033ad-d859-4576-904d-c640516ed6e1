<view class="container">
  <!-- 顶部封面和名称 -->
  <view class="cover-section">
    <image class="cover-img" src="{{communityInfo.communityBanner}}" mode="aspectFill"></image>
    <view class="community-name">{{communityInfo.communityName}}</view>
  </view>

  <!-- 基本信息 -->
  <view class="info-card">
    <view class="info-row">
      <text class="info-label">地址：</text>
      <text class="info-value">{{communityInfo.address}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">物业公司：</text>
      <text class="info-value">{{communityInfo.propertyCompany}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">服务电话：</text>
      <text class="info-value phone" bindtap="callServicePhone">{{communityInfo.servicePhone}}</text>
    </view>
    <view class="info-row">
      <text class="info-label">办公时间：</text>
      <text class="info-value">{{communityInfo.officeHours}}</text>
    </view>
  </view>

  <!-- 简介 -->
  <view class="desc-card">
    <view class="desc-title">小区简介</view>
    <view class="desc-content">{{communityInfo.intro}}</view>
  </view>

  <!-- 配套设施 -->
  <view class="facility-card" wx:if="{{communityInfo.facilities && communityInfo.facilities.length}}">
    <view class="facility-title">配套设施</view>
    <view class="facility-list">
      <view class="facility-item" wx:for="{{communityInfo.facilities}}" wx:key="name">
        <image class="facility-icon" src="{{item.icon}}" mode="aspectFit"></image>
        <text class="facility-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 其他入口 -->
  <view class="other-card">
    <button class="other-btn" bindtap="goToNoticeList">查看公告栏</button>
    <button class="other-btn" bindtap="openMap">查看小区地图</button>
  </view>
</view> 