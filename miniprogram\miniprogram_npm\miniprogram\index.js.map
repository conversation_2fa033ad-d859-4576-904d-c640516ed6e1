{"version": 3, "sources": [".eslintrc.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*\n * Eslint config file\n * Documentation: https://eslint.org/docs/user-guide/configuring/\n * Install the Eslint extension before using this feature.\n */\nmodule.exports = {\n  env: {\n    es6: true,\n    browser: true,\n    node: true,\n  },\n  ecmaFeatures: {\n    modules: true,\n  },\n  parserOptions: {\n    ecmaVersion: 2018,\n    sourceType: 'module',\n  },\n  globals: {\n    wx: true,\n    App: true,\n    Page: true,\n    getCurrentPages: true,\n    getApp: true,\n    Component: true,\n    requirePlugin: true,\n    requireMiniProgram: true,\n  },\n  // extends: 'eslint:recommended',\n  rules: {},\n}\n"]}