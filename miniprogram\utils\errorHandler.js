// 错误处理工具
import { ERROR_MESSAGES, API_CODES } from '../constants/index.js'

/**
 * 统一错误处理函数
 * @param {Error} error 错误对象
 * @param {string} context 错误上下文
 * @param {boolean} showToast 是否显示错误提示
 */
export function handleError(error, context = '', showToast = true) {
  console.error(`[${context}] Error:`, error)
  
  const message = getErrorMessage(error)
  
  if (showToast) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }
  
  return message
}

/**
 * 获取友好的错误信息
 * @param {Error|Object} error 错误对象
 * @returns {string} 友好的错误信息
 */
export function getErrorMessage(error) {
  if (!error) return ERROR_MESSAGES.OPERATION_FAILED
  
  // 如果是字符串，直接返回
  if (typeof error === 'string') {
    return error
  }
  
  // 如果有code字段，优先使用code映射
  if (error.code !== undefined) {
    const codeMessages = {
      [API_CODES.UNAUTHORIZED]: ERROR_MESSAGES.LOGIN_EXPIRED,
      [API_CODES.FORBIDDEN]: ERROR_MESSAGES.PERMISSION_DENIED,
      [API_CODES.SERVER_ERROR]: ERROR_MESSAGES.SERVER_BUSY,
      401: ERROR_MESSAGES.LOGIN_EXPIRED,
      403: ERROR_MESSAGES.PERMISSION_DENIED,
      500: ERROR_MESSAGES.SERVER_BUSY
    }
    
    if (codeMessages[error.code]) {
      return codeMessages[error.code]
    }
  }
  
  // 如果有message字段
  if (error.message) {
    // 网络相关错误
    if (error.message.includes('网络') || error.message.includes('network')) {
      return ERROR_MESSAGES.NETWORK_ERROR
    }
    
    // 登录相关错误
    if (error.message.includes('登录') || error.message.includes('token')) {
      return ERROR_MESSAGES.LOGIN_EXPIRED
    }
    
    return error.message
  }
  
  // 如果有msg字段（API返回）
  if (error.msg) {
    return error.msg
  }
  
  // 如果有errMsg字段（微信API返回）
  if (error.errMsg) {
    return translateWxError(error.errMsg)
  }
  
  return ERROR_MESSAGES.OPERATION_FAILED
}

/**
 * 翻译微信API错误信息
 * @param {string} errMsg 微信错误信息
 * @returns {string} 友好的错误信息
 */
function translateWxError(errMsg) {
  const wxErrorMap = {
    'request:fail': ERROR_MESSAGES.NETWORK_ERROR,
    'request:fail timeout': '请求超时，请检查网络连接',
    'getUserProfile:fail auth deny': '用户拒绝授权',
    'getPhoneNumber:fail auth deny': '用户拒绝授权手机号',
    'chooseImage:fail auth deny': '用户拒绝授权相册',
    'uploadFile:fail': '文件上传失败',
    'downloadFile:fail': '文件下载失败'
  }
  
  for (const key in wxErrorMap) {
    if (errMsg.includes(key)) {
      return wxErrorMap[key]
    }
  }
  
  return errMsg
}

/**
 * 网络请求错误处理
 * @param {Error} error 网络错误
 * @param {string} context 请求上下文
 */
export function handleNetworkError(error, context = 'Network') {
  console.error(`[${context}] 网络请求失败:`, error)
  
  let message = ERROR_MESSAGES.NETWORK_ERROR
  
  if (error.message) {
    if (error.message.includes('timeout')) {
      message = '请求超时，请重试'
    } else if (error.message.includes('401')) {
      message = ERROR_MESSAGES.LOGIN_EXPIRED
      // 可以在这里触发重新登录逻辑
      handleLoginExpired()
    } else if (error.message.includes('403')) {
      message = ERROR_MESSAGES.PERMISSION_DENIED
    } else if (error.message.includes('500')) {
      message = ERROR_MESSAGES.SERVER_BUSY
    }
  }
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
  
  return message
}

/**
 * 处理登录过期
 */
function handleLoginExpired() {
  const app = getApp()
  if (app && typeof app.clearLoginState === 'function') {
    // 避免重复调用，检查是否正在清除状态
    if (!app._clearingLoginState) {
      app.clearLoginState(true) // 显示提示信息
    }
  }
}

/**
 * 表单验证错误处理
 * @param {Object} validation 验证结果
 */
export function handleValidationError(validation) {
  if (!validation.valid && validation.message) {
    wx.showToast({
      title: validation.message,
      icon: 'none',
      duration: 2000
    })
  }
}

/**
 * Loading状态管理
 */
class LoadingManager {
  constructor() {
    this.loadingCount = 0
    this.loadingTimer = null
  }
  
  show(title = '加载中...', mask = true) {
    if (this.loadingCount === 0) {
      wx.showLoading({ title, mask })
      
      // 防止loading一直显示，15秒后自动隐藏
      this.loadingTimer = setTimeout(() => {
        this.forceHide()
      }, 15000)
    }
    this.loadingCount++
  }
  
  hide() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      this.forceHide()
    }
  }
  
  forceHide() {
    this.loadingCount = 0
    wx.hideLoading()
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer)
      this.loadingTimer = null
    }
  }
}

// 创建单例loading管理器
let loadingManager = null

export function getLoadingManager() {
  if (!loadingManager) {
    loadingManager = new LoadingManager()
  }
  return loadingManager
}

export default {
  handleError,
  getErrorMessage,
  handleNetworkError,
  handleValidationError,
  getLoadingManager
} 