<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('物业管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>
							<li>
								物业名称：<input type="text" name="name"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		        <a class="btn btn-success" onclick="$.operate.add()">
		            <i class="fa fa-plus"></i> 新增
		        </a>
		        <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
		            <i class="fa fa-edit"></i> 修改
		        </a>
		        <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
		            <i class="fa fa-remove"></i> 删除
		        </a>
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "pms/info";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "物业",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'pms_id',
                    title: '物业ID',
                    visible: false
                },
                {
                    field: 'pms_name',
                    title: '物业名称',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'address',
                    title: '物业地址',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'manager',
                    title: '物业管理员',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'phone',
                    title: '联系电话'
                },
                {
                    field: 'legal_person',
                    title: '公司法人'
                },
                {
                    field: 'establishment_date',
                    title: '成立日期'
                },
				{
					field: 'employee_count',
					title: '员工数量'
				},
				{
					field: 'registration_number',
					title: '注册号'
				},
				{
					field: 'status',
					title: '状态',
					align: 'center',
					formatter: function(value, row, index) {
						if (value == 0) {
							return '<span class="label label-primary">正常</span>';
						} else if (value == 1) {
							return '<span class="label label-danger">暂停</span>';
						}
					}
				},
                {
                    field: 'create_time',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.pms_id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                       	if(row.status==0){
							actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="updateStatus(\'' + row.pms_id + '\',1)"><i class="fa fa-stop"></i> 暂停</a>');
						}else{
							actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="updateStatus(\'' + row.pms_id + '\',0)"><i class="fa fa-play"></i> 启用</a>');
						}
                        actions.push('<a class="btn btn-default btn-xs" href="javascript:void(0)" onclick="detail(\'' + row.pms_id + '\')">管理小区</a>');
                        return actions.join('&nbsp;');
                    }
                }]
            };
            $.table.init(options);
        });

		function detail(pmsId) {
			var url = ctx + 'oc/info/pmsOc/' + pmsId;
			$.modal.openTab("管理小区", url);
		}

		function updateStatus(pmsId, status) {
			$.modal.confirm("确定要修改状态吗？", function() {
				var data = {
					"pms_id": pmsId,
					"status": status
				};
				$.operate.submit(prefix + "/changeStatus", "post", "json", data);
			});
		}

    </script>
</body>
</html>