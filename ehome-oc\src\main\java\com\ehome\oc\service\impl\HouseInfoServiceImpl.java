package com.ehome.oc.service.impl;

import com.ehome.common.exception.ServiceException;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.mapper.HouseInfoMapper;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class HouseInfoServiceImpl implements IHouseInfoService {

    @Autowired
    private HouseInfoMapper houseInfoMapper;

    @Override
    public List<HouseInfo> selectHouseList(HouseInfo houseInfo) {
        return houseInfoMapper.selectHouseList(houseInfo.getWxUserId());
    }

    @Override
    public HouseInfo selectHouseById(Long houseId) {
        return houseInfoMapper.selectHouseById(houseId);
    }


    @Override
    public HouseInfo recordToObj(Record record){
        HouseInfo house = new HouseInfo();
        house.setIsDefault(record.getInt("is_default"));
        house.setHouseStatus(record.getStr("house_status"));
        house.setCheckStatus(record.getStr("check_status"));
        house.setCommunityName(record.getStr("community_name"));
        house.setCommunityId(record.getStr("community_id"));
        house.setBuildingId(record.getStr("building_id"));
        house.setBuilding(record.getStr("building"));
        house.setUnit(record.getStr("unit"));
        house.setRoom(record.getStr("room"));
        house.setArea(record.getBigDecimal("area"));
        house.setOwnerName(record.getStr("owner_name"));
        house.setOwnerPhone(record.getStr("owner_phone"));
        house.setIdCard(record.getStr("id_card"));
        house.setRemark(record.getStr("remark"));
        house.setHouseId(record.getLong("house_id"));
        house.setCombinaName(record.getStr("combina_name"));
        return house;
    }

    @Override
    @Transactional
    public int addHouse(HouseInfo house) {
        // 检查房屋数量限制
        int count = houseInfoMapper.selectHouseCount(house.getWxUserId());
        if (count >= 5) {
            throw new ServiceException("最多只能添加5套房屋");
        }

        // 如果是第一套房屋，设为默认
        if (count == 0) {
            house.setIsDefault(1);
        } else {
            house.setIsDefault(0);
        }

        // 设置初始状态为未审核
        house.setStatus(0);
        
        return houseInfoMapper.insertHouse(house);
    }

    @Override
    public int updateHouse(HouseInfo house) {
        // 重置审核状态
        house.setStatus(0);
        return houseInfoMapper.updateHouse(house);
    }

    @Override
    public int deleteHouse(Long houseId) {
        // 检查是否为默认房屋
        HouseInfo house = houseInfoMapper.selectHouseById(houseId);
        if (house != null && house.getIsDefault() == 1) {
            throw new ServiceException("默认房屋不能删除");
        }
        return houseInfoMapper.deleteHouseById(houseId);
    }

    @Override
    @Transactional
    public int setDefaultHouse(Long houseId, Long wxUserId) {
        // 先重置该用户的所有默认房屋
        houseInfoMapper.resetDefaultHouse(wxUserId);
        // 设置新的默认房屋
        return houseInfoMapper.setDefaultHouse(houseId);
    }

    @Override
    public int countHouseByUserId(Long wxUserId) {
        return houseInfoMapper.countHouseByUserId(wxUserId);
    }
} 