import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    isLogin: false,
    hasBindPhone: false,
    showPhoneModal: false
  },

  onLoad() {
    this.checkAutoLogin()
  },

  onShow() {
    this.refreshLoginState()
  },

  // 检查自动登录
  async checkAutoLogin() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('wxUserInfo')

    if (!token || !userInfo?.userId) {
      // token 无效或不存在，直接清除状态
      stateManager.clearState()
      return
    }

    try {
      loadingManager.show('检查登录状态...')

      // 验证 token 有效性，添加超时处理
      const res = await Promise.race([
        app.request({
          url: '/api/wx/auth/check',
          method: 'POST'
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('请求超时')), 10000)
        )
      ])
      
      if (res.code === 0) {
        // token 有效，直接跳转
        this.redirectToHome()
        return
      }
      
      // token 无效，清除状态
      stateManager.clearState()
    } catch (error) {
      // token 验证失败，清除状态
      stateManager.clearState()
    } finally {
      loadingManager.hide()
    }
  },

  // 刷新登录状态
  refreshLoginState() {
    const state = stateManager.getState()
    this.setData({
      isLogin: state.isLogin,
      hasBindPhone: state.hasBindPhone
    })
  },

  // 微信登录
  async handleLogin() {
    try {
      // 获取用户授权
      const userProfile = await wx.getUserProfile({
        desc: '用于完善业主资料',
        lang: 'zh_CN'
      }).catch(() => {
        throw new Error('用户拒绝授权')
      })

      loadingManager.show('登录中...')

      // 获取微信登录凭证
      const loginResult = await wx.login()
      
      // 调用统一登录接口，添加超时处理
      const res = await Promise.race([
        app.request({
          url: '/api/wx/auth/loginComplete',
          method: 'POST',
          data: {
            code: loginResult.code,
            userInfo: userProfile.userInfo,
            encryptedData: userProfile.encryptedData,
            iv: userProfile.iv
          }
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('登录请求超时')), 15000)
        )
      ])

      if (res.code === 0) {
        // 登录成功，更新状态
        stateManager.setLoginSuccess({
          token: res.data.token,
          userInfo: res.data.userInfo,
          hasBindPhone: res.data.hasBindPhone,
          isHouseAuth: res.data.isHouseAuth,
          houseInfo: res.data.houseInfo,
          ownerInfo: res.data.ownerInfo,
          communityInfo: res.data.communityInfo
        })

        this.refreshLoginState()

        // 根据登录状态处理后续流程
        if (!res.data.hasBindPhone) {
          this.showPhoneBinding()
        } else if (!res.data.isHouseAuth) {
          this.showHouseAuthError()
        } else {
          this.showLoginSuccess()
        }
      } else {
        throw new Error(res.msg || '登录失败')
      }
    } catch (error) {
      handleError(error, '登录')
      stateManager.clearState()
      this.refreshLoginState()
    } finally {
      loadingManager.hide()
    }
  },

  // 显示手机号绑定提示
  showPhoneBinding() {
    this.setData({ showPhoneModal: true })
  },

  // 关闭手机号绑定弹窗
  closePhoneModal() {
    this.setData({ showPhoneModal: false })
  },

  // 获取手机号授权
  async getPhoneNumber(e) {
    try {
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        throw new Error('用户拒绝授权手机号')
      }

      this.closePhoneModal()
      loadingManager.show('绑定手机号...')

      // 获取新的登录凭证
      const loginResult = await wx.login()

      // 调用手机号绑定接口，添加超时处理
      const res = await Promise.race([
        app.request({
          url: '/api/wx/auth/bindPhone',
          method: 'POST',
          data: {
            code: loginResult.code,
            phoneCode: e.detail.code,
            userId: stateManager.getState().userInfo?.userId
          }
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('绑定手机号请求超时')), 15000)
        )
      ])

      if (res.code === 0) {
        // 绑定成功，更新状态
        stateManager.updateLoginState({
          hasBindPhone: true,
          token: res.data.token,
          userInfo: res.data.userInfo,
          isHouseAuth: res.data.isHouseAuth,
          houseInfo: res.data.houseInfo,
          ownerInfo: res.data.ownerInfo,
          communityInfo: res.data.communityInfo
        })

        this.refreshLoginState()

        // 检查房屋认证状态
        if (!res.data.isHouseAuth) {
          this.showHouseAuthError()
        } else {
          this.showLoginSuccess('手机号绑定成功')
        }
      } else {
        throw new Error(res.msg || '手机号绑定失败')
      }
    } catch (error) {
      handleError(error, '手机号绑定')
    } finally {
      loadingManager.hide()
    }
  },

  // 显示房屋认证错误
  showHouseAuthError() {
    wx.showModal({
      title: '无法登录',
      content: '当前手机号未绑定房屋，请联系管理员绑定后再登录。',
      showCancel: false,
      confirmText: '我知道了',
      success: () => {
        // 清除登录状态
        stateManager.clearState()
        this.refreshLoginState()
      }
    })
  },

  // 显示登录成功并跳转
  showLoginSuccess(message = '登录成功') {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          this.redirectToHome()
        }, 1500)
      }
    })
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 跳转到隐私政策
  goToPrivacy() {
    wx.navigateTo({
      url: '/pages/about/privacy'
    })
  },

  // 跳转到用户协议
  goToAgreement() {
    wx.navigateTo({
      url: '/pages/about/agreement'
    })
  }
})