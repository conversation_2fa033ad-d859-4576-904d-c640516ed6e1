// 状态管理器
import { STORAGE_KEYS } from '../constants/index.js'

class StateManager {
  constructor() {
    this.state = {
      isLogin: false,
      hasBindPhone: false,
      isHouseAuth: false,
      userInfo: null,
      ownerInfo: null,
      communityInfo: null,
      houseInfo: null,
      tokenUser: null
    }
    this.loadFromStorage()
  }

  // 从本地存储加载状态
  loadFromStorage() {
    try {
      const token = wx.getStorageSync(STORAGE_KEYS.TOKEN)
      const userInfo = wx.getStorageSync(STORAGE_KEYS.WX_USER_INFO)
      const ownerInfo = wx.getStorageSync(STORAGE_KEYS.OWNER_INFO)
      const communityInfo = wx.getStorageSync(STORAGE_KEYS.COMMUNITY_INFO)
      const houseInfo = wx.getStorageSync(STORAGE_KEYS.HOUSE_INFO)
      const tokenUser = wx.getStorageSync(STORAGE_KEYS.TOKEN_USER)

      this.state = {
        ...this.state,
        isLogin: !!(token && userInfo),
        hasBindPhone: !!(userInfo && userInfo.phoneNumber),
        isHouseAuth: !!(ownerInfo && houseInfo),
        userInfo,
        ownerInfo,
        communityInfo,
        houseInfo,
        tokenUser
      }
    } catch (error) {
      console.error('[StateManager] 加载状态失败:', error)
    }
  }

  // 更新状态
  setState(newState) {
    this.state = { ...this.state, ...newState }
    this.syncToStorage()
    this.syncToGlobalData()
  }

  // 同步到本地存储
  syncToStorage() {
    try {
      if (this.state.userInfo) {
        wx.setStorageSync(STORAGE_KEYS.WX_USER_INFO, this.state.userInfo)
        wx.setStorageSync('um_userid', this.state.userInfo.userId)
        wx.setStorageSync('um_unid', this.state.userInfo.openId)
      }
      if (this.state.ownerInfo) {
        wx.setStorageSync(STORAGE_KEYS.OWNER_INFO, this.state.ownerInfo)
      }
      if (this.state.communityInfo) {
        wx.setStorageSync(STORAGE_KEYS.COMMUNITY_INFO, this.state.communityInfo)
      }
      if (this.state.houseInfo) {
        wx.setStorageSync(STORAGE_KEYS.HOUSE_INFO, this.state.houseInfo)
      }
      if (this.state.tokenUser) {
        wx.setStorageSync(STORAGE_KEYS.TOKEN_USER, this.state.tokenUser)
      }
    } catch (error) {
      console.error('[StateManager] 同步存储失败:', error)
    }
  }

  // 同步到全局数据
  syncToGlobalData() {
    const app = getApp()
    if (app && app.globalData) {
      // 只同步必要的数据到 globalData，保持 globalData 简洁
      app.globalData.isLogin = this.state.isLogin
      app.globalData.userInfo = this.state.userInfo
      app.globalData.tokenUser = this.state.tokenUser
    }
  }

  // 获取状态
  getState() {
    return { ...this.state }
  }

  // 清除所有状态
  clearState(callback) {
    this.state = {
      isLogin: false,
      hasBindPhone: false,
      isHouseAuth: false,
      userInfo: null,
      ownerInfo: null,
      communityInfo: null,
      houseInfo: null,
      tokenUser: null
    }

    // 清除本地存储
    wx.removeStorageSync(STORAGE_KEYS.TOKEN)
    wx.removeStorageSync(STORAGE_KEYS.WX_USER_INFO)
    wx.removeStorageSync(STORAGE_KEYS.OWNER_INFO)
    wx.removeStorageSync(STORAGE_KEYS.COMMUNITY_INFO)
    wx.removeStorageSync(STORAGE_KEYS.HOUSE_INFO)
    wx.removeStorageSync(STORAGE_KEYS.TOKEN_USER)

    this.syncToGlobalData()

    // 执行回调函数（如果提供）
    if (typeof callback === 'function') {
      callback()
    }
  }

  // 设置登录成功状态
  setLoginSuccess(loginData) {
    // 保存 token
    if (loginData.token) {
      wx.setStorageSync(STORAGE_KEYS.TOKEN, loginData.token)
    }

    // 更新状态
    this.setState({
      isLogin: true,
      hasBindPhone: loginData.hasBindPhone || false,
      isHouseAuth: loginData.isHouseAuth || false,
      userInfo: loginData.userInfo,
      ownerInfo: loginData.ownerInfo,
      communityInfo: loginData.communityInfo,
      houseInfo: loginData.houseInfo,
      tokenUser: loginData.tokenUser
    })
  }

  // 更新登录状态（用于手机号绑定后）
  updateLoginState(updateData) {
    // 更新 token
    if (updateData.token) {
      wx.setStorageSync(STORAGE_KEYS.TOKEN, updateData.token)
    }

    // 更新状态
    const newState = { ...this.state, ...updateData }
    this.setState(newState)
  }

  // 检查登录状态
  isLoggedIn() {
    return this.state.isLogin && this.state.hasBindPhone
  }

  // 检查房屋认证状态
  isHouseAuthenticated() {
    return this.state.isHouseAuth
  }
}

// 创建单例
let stateManager = null

export function getStateManager() {
  if (!stateManager) {
    stateManager = new StateManager()
  }
  return stateManager
}

export default getStateManager 