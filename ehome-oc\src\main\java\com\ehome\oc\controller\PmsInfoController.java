package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.jfinal.model.PmsInfoModel;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 物业信息管理控制器
 */
@Controller
@RequestMapping("/pms/info")
public class PmsInfoController extends BaseController {
    private static final String PREFIX = "pms/info";

    /**
     * 跳转到物业管理页面
     */
    @RequestMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 跳转到新增物业页面
     */
    @RequestMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    /**
     * 跳转到编辑物业页面
     *
     * @param pmsId 物业ID
     * @param mmap 数据传输对象
     */
    @GetMapping("/edit/{pmsId}")
    public String edit(@PathVariable("pmsId") String pmsId, ModelMap mmap) {
        PmsInfoModel pmsInfo = PmsInfoModel.dao.findById(pmsId);
        mmap.put("pmsInfo", pmsInfo.toMap());
        return PREFIX + "/edit";
    }

    /**
     * 查询物业列表
     * 支持以下查询条件:
     * - 物业名称(模糊查询)
     * - 物业地址(模糊查询)
     * - 物业管理员(精确匹配)
     * - 联系电话(精确匹配)
     * - 公司法人(精确匹配)
     * - 成立日期范围
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 获取单条物业记录
     * 
     * @return 包含物业信息的响应对象
     */
    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String pmsId = params.getString("pms_id");
        if (StringUtils.isEmpty(pmsId)) {
            return AjaxResult.error("物业ID不能为空");
        }
        PmsInfoModel model = PmsInfoModel.dao.findById(pmsId);
        return AjaxResult.success(null, model.toMap());
    }

    /**
     * 新增物业信息
     */
    @PostMapping("/addData")
    @ResponseBody
    @Log(title = "新增物业信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        PmsInfoModel model = new PmsInfoModel();
        model.setColumns(params);
        model.set("pms_id", Seq.getId());
        model.set("create_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        model.save();
        return AjaxResult.success();
    }

    /**
     * 修改物业信息
     */
    @Log(title = "更改物业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        PmsInfoModel model = new PmsInfoModel();
        model.setColumns(params);
        return toAjax(model.update());
    }

    /**
     * 删除物业信息
     *
     * @param ids 物业ID,多个以逗号分隔
     */
    @Log(title = "删除物业", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            PmsInfoModel.dao.deleteById(id);
        }
        return success();
    }

    /**
     * 校验物业名称是否唯一
     */
    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String name = params.getString("name");
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_pms_info where 1=1");
        sql.append(name, "and name = ?");
        PmsInfoModel model = PmsInfoModel.dao.findFirst(sql.getSQL(), sql.getParams());
        return model == null;
    }
    /**
     * 修改物业状态
     */
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus() {
        JSONObject params = getParams();
        String pmsId = params.getString("pms_id");
        String status = params.getString("status");
        if (StringUtils.isEmpty(pmsId)) {
            return AjaxResult.error("物业ID不能为空");
        }
        if (StringUtils.isEmpty(status)) {
            return AjaxResult.error("状态不能为空");
        }
        PmsInfoModel model = PmsInfoModel.dao.findById(pmsId);
        model.set("status", status);
        return toAjax(model.update());
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_pms_info where 1=1");

        // 物业名称 - 模糊查询
        sql.appendLike(params.getString("name"), "and name like ?");
        // 物业地址 - 模糊查询
        sql.appendLike(params.getString("address"), "and address like ?");
        // 物业管理员 - 精确匹配
        sql.append(params.getString("manager"), "and manager = ?");
        // 联系电话 - 精确匹配
        sql.append(params.getString("phone"), "and phone = ?");
        // 公司法人 - 精确匹配
        sql.append(params.getString("legal_person"), "and legal_person = ?");
        // 成立日期范围查询
        String beginDate = params.getString("beginDate");
        sql.append(beginDate, "and establishment_date >= ?");
        String endDate = params.getString("endDate");
        sql.append(endDate, "and establishment_date <= ?");

        // 默认按创建时间倒序
        sql.append("order by create_time desc");
        return sql;
    }
}
