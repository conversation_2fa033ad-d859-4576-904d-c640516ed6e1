package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.http.HttpUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.mapper.WxUserMapper;
import com.ehome.oc.service.IWxUserService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Service
public class WxUserServiceImpl implements IWxUserService {
    private static final Logger log = LoggerFactory.getLogger(WxUserServiceImpl.class);

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.secret}")
    private String secret;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Override
    @Transactional
    public WxUser wxLogin(WxLoginDTO loginDTO) {
        try {
            String code = loginDTO.getCode();
            // 调用微信接口获取openid
            String openid = getOpenidByCode(code);
            if (openid == null) {
                throw new ServiceException("获取openid失败");
            }

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            if (wxUser == null) {
                // 新用户，自动注册
                wxUser = new WxUser();
                wxUser.setOpenId(openid);
                if (loginDTO.getUserInfo() != null && loginDTO.getUserInfo() instanceof java.util.Map) {
                    java.util.Map userInfoMap = (java.util.Map) loginDTO.getUserInfo();
                    Object nickName = userInfoMap.get("nickName");
                    Object avatarUrl = userInfoMap.get("avatarUrl");
                    if (nickName != null) wxUser.setNickName(nickName.toString());
                    if (avatarUrl != null) wxUser.setAvatarUrl(avatarUrl.toString());
                }
                if (wxUser.getNickName() == null) {
                    wxUser.setNickName("微信用户");
                }
                wxUser.setStatus("0");
                wxUserMapper.insertWxUser(wxUser);
            } else {
                // 已有用户，自动更新昵称和头像
                wxUser.setOpenId(openid);
                if (loginDTO.getUserInfo() != null && loginDTO.getUserInfo() instanceof java.util.Map) {
                    java.util.Map userInfoMap = (java.util.Map) loginDTO.getUserInfo();
                    Object nickName = userInfoMap.get("nickName");
                    Object avatarUrl = userInfoMap.get("avatarUrl");
                    boolean needUpdate = false;
                    if (nickName != null && !nickName.equals(wxUser.getNickName())) {
                        wxUser.setNickName(nickName.toString());
                        needUpdate = true;
                    }
                    if (avatarUrl != null && !avatarUrl.equals(wxUser.getAvatarUrl())) {
                        wxUser.setAvatarUrl(avatarUrl.toString());
                        needUpdate = true;
                    }
                    if (needUpdate) wxUserMapper.updateWxUser(wxUser);
                }
            }

            // 获取真实IP
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String ip = IpUtils.getIpAddr(request);

            // 更新登录信息
            wxUser.setLoginIp(ip);
            wxUser.setLoginDate(new Date());
            wxUserMapper.updateWxUser(wxUser);

            Record wxLoginLog = new Record();
            wxLoginLog.set("user_id", wxUser.getUserId());
            wxLoginLog.set("mobile", wxUser.getMobile());
            wxLoginLog.set("login_ip", ip);
            wxLoginLog.set("login_date", new Date());
            wxLoginLog.set("user_agent", request.getHeader("User-Agent"));
            Db.save("wx_login_log","log_id", wxLoginLog);

            return wxUser;
        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw new ServiceException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WxUser selectWxUserById(Long userId) {
        return wxUserMapper.selectWxUserById(userId);
    }

    @Override
    public int updateWxUser(WxUser user) {
        return wxUserMapper.updateWxUser(user);
    }

    @Override
    public WxUser selectWxUserByOpenid(String openid) {
        return wxUserMapper.selectWxUserByOpenid(openid);
    }

    /**
     * 根据code获取openid
     */
    private String getOpenidByCode(String code) {
        try {
            // 微信登录凭证校验接口
            String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appid, secret, code);
            
            String response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);
            
            // 判断是否成功
            if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                log.error("获取openid失败: {}", response);
                return null;
            }
            
            return jsonObject.getString("openid");
        } catch (Exception e) {
            log.error("调用微信接口失败", e);
            return null;
        }
    }
} 