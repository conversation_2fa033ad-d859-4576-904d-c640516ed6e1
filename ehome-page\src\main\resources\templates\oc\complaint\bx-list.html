<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <th:block th:include="include :: header('报修管理')" />
    <style>
        .select-list li {
            width: auto;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="config-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label for="type">报修类型：</label>
                                <input type="text" id="type" name="type" placeholder="请输入报修类型" title="报修类型"/>
                            </li>
                            <li>
                                <label for="content">报修内容：</label>
                                <input type="text" id="content" name="content" placeholder="请输入报修内容" title="报修内容"/>
                            </li>
                            <li>
                                <label for="status">状态：</label>
                                <select id="status" name="status" title="状态">
                                    <option value="">全部</option>
                                    <option value="0">待处理</option>
                                    <option value="1">处理中</option>
                                    <option value="2">已完成</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="batchUpdateStatus()">
                    <i class="fa fa-plus"></i> 批量处理
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <div id="detailModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">报修详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <!-- 详情内容通过JS填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <div id="batchStatusModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量更改报修状态</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="batchStatusSelect">请选择新状态：</label>
                        <select id="batchStatusSelect" class="form-control">
                            <option value="0">待处理</option>
                            <option value="1">处理中</option>
                            <option value="2">已完成</option>
                        </select>
                    </div>
                    <div class="form-group" id="replyContentGroup" style="display:none;">
                        <label for="replyContentInput">反馈结果：</label>
                        <textarea id="replyContentInput" class="form-control" rows="3" placeholder="请填写反馈结果"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="doBatchUpdateStatus()">确定</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/complain";
        $(function() {
            var options = {
                url: prefix + "/bxData",
                modalName: "报修",
                columns: [
                    { checkbox: true },
                    { field: 'id', title: 'ID', visible: false },
                    { field: 'type', title: '报修类型' },
                    { field: 'content', title: '报修内容',width: '500px'},
                    { field: 'address', title: '地址',width: '200px'},
                    { field: 'name', title: '报修人',width: '80px' },
                    { field: 'phone', title: '联系电话' },
                    { field: 'create_time', title: '报修时间' },
                    { field: 'status', title: '状态',width:'80px',formatter: function(value) {
                        if(value==0) return '待处理';
                        if(value==1) return '处理中';
                        if(value==2) return '已完成';
                        return '-';
                    }},
                    { title: '操作', align: 'center', formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-search"></i> 查看详情</a> ');
                        return actions.join('');
                    }}
                ]
            };
            $.table.init(options);
        });
        
        function viewDetail(id) {
            $.modal.loading('加载中...');
            $.post(prefix + '/bxDetail', {id: id}, function(res) {
                $.modal.closeLoading();
                if(res.code === 0 && res.data) {
                    var d = res.data;
                    var html = '<table class="table table-bordered">';
                    html += '<tr><th>报修类型</th><td>' + (d.type||'') + '</td></tr>';
                    html += '<tr><th>报修内容</th><td>' + (d.content||'') + '</td></tr>';
                    html += '<tr><th>地址</th><td>' + (d.address||'') + '</td></tr>';
                    html += '<tr><th>报修人</th><td>' + (d.name||'') + '</td></tr>';
                    html += '<tr><th>联系电话</th><td>' + (d.phone||'') + '</td></tr>';
                    html += '<tr><th>报修时间</th><td>' + (d.create_time||'') + '</td></tr>';
                    html += '<tr><th>状态</th><td>' + (d.status==0?'待处理':(d.status==1?'处理中':(d.status==2?'已完成':'-'))) + '</td></tr>';
                    if(d.media_urls){
                        html += '<tr><th>图片</th><td>' + d.media_urls.split(',').map(function(url){return '<img src=\"'+url+'\" style=\"max-width:100px;max-height:100px;margin-right:5px;\">';}).join('') + '</td></tr>';
                    }
                    html += '</table>';
                    $('#detailModalBody').html(html);
                    $('#detailModal').modal('show');
                } else {
                    $.modal.msgError(res.msg||'加载失败');
                }
            });
        }

        function batchUpdateStatus(){
            var rows = $.table.selectColumns("id");
            if(rows.length === 0){
                $.modal.msgWarning('请先选择要处理的报修');
                return;
            }
            $('#batchStatusModal').data('ids', rows.join(','));
            $('#batchStatusModal').modal('show');
        }

        // 状态选择联动反馈结果输入显示
        $(document).on('change', '#batchStatusSelect', function(){
            if($(this).val() == '2'){
                $('#replyContentGroup').show();
            }else{
                $('#replyContentGroup').hide();
                $('#replyContentInput').val('');
            }
        });

        function doBatchUpdateStatus(){
            var ids = $('#batchStatusModal').data('ids');
            var status = $('#batchStatusSelect').val();
            var reply_content = $('#replyContentInput').val();
            if(!ids || !status){
                $.modal.msgWarning('参数错误');
                return;
            }
            if (status == 2) {
                if(!reply_content){
                    $.modal.msgWarning('请填写反馈结果');
                    return;
                }
            }
            submitStatus(ids, status, reply_content||'');
        }

        function submitStatus(ids, status, reply_content) {
            $.modal.loading('处理中...');
            $.post(prefix + '/batchUpdateBxStatus', {ids: ids, status: status, reply_content: reply_content}, function(res){
                $.modal.closeLoading();
                if(res.code === 0){
                    $.modal.msgSuccess('批量处理成功');
                    $('#batchStatusModal').modal('hide');
                    $.table.refresh();
                }else{
                    $.modal.msgError(res.msg||'批量处理失败');
                }
            });
        }
    </script>
</body>
</html>
