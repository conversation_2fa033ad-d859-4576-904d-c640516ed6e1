/* 财务页面 */
page {
  height: 100vh;
  background: #f7f8fa;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}

/* 账户卡片 */
.account-card {
  margin: 20rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.balance-box {
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.balance-amount {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
}

.balance-amount.negative {
  color: #ff4d4f;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid #f5f5f5;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-btn .iconfont {
  font-size: 44rpx;
  color: #07c160;
  margin-bottom: 8rpx;
}

.action-btn text {
  font-size: 26rpx;
  color: #333;
}

/* 账单列表 */
.bill-list {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.bill-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.bill-item:last-child {
  border-bottom: none;
}

.bill-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(7, 193, 96, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.bill-icon .iconfont {
  font-size: 40rpx;
  color: #07c160;
}

.bill-info {
  flex: 1;
  margin-right: 20rpx;
}

.bill-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.bill-time {
  font-size: 24rpx;
  color: #999;
}

.bill-amount {
  font-size: 32rpx;
  font-weight: 500;
}

.bill-amount.income {
  color: #07c160;
}

.bill-amount.expense {
  color: #ff4d4f;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 30rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

/* 功能图标 */
.icon-recharge:before { content: "\e64a"; }
.icon-withdraw:before { content: "\e64b"; }
.icon-water:before { content: "\e645"; }
.icon-electric:before { content: "\e646"; }
.icon-property:before { content: "\e647"; }
.icon-parking:before { content: "\e648"; } 