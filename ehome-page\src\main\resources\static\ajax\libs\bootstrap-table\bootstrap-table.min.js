/**
 * <AUTHOR> wen
 * version: 1.22.6
 * https://github.com/wenzhixin/bootstrap-table/
 */
!function(t,e){"object"===typeof exports&&"undefined"!==typeof module?module.exports=e(require("jquery")):"function"===typeof define&&define.amd?define(["jquery"],e):(t="undefined"!==typeof globalThis?globalThis:t||self,t.BootstrapTable=e(t.jQuery))}(this,function(t){"use strict";function e(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var a,i,n,o,s=[],l=!0,c=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=r["return"]&&(o=r["return"](),Object(o)!==o))return}finally{if(c)throw i}}return s}}function r(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function a(t){var e=r(t,"string");return"symbol"==typeof e?e:e+""}function i(t){"@babel/helpers - typeof";return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(t,a(i.key),i)}}function s(t,e,r){if(e)o(t.prototype,e);if(r)o(t,r);Object.defineProperty(t,"prototype",{writable:false});return t}function l(t,r){return u(t)||e(t,r)||v(t,r)||g()}function c(t){return f(t)||h(t)||v(t)||p()}function f(t){if(Array.isArray(t))return d(t)}function u(t){if(Array.isArray(t))return t}function h(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function v(t,e){if(!t)return;if("string"===typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor)r=t.constructor.name;if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}function d(t,e){if(null==e||e>t.length)e=t.length;for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function p(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t,e){var r="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=v(t))||e&&t&&"number"===typeof t.length){if(r)t=r;var a=0;var i=function(){};return{s:i,n:function(){if(a>=t.length)return{done:true};return{done:false,value:t[a++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n=true,o=false,s;return{s:function(){r=r.call(t)},n:function(){var t=r.next();n=t.done;return t},e:function(t){o=true;s=t},f:function(){try{if(!n&&null!=r["return"])r["return"]()}finally{if(o)throw s}}}}var m="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof window?window:"undefined"!==typeof global?global:"undefined"!==typeof self?self:{};var y=function(t){return t&&t.Math===Math&&t};var w=y("object"==typeof globalThis&&globalThis)||y("object"==typeof window&&window)||y("object"==typeof self&&self)||y("object"==typeof m&&m)||y("object"==typeof m&&m)||function(){return this}()||Function("return this")();var S={};var x=function(t){try{return!!t()}catch(e){return true}};var O=x;var k=!O(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]});var P=x;var C=!P(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")});var T=C;var I=Function.prototype.call;var A=T?I.bind(I):function(){return I.apply(I,arguments)};var $={};var R={}.propertyIsEnumerable;var E=Object.getOwnPropertyDescriptor;var j=E&&!R.call({1:2},1);$.f=j?function ky(t){var e=E(this,t);return!!e&&e.enumerable}:R;var _=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}};var N=C;var F=Function.prototype;var D=F.call;var V=N&&F.bind.bind(D,D);var B=N?V:function(t){return function(){return D.apply(t,arguments)}};var L=B;var H=L({}.toString);var M=L("".slice);var U=function(t){return M(H(t),8,-1)};var z=B;var q=x;var G=U;var W=Object;var K=z("".split);var Y=q(function(){return!W("z").propertyIsEnumerable(0)})?function(t){return"String"===G(t)?K(t,""):W(t)}:W;var J=function(t){return null===t||void 0===t};var X=J;var Q=TypeError;var Z=function(t){if(X(t))throw new Q("Can't call method on "+t);return t};var tt=Y;var et=Z;var rt=function(t){return tt(et(t))};var at="object"==typeof document&&document.all;var it="undefined"==typeof at&&void 0!==at?function(t){return"function"==typeof t||t===at}:function(t){return"function"==typeof t};var nt=it;var ot=function(t){return"object"==typeof t?null!==t:nt(t)};var st=w;var lt=it;var ct=function(t){return lt(t)?t:void 0};var ft=function(t,e){return arguments.length<2?ct(st[t]):st[t]&&st[t][e]};var ut=B;var ht=ut({}.isPrototypeOf);var vt="undefined"!=typeof navigator&&String(navigator.userAgent)||"";var dt=w;var pt=vt;var gt=dt.process;var bt=dt.Deno;var mt=gt&&gt.versions||bt&&bt.version;var yt=mt&&mt.v8;var wt,St;if(yt){wt=yt.split(".");St=wt[0]>0&&wt[0]<4?1:+(wt[0]+wt[1])}if(!St&&pt){wt=pt.match(/Edge\/(\d+)/);if(!wt||wt[1]>=74){wt=pt.match(/Chrome\/(\d+)/);if(wt)St=+wt[1]}}var xt=St;var Ot=xt;var kt=x;var Pt=w;var Ct=Pt.String;var Tt=!!Object.getOwnPropertySymbols&&!kt(function(){var t=Symbol("symbol detection");return!Ct(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Ot&&Ot<41});var It=Tt;var At=It&&!Symbol.sham&&"symbol"==typeof Symbol.iterator;var $t=ft;var Rt=it;var Et=ht;var jt=At;var _t=Object;var Nt=jt?function(t){return"symbol"==typeof t}:function(t){var e=$t("Symbol");return Rt(e)&&Et(e.prototype,_t(t))};var Ft=String;var Dt=function(t){try{return Ft(t)}catch(e){return"Object"}};var Vt=it;var Bt=Dt;var Lt=TypeError;var Ht=function(t){if(Vt(t))return t;throw new Lt(Bt(t)+" is not a function")};var Mt=Ht;var Ut=J;var zt=function(t,e){var r=t[e];return Ut(r)?void 0:Mt(r)};var qt=A;var Gt=it;var Wt=ot;var Kt=TypeError;var Yt=function(t,e){var r,a;if("string"===e&&Gt(r=t.toString)&&!Wt(a=qt(r,t)))return a;if(Gt(r=t.valueOf)&&!Wt(a=qt(r,t)))return a;if("string"!==e&&Gt(r=t.toString)&&!Wt(a=qt(r,t)))return a;throw new Kt("Can't convert object to primitive value")};var Jt={exports:{}};var Xt=false;var Qt=w;var Zt=Object.defineProperty;var te=function(t,e){try{Zt(Qt,t,{value:e,configurable:true,writable:true})}catch(r){Qt[t]=e}return e};var ee=w;var re=te;var ae="__core-js_shared__";var ie=Jt.exports=ee[ae]||re(ae,{});(ie.versions||(ie.versions=[])).push({version:"3.36.1",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.1/LICENSE",source:"https://github.com/zloirock/core-js"});var ne=Jt.exports;var oe=ne;var se=function(t,e){return oe[t]||(oe[t]=e||{})};var le=Z;var ce=Object;var fe=function(t){return ce(le(t))};var ue=B;var he=fe;var ve=ue({}.hasOwnProperty);var de=Object.hasOwn||function Py(t,e){return ve(he(t),e)};var pe=B;var ge=0;var be=Math.random();var me=pe(1..toString);var ye=function(t){return"Symbol("+(void 0===t?"":t)+")_"+me(++ge+be,36)};var we=w;var Se=se;var xe=de;var Oe=ye;var ke=Tt;var Pe=At;var Ce=we.Symbol;var Te=Se("wks");var Ie=Pe?Ce["for"]||Ce:Ce&&Ce.withoutSetter||Oe;var Ae=function(t){if(!xe(Te,t))Te[t]=ke&&xe(Ce,t)?Ce[t]:Ie("Symbol."+t);return Te[t]};var $e=A;var Re=ot;var Ee=Nt;var je=zt;var _e=Yt;var Ne=Ae;var Fe=TypeError;var De=Ne("toPrimitive");var Ve=function(t,e){if(!Re(t)||Ee(t))return t;var r=je(t,De);var a;if(r){if(void 0===e)e="default";a=$e(r,t,e);if(!Re(a)||Ee(a))return a;throw new Fe("Can't convert object to primitive value")}if(void 0===e)e="number";return _e(t,e)};var Be=Ve;var Le=Nt;var He=function(t){var e=Be(t,"string");return Le(e)?e:e+""};var Me=w;var Ue=ot;var ze=Me.document;var qe=Ue(ze)&&Ue(ze.createElement);var Ge=function(t){return qe?ze.createElement(t):{}};var We=k;var Ke=x;var Ye=Ge;var Je=!We&&!Ke(function(){return 7!==Object.defineProperty(Ye("div"),"a",{get:function(){return 7}}).a});var Xe=k;var Qe=A;var Ze=$;var tr=_;var er=rt;var rr=He;var ar=de;var ir=Je;var nr=Object.getOwnPropertyDescriptor;S.f=Xe?nr:function Cy(t,e){t=er(t);e=rr(e);if(ir)try{return nr(t,e)}catch(r){}if(ar(t,e))return tr(!Qe(Ze.f,t,e),t[e])};var or={};var sr=k;var lr=x;var cr=sr&&lr(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:false}).prototype});var fr=ot;var ur=String;var hr=TypeError;var vr=function(t){if(fr(t))return t;throw new hr(ur(t)+" is not an object")};var dr=k;var pr=Je;var gr=cr;var br=vr;var mr=He;var yr=TypeError;var wr=Object.defineProperty;var Sr=Object.getOwnPropertyDescriptor;var xr="enumerable";var Or="configurable";var kr="writable";or.f=dr?gr?function Ty(t,e,r){br(t);e=mr(e);br(r);if("function"===typeof t&&"prototype"===e&&"value"in r&&kr in r&&!r[kr]){var a=Sr(t,e);if(a&&a[kr]){t[e]=r.value;r={configurable:Or in r?r[Or]:a[Or],enumerable:xr in r?r[xr]:a[xr],writable:false}}}return wr(t,e,r)}:wr:function Iy(t,e,r){br(t);e=mr(e);br(r);if(pr)try{return wr(t,e,r)}catch(a){}if("get"in r||"set"in r)throw new yr("Accessors not supported");if("value"in r)t[e]=r.value;return t};var Pr=k;var Cr=or;var Tr=_;var Ir=Pr?function(t,e,r){return Cr.f(t,e,Tr(1,r))}:function(t,e,r){t[e]=r;return t};var Ar={exports:{}};var $r=k;var Rr=de;var Er=Function.prototype;var jr=$r&&Object.getOwnPropertyDescriptor;var _r=Rr(Er,"name");var Nr=_r&&"something"===function Ay(){}.name;var Fr=_r&&(!$r||$r&&jr(Er,"name").configurable);var Dr={EXISTS:_r,PROPER:Nr,CONFIGURABLE:Fr};var Vr=B;var Br=it;var Lr=ne;var Hr=Vr(Function.toString);if(!Br(Lr.inspectSource))Lr.inspectSource=function(t){return Hr(t)};var Mr=Lr.inspectSource;var Ur=w;var zr=it;var qr=Ur.WeakMap;var Gr=zr(qr)&&/native code/.test(String(qr));var Wr=se;var Kr=ye;var Yr=Wr("keys");var Jr=function(t){return Yr[t]||(Yr[t]=Kr(t))};var Xr={};var Qr=Gr;var Zr=w;var ta=ot;var ea=Ir;var ra=de;var aa=ne;var ia=Jr;var na=Xr;var oa="Object already initialized";var sa=Zr.TypeError;var la=Zr.WeakMap;var ca,fa,ua;var ha=function(t){return ua(t)?fa(t):ca(t,{})};var va=function(t){return function(e){var r;if(!ta(e)||(r=fa(e)).type!==t)throw new sa("Incompatible receiver, "+t+" required");return r}};if(Qr||aa.state){var da=aa.state||(aa.state=new la);da.get=da.get;da.has=da.has;da.set=da.set;ca=function(t,e){if(da.has(t))throw new sa(oa);e.facade=t;da.set(t,e);return e};fa=function(t){return da.get(t)||{}};ua=function(t){return da.has(t)}}else{var pa=ia("state");na[pa]=true;ca=function(t,e){if(ra(t,pa))throw new sa(oa);e.facade=t;ea(t,pa,e);return e};fa=function(t){return ra(t,pa)?t[pa]:{}};ua=function(t){return ra(t,pa)}}var ga={set:ca,get:fa,has:ua,enforce:ha,getterFor:va};var ba=B;var ma=x;var ya=it;var wa=de;var Sa=k;var xa=Dr.CONFIGURABLE;var Oa=Mr;var ka=ga;var Pa=ka.enforce;var Ca=ka.get;var Ta=String;var Ia=Object.defineProperty;var Aa=ba("".slice);var $a=ba("".replace);var Ra=ba([].join);var Ea=Sa&&!ma(function(){return 8!==Ia(function(){},"length",{value:8}).length});var ja=String(String).split("String");var _a=Ar.exports=function(t,e,r){if("Symbol("===Aa(Ta(e),0,7))e="["+$a(Ta(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]";if(r&&r.getter)e="get "+e;if(r&&r.setter)e="set "+e;if(!wa(t,"name")||xa&&t.name!==e)if(Sa)Ia(t,"name",{value:e,configurable:true});else t.name=e;if(Ea&&r&&wa(r,"arity")&&t.length!==r.arity)Ia(t,"length",{value:r.arity});try{if(r&&wa(r,"constructor")&&r.constructor){if(Sa)Ia(t,"prototype",{writable:false})}else if(t.prototype)t.prototype=void 0}catch(a){}var i=Pa(t);if(!wa(i,"source"))i.source=Ra(ja,"string"==typeof e?e:"");return t};Function.prototype.toString=_a(function $y(){return ya(this)&&Ca(this).source||Oa(this)},"toString");var Na=Ar.exports;var Fa=it;var Da=or;var Va=Na;var Ba=te;var La=function(t,e,r,a){if(!a)a={};var i=a.enumerable;var n=void 0!==a.name?a.name:e;if(Fa(r))Va(r,n,a);if(a.global)if(i)t[e]=r;else Ba(e,r);else{try{if(!a.unsafe)delete t[e];else if(t[e])i=true}catch(o){}if(i)t[e]=r;else Da.f(t,e,{value:r,enumerable:false,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return t};var Ha={};var Ma=Math.ceil;var Ua=Math.floor;var za=Math.trunc||function Ry(t){var e=+t;return(e>0?Ua:Ma)(e)};var qa=za;var Ga=function(t){var e=+t;return e!==e||0===e?0:qa(e)};var Wa=Ga;var Ka=Math.max;var Ya=Math.min;var Ja=function(t,e){var r=Wa(t);return r<0?Ka(r+e,0):Ya(r,e)};var Xa=Ga;var Qa=Math.min;var Za=function(t){var e=Xa(t);return e>0?Qa(e,9007199254740991):0};var ti=Za;var ei=function(t){return ti(t.length)};var ri=rt;var ai=Ja;var ii=ei;var ni=function(t){return function(e,r,a){var i=ri(e);var n=ii(i);if(0===n)return!t&&-1;var o=ai(a,n);var s;if(t&&r!==r)while(n>o){s=i[o++];if(s!==s)return true}else for(;n>o;o++)if((t||o in i)&&i[o]===r)return t||o||0;return!t&&-1}};var oi={includes:ni(true),indexOf:ni(false)};var si=B;var li=de;var ci=rt;var fi=oi.indexOf;var ui=Xr;var hi=si([].push);var vi=function(t,e){var r=ci(t);var a=0;var i=[];var n;for(n in r)!li(ui,n)&&li(r,n)&&hi(i,n);while(e.length>a)if(li(r,n=e[a++]))~fi(i,n)||hi(i,n);return i};var di=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"];var pi=vi;var gi=di;var bi=gi.concat("length","prototype");Ha.f=Object.getOwnPropertyNames||function Ey(t){return pi(t,bi)};var mi={};mi.f=Object.getOwnPropertySymbols;var yi=ft;var wi=B;var Si=Ha;var xi=mi;var Oi=vr;var ki=wi([].concat);var Pi=yi("Reflect","ownKeys")||function jy(t){var e=Si.f(Oi(t));var r=xi.f;return r?ki(e,r(t)):e};var Ci=de;var Ti=Pi;var Ii=S;var Ai=or;var $i=function(t,e,r){var a=Ti(e);var i=Ai.f;var n=Ii.f;for(var o=0;o<a.length;o++){var s=a[o];if(!Ci(t,s)&&!(r&&Ci(r,s)))i(t,s,n(e,s))}};var Ri=x;var Ei=it;var ji=/#|\.prototype\./;var _i=function(t,e){var r=Fi[Ni(t)];return r===Vi?true:r===Di?false:Ei(e)?Ri(e):!!e};var Ni=_i.normalize=function(t){return String(t).replace(ji,".").toLowerCase()};var Fi=_i.data={};var Di=_i.NATIVE="N";var Vi=_i.POLYFILL="P";var Bi=_i;var Li=w;var Hi=S.f;var Mi=Ir;var Ui=La;var zi=te;var qi=$i;var Gi=Bi;var Wi=function(t,e){var r=t.target;var a=t.global;var i=t.stat;var n,o,s,l,c,f;if(a)o=Li;else if(i)o=Li[r]||zi(r,{});else o=Li[r]&&Li[r].prototype;if(o)for(s in e){c=e[s];if(t.dontCallGetSet){f=Hi(o,s);l=f&&f.value}else l=o[s];n=Gi(a?s:r+(i?".":"#")+s,t.forced);if(!n&&void 0!==l){if(typeof c==typeof l)continue;qi(c,l)}if(t.sham||l&&l.sham)Mi(c,"sham",true);Ui(o,s,c,t)}};var Ki=U;var Yi=Array.isArray||function _y(t){return"Array"===Ki(t)};var Ji=TypeError;var Xi=9007199254740991;var Qi=function(t){if(t>Xi)throw Ji("Maximum allowed index exceeded");return t};var Zi=k;var tn=or;var en=_;var rn=function(t,e,r){if(Zi)tn.f(t,e,en(0,r));else t[e]=r};var an=Ae;var nn=an("toStringTag");var on={};on[nn]="z";var sn="[object z]"===String(on);var ln=sn;var cn=it;var fn=U;var un=Ae;var hn=un("toStringTag");var vn=Object;var dn="Arguments"===fn(function(){return arguments}());var pn=function(t,e){try{return t[e]}catch(r){}};var gn=ln?fn:function(t){var e,r,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=pn(e=vn(t),hn))?r:dn?fn(e):"Object"===(a=fn(e))&&cn(e.callee)?"Arguments":a};var bn=B;var mn=x;var yn=it;var wn=gn;var Sn=ft;var xn=Mr;var On=function(){};var kn=Sn("Reflect","construct");var Pn=/^\s*(?:class|function)\b/;var Cn=bn(Pn.exec);var Tn=!Pn.test(On);var In=function Ny(t){if(!yn(t))return false;try{kn(On,[],t);return true}catch(e){return false}};var An=function Fy(t){if(!yn(t))return false;switch(wn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return false}try{return Tn||!!Cn(Pn,xn(t))}catch(e){return true}};An.sham=true;var $n=!kn||mn(function(){var t;return In(In.call)||!In(Object)||!In(function(){t=true})||t})?An:In;var Rn=Yi;var En=$n;var jn=ot;var _n=Ae;var Nn=_n("species");var Fn=Array;var Dn=function(t){var e;if(Rn(t)){e=t.constructor;if(En(e)&&(e===Fn||Rn(e.prototype)))e=void 0;else if(jn(e)){e=e[Nn];if(null===e)e=void 0}}return void 0===e?Fn:e};var Vn=Dn;var Bn=function(t,e){return new(Vn(t))(0===e?0:e)};var Ln=x;var Hn=Ae;var Mn=xt;var Un=Hn("species");var zn=function(t){return Mn>=51||!Ln(function(){var e=[];var r=e.constructor={};r[Un]=function(){return{foo:1}};return 1!==e[t](Boolean).foo})};var qn=Wi;var Gn=x;var Wn=Yi;var Kn=ot;var Yn=fe;var Jn=ei;var Xn=Qi;var Qn=rn;var Zn=Bn;var to=zn;var eo=Ae;var ro=xt;var ao=eo("isConcatSpreadable");var io=ro>=51||!Gn(function(){var t=[];t[ao]=false;return t.concat()[0]!==t});var no=function(t){if(!Kn(t))return false;var e=t[ao];return void 0!==e?!!e:Wn(t)};var oo=!io||!to("concat");qn({target:"Array",proto:true,arity:1,forced:oo},{concat:function Dy(t){var e=Yn(this);var r=Zn(e,0);var a=0;var i,n,o,s,l;for(i=-1,o=arguments.length;i<o;i++){l=i===-1?e:arguments[i];if(no(l)){s=Jn(l);Xn(a+s);for(n=0;n<s;n++,a++)if(n in l)Qn(r,a,l[n])}else{Xn(a+1);Qn(r,a++,l)}}r.length=a;return r}});var so=U;var lo=B;var co=function(t){if("Function"===so(t))return lo(t)};var fo=co;var uo=Ht;var ho=C;var vo=fo(fo.bind);var po=function(t,e){uo(t);return void 0===e?t:ho?vo(t,e):function(){return t.apply(e,arguments)}};var go=po;var bo=B;var mo=Y;var yo=fe;var wo=ei;var So=Bn;var xo=bo([].push);var Oo=function(t){var e=1===t;var r=2===t;var a=3===t;var i=4===t;var n=6===t;var o=7===t;var s=5===t||n;return function(l,c,f,u){var h=yo(l);var v=mo(h);var d=wo(v);var p=go(c,f);var g=0;var b=u||So;var m=e?b(l,d):r||o?b(l,0):void 0;var y,w;for(;d>g;g++)if(s||g in v){y=v[g];w=p(y,g,h);if(t)if(e)m[g]=w;else if(w)switch(t){case 3:return true;case 5:return y;case 6:return g;case 2:xo(m,y)}else switch(t){case 4:return false;case 7:xo(m,y)}}return n?-1:a||i?i:m}};var ko={forEach:Oo(0),map:Oo(1),filter:Oo(2),some:Oo(3),every:Oo(4),find:Oo(5),findIndex:Oo(6),filterReject:Oo(7)};var Po=Wi;var Co=ko.filter;var To=zn;var Io=To("filter");Po({target:"Array",proto:true,forced:!Io},{filter:function Vy(t){return Co(this,t,arguments.length>1?arguments[1]:void 0)}});var Ao={};var $o=vi;var Ro=di;var Eo=Object.keys||function By(t){return $o(t,Ro)};var jo=k;var _o=cr;var No=or;var Fo=vr;var Do=rt;var Vo=Eo;Ao.f=jo&&!_o?Object.defineProperties:function Ly(t,e){Fo(t);var r=Do(e);var a=Vo(e);var i=a.length;var n=0;var o;while(i>n)No.f(t,o=a[n++],r[o]);return t};var Bo=ft;var Lo=Bo("document","documentElement");var Ho=vr;var Mo=Ao;var Uo=di;var zo=Xr;var qo=Lo;var Go=Ge;var Wo=Jr;var Ko=">";var Yo="<";var Jo="prototype";var Xo="script";var Qo=Wo("IE_PROTO");var Zo=function(){};var ts=function(t){return Yo+Xo+Ko+t+Yo+"/"+Xo+Ko};var es=function(t){t.write(ts(""));t.close();var e=t.parentWindow.Object;t=null;return e};var rs=function(){var t=Go("iframe");var e="java"+Xo+":";var r;t.style.display="none";qo.appendChild(t);t.src=String(e);r=t.contentWindow.document;r.open();r.write(ts("document.F=Object"));r.close();return r.F};var as;var is=function(){try{as=new ActiveXObject("htmlfile")}catch(t){}is="undefined"!=typeof document?document.domain&&as?es(as):rs():es(as);var e=Uo.length;while(e--)delete is[Jo][Uo[e]];return is()};zo[Qo]=true;var ns=Object.create||function Hy(t,e){var r;if(null!==t){Zo[Jo]=Ho(t);r=new Zo;Zo[Jo]=null;r[Qo]=t}else r=is();return void 0===e?r:Mo.f(r,e)};var os=Ae;var ss=ns;var ls=or.f;var cs=os("unscopables");var fs=Array.prototype;if(void 0===fs[cs])ls(fs,cs,{configurable:true,value:ss(null)});var us=function(t){fs[cs][t]=true};var hs=Wi;var vs=ko.find;var ds=us;var ps="find";var gs=true;if(ps in[])Array(1)[ps](function(){gs=false});hs({target:"Array",proto:true,forced:gs},{find:function My(t){return vs(this,t,arguments.length>1?arguments[1]:void 0)}});ds(ps);var bs=Wi;var ms=ko.findIndex;var ys=us;var ws="findIndex";var Ss=true;if(ws in[])Array(1)[ws](function(){Ss=false});bs({target:"Array",proto:true,forced:Ss},{findIndex:function Uy(t){return ms(this,t,arguments.length>1?arguments[1]:void 0)}});ys(ws);var xs=Wi;var Os=oi.includes;var ks=x;var Ps=us;var Cs=ks(function(){return!Array(1).includes()});xs({target:"Array",proto:true,forced:Cs},{includes:function zy(t){return Os(this,t,arguments.length>1?arguments[1]:void 0)}});Ps("includes");var Ts=x;var Is=function(t,e){var r=[][t];return!!r&&Ts(function(){r.call(null,e||function(){return 1},1)})};var As=Wi;var $s=co;var Rs=oi.indexOf;var Es=Is;var js=$s([].indexOf);var _s=!!js&&1/js([1],1,-0)<0;var Ns=_s||!Es("indexOf");As({target:"Array",proto:true,forced:Ns},{indexOf:function qy(t){var e=arguments.length>1?arguments[1]:void 0;return _s?js(this,t,e)||0:Rs(this,t,e)}});var Fs={};var Ds=x;var Vs=!Ds(function(){function t(){}t.prototype.constructor=null;return Object.getPrototypeOf(new t)!==t.prototype});var Bs=de;var Ls=it;var Hs=fe;var Ms=Jr;var Us=Vs;var zs=Ms("IE_PROTO");var qs=Object;var Gs=qs.prototype;var Ws=Us?qs.getPrototypeOf:function(t){var e=Hs(t);if(Bs(e,zs))return e[zs];var r=e.constructor;if(Ls(r)&&e instanceof r)return r.prototype;return e instanceof qs?Gs:null};var Ks=x;var Ys=it;var Js=ot;var Xs=Ws;var Qs=La;var Zs=Ae;var tl=Zs("iterator");var el=false;var rl,al,il;if([].keys){il=[].keys();if(!("next"in il))el=true;else{al=Xs(Xs(il));if(al!==Object.prototype)rl=al}}var nl=!Js(rl)||Ks(function(){var t={};return rl[tl].call(t)!==t});if(nl)rl={};if(!Ys(rl[tl]))Qs(rl,tl,function(){return this});var ol={IteratorPrototype:rl,BUGGY_SAFARI_ITERATORS:el};var sl=or.f;var ll=de;var cl=Ae;var fl=cl("toStringTag");var ul=function(t,e,r){if(t&&!r)t=t.prototype;if(t&&!ll(t,fl))sl(t,fl,{configurable:true,value:e})};var hl=ol.IteratorPrototype;var vl=ns;var dl=_;var pl=ul;var gl=Fs;var bl=function(){return this};var ml=function(t,e,r,a){var i=e+" Iterator";t.prototype=vl(hl,{next:dl(+!a,r)});pl(t,i,false);gl[i]=bl;return t};var yl=B;var wl=Ht;var Sl=function(t,e,r){try{return yl(wl(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(a){}};var xl=ot;var Ol=function(t){return xl(t)||null===t};var kl=Ol;var Pl=String;var Cl=TypeError;var Tl=function(t){if(kl(t))return t;throw new Cl("Can't set "+Pl(t)+" as a prototype")};var Il=Sl;var Al=ot;var $l=Z;var Rl=Tl;var El=Object.setPrototypeOf||("__proto__"in{}?function(){var t=false;var e={};var r;try{r=Il(Object.prototype,"__proto__","set");r(e,[]);t=e instanceof Array}catch(a){}return function i(e,a){$l(e);Rl(a);if(!Al(e))return e;if(t)r(e,a);else e.__proto__=a;return e}}():void 0);var jl=Wi;var _l=A;var Nl=Dr;var Fl=it;var Dl=ml;var Vl=Ws;var Bl=El;var Ll=ul;var Hl=Ir;var Ml=La;var Ul=Ae;var zl=Fs;var ql=ol;var Gl=Nl.PROPER;var Wl=Nl.CONFIGURABLE;var Kl=ql.IteratorPrototype;var Yl=ql.BUGGY_SAFARI_ITERATORS;var Jl=Ul("iterator");var Xl="keys";var Ql="values";var Zl="entries";var tc=function(){return this};var ec=function(t,e,r,a,i,n,o){Dl(r,e,a);var s=function(t){if(t===i&&h)return h;if(!Yl&&t&&t in f)return f[t];switch(t){case Xl:return function e(){return new r(this,t)};case Ql:return function a(){return new r(this,t)};case Zl:return function n(){return new r(this,t)}}return function(){return new r(this)}};var l=e+" Iterator";var c=false;var f=t.prototype;var u=f[Jl]||f["@@iterator"]||i&&f[i];var h=!Yl&&u||s(i);var v="Array"===e?f.entries||u:u;var d,p,g;if(v){d=Vl(v.call(new t));if(d!==Object.prototype&&d.next){if(Vl(d)!==Kl)if(Bl)Bl(d,Kl);else if(!Fl(d[Jl]))Ml(d,Jl,tc);Ll(d,l,true)}}if(Gl&&i===Ql&&u&&u.name!==Ql)if(Wl)Hl(f,"name",Ql);else{c=true;h=function b(){return _l(u,this)}}if(i){p={values:s(Ql),keys:n?h:s(Xl),entries:s(Zl)};if(o){for(g in p)if(Yl||c||!(g in f))Ml(f,g,p[g])}else jl({target:e,proto:true,forced:Yl||c},p)}if(f[Jl]!==h)Ml(f,Jl,h,{name:i});zl[e]=h;return p};var rc=function(t,e){return{value:t,done:e}};var ac=rt;var ic=us;var nc=Fs;var oc=ga;var sc=or.f;var lc=ec;var cc=rc;var fc=k;var uc="Array Iterator";var hc=oc.set;var vc=oc.getterFor(uc);var dc=lc(Array,"Array",function(t,e){hc(this,{type:uc,target:ac(t),index:0,kind:e})},function(){var t=vc(this);var e=t.target;var r=t.index++;if(!e||r>=e.length){t.target=void 0;return cc(void 0,true)}switch(t.kind){case"keys":return cc(r,false);case"values":return cc(e[r],false)}return cc([r,e[r]],false)},"values");var pc=nc.Arguments=nc.Array;ic("keys");ic("values");ic("entries");if(fc&&"values"!==pc.name)try{sc(pc,"name",{value:"values"})}catch(gc){}var bc=Wi;var mc=B;var yc=Y;var wc=rt;var Sc=Is;var xc=mc([].join);var Oc=yc!==Object;var kc=Oc||!Sc("join",",");bc({target:"Array",proto:true,forced:kc},{join:function Gy(t){return xc(wc(this),void 0===t?",":t)}});var Pc=Wi;var Cc=ko.map;var Tc=zn;var Ic=Tc("map");Pc({target:"Array",proto:true,forced:!Ic},{map:function Wy(t){return Cc(this,t,arguments.length>1?arguments[1]:void 0)}});var Ac=Wi;var $c=B;var Rc=Yi;var Ec=$c([].reverse);var jc=[1,2];Ac({target:"Array",proto:true,forced:String(jc)===String(jc.reverse())},{reverse:function Ky(){if(Rc(this))this.length=this.length;return Ec(this)}});var _c=B;var Nc=_c([].slice);var Fc=Wi;var Dc=Yi;var Vc=$n;var Bc=ot;var Lc=Ja;var Hc=ei;var Mc=rt;var Uc=rn;var zc=Ae;var qc=zn;var Gc=Nc;var Wc=qc("slice");var Kc=zc("species");var Yc=Array;var Jc=Math.max;Fc({target:"Array",proto:true,forced:!Wc},{slice:function Yy(t,e){var r=Mc(this);var a=Hc(r);var i=Lc(t,a);var n=Lc(void 0===e?a:e,a);var o,s,l;if(Dc(r)){o=r.constructor;if(Vc(o)&&(o===Yc||Dc(o.prototype)))o=void 0;else if(Bc(o)){o=o[Kc];if(null===o)o=void 0}if(o===Yc||void 0===o)return Gc(r,i,n)}s=new(void 0===o?Yc:o)(Jc(n-i,0));for(l=0;i<n;i++,l++)if(i in r)Uc(s,l,r[i]);s.length=l;return s}});var Xc=Dt;var Qc=TypeError;var Zc=function(t,e){if(!delete t[e])throw new Qc("Cannot delete property "+Xc(e)+" of "+Xc(t))};var tf=gn;var ef=String;var rf=function(t){if("Symbol"===tf(t))throw new TypeError("Cannot convert a Symbol value to a string");return ef(t)};var af=Nc;var nf=Math.floor;var of=function(t,e){var r=t.length;if(r<8){var a=1;var i,n;while(a<r){n=a;i=t[a];while(n&&e(t[n-1],i)>0)t[n]=t[--n];if(n!==a++)t[n]=i}}else{var o=nf(r/2);var s=of(af(t,0,o),e);var l=of(af(t,o),e);var c=s.length;var f=l.length;var u=0;var h=0;while(u<c||h<f)t[u+h]=u<c&&h<f?e(s[u],l[h])<=0?s[u++]:l[h++]:u<c?s[u++]:l[h++]}return t};var sf=of;var lf=vt;var cf=lf.match(/firefox\/(\d+)/i);var ff=!!cf&&+cf[1];var uf=vt;var hf=/MSIE|Trident/.test(uf);var vf=vt;var df=vf.match(/AppleWebKit\/(\d+)\./);var pf=!!df&&+df[1];var gf=Wi;var bf=B;var mf=Ht;var yf=fe;var wf=ei;var Sf=Zc;var xf=rf;var Of=x;var kf=sf;var Pf=Is;var Cf=ff;var Tf=hf;var If=xt;var Af=pf;var $f=[];var Rf=bf($f.sort);var Ef=bf($f.push);var jf=Of(function(){$f.sort(void 0)});var _f=Of(function(){$f.sort(null)});var Nf=Pf("sort");var Ff=!Of(function(){if(If)return If<70;if(Cf&&Cf>3)return;if(Tf)return true;if(Af)return Af<603;var t="";var e,r,a,i;for(e=65;e<76;e++){r=String.fromCharCode(e);switch(e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(i=0;i<47;i++)$f.push({k:r+i,v:a})}$f.sort(function(t,e){return e.v-t.v});for(i=0;i<$f.length;i++){r=$f[i].k.charAt(0);if(t.charAt(t.length-1)!==r)t+=r}return"DGBEFHACIJK"!==t});var Df=jf||!_f||!Nf||!Ff;var Vf=function(t){return function(e,r){if(void 0===r)return-1;if(void 0===e)return 1;if(void 0!==t)return+t(e,r)||0;return xf(e)>xf(r)?1:-1}};gf({target:"Array",proto:true,forced:Df},{sort:function Jy(t){if(void 0!==t)mf(t);var e=yf(this);if(Ff)return void 0===t?Rf(e):Rf(e,t);var r=[];var a=wf(e);var i,n;for(n=0;n<a;n++)if(n in e)Ef(r,e[n]);kf(r,Vf(t));i=wf(r);n=0;while(n<i)e[n]=r[n++];while(n<a)Sf(e,n++);return e}});var Bf=k;var Lf=Yi;var Hf=TypeError;var Mf=Object.getOwnPropertyDescriptor;var Uf=Bf&&!function(){if(void 0!==this)return true;try{Object.defineProperty([],"length",{writable:false}).length=1}catch(t){return t instanceof TypeError}}();var zf=Uf?function(t,e){if(Lf(t)&&!Mf(t,"length").writable)throw new Hf("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e};var qf=Wi;var Gf=fe;var Wf=Ja;var Kf=Ga;var Yf=ei;var Jf=zf;var Xf=Qi;var Qf=Bn;var Zf=rn;var tu=Zc;var eu=zn;var ru=eu("splice");var au=Math.max;var iu=Math.min;qf({target:"Array",proto:true,forced:!ru},{splice:function Xy(t,e){var r=Gf(this);var a=Yf(r);var i=Wf(t,a);var n=arguments.length;var o,s,l,c,f,u;if(0===n)o=s=0;else if(1===n){o=0;s=a-i}else{o=n-2;s=iu(au(Kf(e),0),a-i)}Xf(a+o-s);l=Qf(r,s);for(c=0;c<s;c++){f=i+c;if(f in r)Zf(l,c,r[f])}l.length=s;if(o<s){for(c=i;c<a-s;c++){f=c+s;u=c+o;if(f in r)r[u]=r[f];else tu(r,u)}for(c=a;c>a-s+o;c--)tu(r,c-1)}else if(o>s)for(c=a-s;c>i;c--){f=c+s-1;u=c+o-1;if(f in r)r[u]=r[f];else tu(r,u)}for(c=0;c<o;c++)r[c+i]=arguments[c+2];Jf(r,a-s+o);return l}});var nu=Wi;var ou=x;var su=fe;var lu=Ve;var cu=ou(function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})});nu({target:"Date",proto:true,arity:1,forced:cu},{toJSON:function Qy(t){var e=su(this);var r=lu(e,"number");return"number"==typeof r&&!isFinite(r)?null:e.toISOString()}});var fu=w;var uu=fu;var hu=it;var vu=ot;var du=El;var pu=function(t,e,r){var a,i;if(du&&hu(a=e.constructor)&&a!==r&&vu(i=a.prototype)&&i!==r.prototype)du(t,i);return t};var gu=B;var bu=gu(1..valueOf);var mu="\t\n\v\f\r      "+"          　\u2028\u2029\ufeff";var yu=B;var wu=Z;var Su=rf;var xu=mu;var Ou=yu("".replace);var ku=RegExp("^["+xu+"]+");var Pu=RegExp("(^|[^"+xu+"])["+xu+"]+$");var Cu=function(t){return function(e){var r=Su(wu(e));if(1&t)r=Ou(r,ku,"");if(2&t)r=Ou(r,Pu,"$1");return r}};var Tu={start:Cu(1),end:Cu(2),trim:Cu(3)};var Iu=Wi;var Au=Xt;var $u=k;var Ru=w;var Eu=uu;var ju=B;var _u=Bi;var Nu=de;var Fu=pu;var Du=ht;var Vu=Nt;var Bu=Ve;var Lu=x;var Hu=Ha.f;var Mu=S.f;var Uu=or.f;var zu=bu;var qu=Tu.trim;var Gu="Number";var Wu=Ru[Gu];Eu[Gu];var Ku=Wu.prototype;var Yu=Ru.TypeError;var Ju=ju("".slice);var Xu=ju("".charCodeAt);var Qu=function(t){var e=Bu(t,"number");return"bigint"==typeof e?e:Zu(e)};var Zu=function(t){var e=Bu(t,"number");var r,a,i,n,o,s,l,c;if(Vu(e))throw new Yu("Cannot convert a Symbol value to a number");if("string"==typeof e&&e.length>2){e=qu(e);r=Xu(e,0);if(43===r||45===r){a=Xu(e,2);if(88===a||120===a)return NaN}else if(48===r){switch(Xu(e,1)){case 66:case 98:i=2;n=49;break;case 79:case 111:i=8;n=55;break;default:return+e}o=Ju(e,2);s=o.length;for(l=0;l<s;l++){c=Xu(o,l);if(c<48||c>n)return NaN}return parseInt(o,i)}}return+e};var th=_u(Gu,!Wu(" 0o1")||!Wu("0b1")||Wu("+0x1"));var eh=function(t){return Du(Ku,t)&&Lu(function(){zu(t)})};var rh=function Zy(t){var e=arguments.length<1?0:Wu(Qu(t));return eh(this)?Fu(Object(e),this,rh):e};rh.prototype=Ku;if(th&&!Au)Ku.constructor=rh;Iu({global:true,constructor:true,wrap:true,forced:th},{Number:rh});var ah=function(t,e){for(var r=$u?Hu(e):("MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,"+"EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,"+"fromString,range").split(","),a=0,i;r.length>a;a++)if(Nu(e,i=r[a])&&!Nu(t,i))Uu(t,i,Mu(e,i))};if(th||Au)ah(Eu[Gu],Wu);var ih=k;var nh=B;var oh=A;var sh=x;var lh=Eo;var ch=mi;var fh=$;var uh=fe;var hh=Y;var vh=Object.assign;var dh=Object.defineProperty;var ph=nh([].concat);var gh=!vh||sh(function(){if(ih&&1!==vh({b:1},vh(dh({},"a",{enumerable:true,get:function(){dh(this,"b",{value:3,enumerable:false})}}),{b:2})).b)return true;var t={};var e={};var r=Symbol("assign detection");var a="abcdefghijklmnopqrst";t[r]=7;a.split("").forEach(function(t){e[t]=t});return 7!==vh({},t)[r]||lh(vh({},e)).join("")!==a})?function tw(t,e){var r=uh(t);var a=arguments.length;var i=1;var n=ch.f;var o=fh.f;while(a>i){var s=hh(arguments[i++]);var l=n?ph(lh(s),n(s)):lh(s);var c=l.length;var f=0;var u;while(c>f){u=l[f++];if(!ih||oh(o,s,u))r[u]=s[u]}}return r}:vh;var bh=Wi;var mh=gh;bh({target:"Object",stat:true,arity:2,forced:Object.assign!==mh},{assign:mh});var yh=k;var wh=x;var Sh=B;var xh=Ws;var Oh=Eo;var kh=rt;var Ph=$.f;var Ch=Sh(Ph);var Th=Sh([].push);var Ih=yh&&wh(function(){var t=Object.create(null);t[2]=2;return!Ch(t,2)});var Ah=function(t){return function(e){var r=kh(e);var a=Oh(r);var i=Ih&&null===xh(r);var n=a.length;var o=0;var s=[];var l;while(n>o){l=a[o++];if(!yh||(i?l in r:Ch(r,l)))Th(s,t?[l,r[l]]:r[l])}return s}};var $h={entries:Ah(true),values:Ah(false)};var Rh=Wi;var Eh=$h.entries;Rh({target:"Object",stat:true},{entries:function ew(t){return Eh(t)}});var jh=Wi;var _h=fe;var Nh=Eo;var Fh=x;var Dh=Fh(function(){Nh(1)});jh({target:"Object",stat:true,forced:Dh},{keys:function rw(t){return Nh(_h(t))}});var Vh=sn;var Bh=gn;var Lh=Vh?{}.toString:function aw(){return"[object "+Bh(this)+"]"};var Hh=sn;var Mh=La;var Uh=Lh;if(!Hh)Mh(Object.prototype,"toString",Uh,{unsafe:true});var zh=w;var qh=x;var Gh=B;var Wh=rf;var Kh=Tu.trim;var Yh=mu;var Jh=Gh("".charAt);var Xh=zh.parseFloat;var Qh=zh.Symbol;var Zh=Qh&&Qh.iterator;var tv=1/Xh(Yh+"-0")!==-(1/0)||Zh&&!qh(function(){Xh(Object(Zh))});var ev=tv?function iw(t){var e=Kh(Wh(t));var r=Xh(e);return 0===r&&"-"===Jh(e,0)?-0:r}:Xh;var rv=Wi;var av=ev;rv({global:true,forced:parseFloat!==av},{parseFloat:av});var iv=w;var nv=x;var ov=B;var sv=rf;var lv=Tu.trim;var cv=mu;var fv=iv.parseInt;var uv=iv.Symbol;var hv=uv&&uv.iterator;var vv=/^[+-]?0x/i;var dv=ov(vv.exec);var pv=8!==fv(cv+"08")||22!==fv(cv+"0x16")||hv&&!nv(function(){fv(Object(hv))});var gv=pv?function nw(t,e){var r=lv(sv(t));return fv(r,e>>>0||(dv(vv,r)?16:10))}:fv;var bv=Wi;var mv=gv;bv({global:true,forced:parseInt!==mv},{parseInt:mv});var yv=ot;var wv=U;var Sv=Ae;var xv=Sv("match");var Ov=function(t){var e;return yv(t)&&(void 0!==(e=t[xv])?!!e:"RegExp"===wv(t))};var kv=vr;var Pv=function(){var t=kv(this);var e="";if(t.hasIndices)e+="d";if(t.global)e+="g";if(t.ignoreCase)e+="i";if(t.multiline)e+="m";if(t.dotAll)e+="s";if(t.unicode)e+="u";if(t.unicodeSets)e+="v";if(t.sticky)e+="y";return e};var Cv=A;var Tv=de;var Iv=ht;var Av=Pv;var $v=RegExp.prototype;var Rv=function(t){var e=t.flags;return void 0===e&&!("flags"in $v)&&!Tv(t,"flags")&&Iv($v,t)?Cv(Av,t):e};var Ev=x;var jv=w;var _v=jv.RegExp;var Nv=Ev(function(){var t=_v("a","y");t.lastIndex=2;return null!==t.exec("abcd")});var Fv=Nv||Ev(function(){return!_v("a","y").sticky});var Dv=Nv||Ev(function(){var t=_v("^r","gy");t.lastIndex=2;return null!==t.exec("str")});var Vv={BROKEN_CARET:Dv,MISSED_STICKY:Fv,UNSUPPORTED_Y:Nv};var Bv=or.f;var Lv=function(t,e,r){r in t||Bv(t,r,{configurable:true,get:function(){return e[r]},set:function(t){e[r]=t}})};var Hv=Na;var Mv=or;var Uv=function(t,e,r){if(r.get)Hv(r.get,e,{getter:true});if(r.set)Hv(r.set,e,{setter:true});return Mv.f(t,e,r)};var zv=ft;var qv=Uv;var Gv=Ae;var Wv=k;var Kv=Gv("species");var Yv=function(t){var e=zv(t);if(Wv&&e&&!e[Kv])qv(e,Kv,{configurable:true,get:function(){return this}})};var Jv=x;var Xv=w;var Qv=Xv.RegExp;var Zv=Jv(function(){var t=Qv(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)});var td=x;var ed=w;var rd=ed.RegExp;var ad=td(function(){var t=rd("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")});var id=k;var nd=w;var od=B;var sd=Bi;var ld=pu;var cd=Ir;var fd=ns;var ud=Ha.f;var hd=ht;var vd=Ov;var dd=rf;var pd=Rv;var gd=Vv;var bd=Lv;var md=La;var yd=x;var wd=de;var Sd=ga.enforce;var xd=Yv;var Od=Ae;var kd=Zv;var Pd=ad;var Cd=Od("match");var Td=nd.RegExp;var Id=Td.prototype;var Ad=nd.SyntaxError;var $d=od(Id.exec);var Rd=od("".charAt);var Ed=od("".replace);var jd=od("".indexOf);var _d=od("".slice);var Nd=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/;var Fd=/a/g;var Dd=/a/g;var Vd=new Td(Fd)!==Fd;var Bd=gd.MISSED_STICKY;var Ld=gd.UNSUPPORTED_Y;var Hd=id&&(!Vd||Bd||kd||Pd||yd(function(){Dd[Cd]=false;return Td(Fd)!==Fd||Td(Dd)===Dd||"/a/i"!==String(Td(Fd,"i"))}));var Md=function(t){var e=t.length;var r=0;var a="";var i=false;var n;for(;r<=e;r++){n=Rd(t,r);if("\\"===n){a+=n+Rd(t,++r);continue}if(!i&&"."===n)a+="[\\s\\S]";else{if("["===n)i=true;else if("]"===n)i=false;a+=n}}return a};var Ud=function(t){var e=t.length;var r=0;var a="";var i=[];var n=fd(null);var o=false;var s=false;var l=0;var c="";var f;for(;r<=e;r++){f=Rd(t,r);if("\\"===f)f+=Rd(t,++r);else if("]"===f)o=false;else if(!o)switch(true){case"["===f:o=true;break;case"("===f:if($d(Nd,_d(t,r+1))){r+=2;s=true}a+=f;l++;continue;case">"===f&&s:if(""===c||wd(n,c))throw new Ad("Invalid capture group name");n[c]=true;i[i.length]=[c,l];s=false;c="";continue}if(s)c+=f;else a+=f}return[a,i]};if(sd("RegExp",Hd)){var zd=function ow(t,e){var r=hd(Id,this);var a=vd(t);var i=void 0===e;var n=[];var o=t;var s,l,c,f,u,h;if(!r&&a&&i&&t.constructor===zd)return t;if(a||hd(Id,t)){t=t.source;if(i)e=pd(o)}t=void 0===t?"":dd(t);e=void 0===e?"":dd(e);o=t;if(kd&&"dotAll"in Fd){l=!!e&&jd(e,"s")>-1;if(l)e=Ed(e,/s/g,"")}s=e;if(Bd&&"sticky"in Fd){c=!!e&&jd(e,"y")>-1;if(c&&Ld)e=Ed(e,/y/g,"")}if(Pd){f=Ud(t);t=f[0];n=f[1]}u=ld(Td(t,e),r?this:Id,zd);if(l||c||n.length){h=Sd(u);if(l){h.dotAll=true;h.raw=zd(Md(t),s)}if(c)h.sticky=true;if(n.length)h.groups=n}if(t!==o)try{cd(u,"source",""===o?"(?:)":o)}catch(v){}return u};for(var qd=ud(Td),Gd=0;qd.length>Gd;)bd(zd,Td,qd[Gd++]);Id.constructor=zd;zd.prototype=Id;md(nd,"RegExp",zd,{constructor:true})}xd("RegExp");var Wd=A;var Kd=B;var Yd=rf;var Jd=Pv;var Xd=Vv;var Qd=se;var Zd=ns;var tp=ga.get;var ep=Zv;var rp=ad;var ap=Qd("native-string-replace",String.prototype.replace);var ip=RegExp.prototype.exec;var np=ip;var op=Kd("".charAt);var sp=Kd("".indexOf);var lp=Kd("".replace);var cp=Kd("".slice);var fp=function(){var t=/a/;var e=/b*/g;Wd(ip,t,"a");Wd(ip,e,"a");return 0!==t.lastIndex||0!==e.lastIndex}();var up=Xd.BROKEN_CARET;var hp=void 0!==/()??/.exec("")[1];var vp=fp||hp||up||ep||rp;if(vp)np=function sw(t){var e=this;var r=tp(e);var a=Yd(t);var i=r.raw;var n,o,s,l,c,f,u;if(i){i.lastIndex=e.lastIndex;n=Wd(np,i,a);e.lastIndex=i.lastIndex;return n}var h=r.groups;var v=up&&e.sticky;var d=Wd(Jd,e);var p=e.source;var g=0;var b=a;if(v){d=lp(d,"y","");if(sp(d,"g")===-1)d+="g";b=cp(a,e.lastIndex);if(e.lastIndex>0&&(!e.multiline||e.multiline&&"\n"!==op(a,e.lastIndex-1))){p="(?: "+p+")";b=" "+b;g++}o=new RegExp("^(?:"+p+")",d)}if(hp)o=new RegExp("^"+p+"$(?!\\s)",d);if(fp)s=e.lastIndex;l=Wd(ip,v?o:e,b);if(v)if(l){l.input=cp(l.input,g);l[0]=cp(l[0],g);l.index=e.lastIndex;e.lastIndex+=l[0].length}else e.lastIndex=0;else if(fp&&l)e.lastIndex=e.global?l.index+l[0].length:s;if(hp&&l&&l.length>1)Wd(ap,l[0],o,function(){for(c=1;c<arguments.length-2;c++)if(void 0===arguments[c])l[c]=void 0});if(l&&h){l.groups=f=Zd(null);for(c=0;c<h.length;c++){u=h[c];f[u[0]]=l[u[1]]}}return l};var dp=np;var pp=Wi;var gp=dp;pp({target:"RegExp",proto:true,forced:/./.exec!==gp},{exec:gp});var bp=Dr.PROPER;var mp=La;var yp=vr;var wp=rf;var Sp=x;var xp=Rv;var Op="toString";var kp=RegExp.prototype;var Pp=kp[Op];var Cp=Sp(function(){return"/a/b"!==Pp.call({source:"a",flags:"b"})});var Tp=bp&&Pp.name!==Op;if(Cp||Tp)mp(kp,Op,function lw(){var t=yp(this);var e=wp(t.source);var r=wp(xp(t));return"/"+e+"/"+r},{unsafe:true});var Ip=Ov;var Ap=TypeError;var $p=function(t){if(Ip(t))throw new Ap("The method doesn't accept regular expressions");return t};var Rp=Ae;var Ep=Rp("match");var jp=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{e[Ep]=false;return"/./"[t](e)}catch(a){}}return false};var _p=Wi;var Np=B;var Fp=$p;var Dp=Z;var Vp=rf;var Bp=jp;var Lp=Np("".indexOf);_p({target:"String",proto:true,forced:!Bp("includes")},{includes:function cw(t){return!!~Lp(Vp(Dp(this)),Vp(Fp(t)),arguments.length>1?arguments[1]:void 0)}});var Hp=C;var Mp=Function.prototype;var Up=Mp.apply;var zp=Mp.call;var qp="object"==typeof Reflect&&Reflect.apply||(Hp?zp.bind(Up):function(){return zp.apply(Up,arguments)});var Gp=A;var Wp=La;var Kp=dp;var Yp=x;var Jp=Ae;var Xp=Ir;var Qp=Jp("species");var Zp=RegExp.prototype;var tg=function(t,e,r,a){var i=Jp(t);var n=!Yp(function(){var e={};e[i]=function(){return 7};return 7!==""[t](e)});var o=n&&!Yp(function(){var e=false;var r=/a/;if("split"===t){r={};r.constructor={};r.constructor[Qp]=function(){return r};r.flags="";r[i]=/./[i]}r.exec=function(){e=true;return null};r[i]("");return!e});if(!n||!o||r){var s=/./[i];var l=e(i,""[t],function(t,e,r,a,i){var o=e.exec;if(o===Kp||o===Zp.exec){if(n&&!i)return{done:true,value:Gp(s,e,r,a)};return{done:true,value:Gp(t,r,e,a)}}return{done:false}});Wp(String.prototype,t,l[0]);Wp(Zp,i,l[1])}if(a)Xp(Zp[i],"sham",true)};var eg=B;var rg=Ga;var ag=rf;var ig=Z;var ng=eg("".charAt);var og=eg("".charCodeAt);var sg=eg("".slice);var lg=function(t){return function(e,r){var a=ag(ig(e));var i=rg(r);var n=a.length;var o,s;if(i<0||i>=n)return t?"":void 0;o=og(a,i);return o<55296||o>56319||i+1===n||(s=og(a,i+1))<56320||s>57343?t?ng(a,i):o:t?sg(a,i,i+2):(o-55296<<10)+(s-56320)+65536}};var cg={codeAt:lg(false),charAt:lg(true)};var fg=cg.charAt;var ug=function(t,e,r){return e+(r?fg(t,e).length:1)};var hg=B;var vg=fe;var dg=Math.floor;var pg=hg("".charAt);var gg=hg("".replace);var bg=hg("".slice);var mg=/\$([$&'`]|\d{1,2}|<[^>]*>)/g;var yg=/\$([$&'`]|\d{1,2})/g;var wg=function(t,e,r,a,i,n){var o=r+t.length;var s=a.length;var l=yg;if(void 0!==i){i=vg(i);l=mg}return gg(n,l,function(n,l){var c;switch(pg(l,0)){case"$":return"$";case"&":return t;case"`":return bg(e,0,r);case"'":return bg(e,o);case"<":c=i[bg(l,1,-1)];break;default:var f=+l;if(0===f)return n;if(f>s){var u=dg(f/10);if(0===u)return n;if(u<=s)return void 0===a[u-1]?pg(l,1):a[u-1]+pg(l,1);return n}c=a[f-1]}return void 0===c?"":c})};var Sg=A;var xg=vr;var Og=it;var kg=U;var Pg=dp;var Cg=TypeError;var Tg=function(t,e){var r=t.exec;if(Og(r)){var a=Sg(r,t,e);if(null!==a)xg(a);return a}if("RegExp"===kg(t))return Sg(Pg,t,e);throw new Cg("RegExp#exec called on incompatible receiver")};var Ig=qp;var Ag=A;var $g=B;var Rg=tg;var Eg=x;var jg=vr;var _g=it;var Ng=J;var Fg=Ga;var Dg=Za;var Vg=rf;var Bg=Z;var Lg=ug;var Hg=zt;var Mg=wg;var Ug=Tg;var zg=Ae;var qg=zg("replace");var Gg=Math.max;var Wg=Math.min;var Kg=$g([].concat);var Yg=$g([].push);var Jg=$g("".indexOf);var Xg=$g("".slice);var Qg=function(t){return void 0===t?t:String(t)};var Zg=function(){return"$0"==="a".replace(/./,"$0")}();var tb=function(){if(/./[qg])return""===/./[qg]("a","$0");return false}();var eb=!Eg(function(){var t=/./;t.exec=function(){var t=[];t.groups={a:"7"};return t};return"7"!=="".replace(t,"$<a>")});Rg("replace",function(t,e,r){var a=tb?"$":"$0";return[function i(t,r){var a=Bg(this);var i=Ng(t)?void 0:Hg(t,qg);return i?Ag(i,t,a,r):Ag(e,Vg(a),t,r)},function(t,i){var n=jg(this);var o=Vg(t);if("string"==typeof i&&Jg(i,a)===-1&&Jg(i,"$<")===-1){var s=r(e,n,o,i);if(s.done)return s.value}var l=_g(i);if(!l)i=Vg(i);var c=n.global;var f;if(c){f=n.unicode;n.lastIndex=0}var u=[];var h;while(true){h=Ug(n,o);if(null===h)break;Yg(u,h);if(!c)break;var v=Vg(h[0]);if(""===v)n.lastIndex=Lg(o,Dg(n.lastIndex),f)}var d="";var p=0;for(var g=0;g<u.length;g++){h=u[g];var b=Vg(h[0]);var m=Gg(Wg(Fg(h.index),o.length),0);var y=[];var w;for(var S=1;S<h.length;S++)Yg(y,Qg(h[S]));var x=h.groups;if(l){var O=Kg([b],y,m,o);if(void 0!==x)Yg(O,x);w=Vg(Ig(i,void 0,O))}else w=Mg(b,o,m,y,x,i);if(m>=p){d+=Xg(o,p,m)+w;p=m+b.length}}return d+Xg(o,p)}]},!eb||!Zg||tb);var rb=Object.is||function fw(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e};var ab=A;var ib=tg;var nb=vr;var ob=J;var sb=Z;var lb=rb;var cb=rf;var fb=zt;var ub=Tg;ib("search",function(t,e,r){return[function a(e){var r=sb(this);var a=ob(e)?void 0:fb(e,t);return a?ab(a,e,r):new RegExp(e)[t](cb(r))},function(t){var a=nb(this);var i=cb(t);var n=r(e,a,i);if(n.done)return n.value;var o=a.lastIndex;if(!lb(o,0))a.lastIndex=0;var s=ub(a,i);if(!lb(a.lastIndex,o))a.lastIndex=o;return null===s?-1:s.index}]});var hb=$n;var vb=Dt;var db=TypeError;var pb=function(t){if(hb(t))return t;throw new db(vb(t)+" is not a constructor")};var gb=vr;var bb=pb;var mb=J;var yb=Ae;var wb=yb("species");var Sb=function(t,e){var r=gb(t).constructor;var a;return void 0===r||mb(a=gb(r)[wb])?e:bb(a)};var xb=A;var Ob=B;var kb=tg;var Pb=vr;var Cb=J;var Tb=Z;var Ib=Sb;var Ab=ug;var $b=Za;var Rb=rf;var Eb=zt;var jb=Tg;var _b=Vv;var Nb=x;var Fb=_b.UNSUPPORTED_Y;var Db=4294967295;var Vb=Math.min;var Bb=Ob([].push);var Lb=Ob("".slice);var Hb=!Nb(function(){var t=/(?:)/;var e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]});var Mb="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;kb("split",function(t,e,r){var a="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:xb(e,this,t,r)}:e;return[function i(e,r){var i=Tb(this);var n=Cb(e)?void 0:Eb(e,t);return n?xb(n,e,i,r):xb(a,Rb(i),e,r)},function(t,i){var n=Pb(this);var o=Rb(t);if(!Mb){var s=r(a,n,o,i,a!==e);if(s.done)return s.value}var l=Ib(n,RegExp);var c=n.unicode;var f=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(Fb?"g":"y");var u=new l(Fb?"^(?:"+n.source+")":n,f);var h=void 0===i?Db:i>>>0;if(0===h)return[];if(0===o.length)return null===jb(u,o)?[o]:[];var v=0;var d=0;var p=[];while(d<o.length){u.lastIndex=Fb?0:d;var g=jb(u,Fb?Lb(o,d):o);var b;if(null===g||(b=Vb($b(u.lastIndex+(Fb?d:0)),o.length))===v)d=Ab(o,d,c);else{Bb(p,Lb(o,v,d));if(p.length===h)return p;for(var m=1;m<=g.length-1;m++){Bb(p,g[m]);if(p.length===h)return p}d=v=b}}Bb(p,Lb(o,v));return p}]},Mb||!Hb,Fb);var Ub=Dr.PROPER;var zb=x;var qb=mu;var Gb="​᠎";var Wb=function(t){return zb(function(){return!!qb[t]()||Gb[t]()!==Gb||Ub&&qb[t].name!==t})};var Kb=Wi;var Yb=Tu.trim;var Jb=Wb;Kb({target:"String",proto:true,forced:Jb("trim")},{trim:function uw(){return Yb(this)}});var Xb={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};var Qb=Ge;var Zb=Qb("span").classList;var tm=Zb&&Zb.constructor&&Zb.constructor.prototype;var em=tm===Object.prototype?void 0:tm;var rm=ko.forEach;var am=Is;var im=am("forEach");var nm=!im?function hw(t){return rm(this,t,arguments.length>1?arguments[1]:void 0)}:[].forEach;var om=w;var sm=Xb;var lm=em;var cm=nm;var fm=Ir;var um=function(t){if(t&&t.forEach!==cm)try{fm(t,"forEach",cm)}catch(e){t.forEach=cm}};for(var hm in sm)if(sm[hm])um(om[hm]&&om[hm].prototype);um(lm);var vm=w;var dm=Xb;var pm=em;var gm=dc;var bm=Ir;var mm=ul;var ym=Ae;var wm=ym("iterator");var Sm=gm.values;var xm=function(t,e){if(t){if(t[wm]!==Sm)try{bm(t,wm,Sm)}catch(r){t[wm]=Sm}mm(t,e,true);if(dm[e])for(var a in gm)if(t[a]!==gm[a])try{bm(t,a,gm[a])}catch(r){t[a]=gm[a]}}};for(var Om in dm)xm(vm[Om]&&vm[Om].prototype,Om);xm(pm,"DOMTokenList");var km=Wi;var Pm=x;var Cm=fe;var Tm=Ws;var Im=Vs;var Am=Pm(function(){Tm(1)});km({target:"Object",stat:true,forced:Am,sham:!Im},{getPrototypeOf:function vw(t){return Tm(Cm(t))}});var $m=Wi;var Rm=co;var Em=S.f;var jm=Za;var _m=rf;var Nm=$p;var Fm=Z;var Dm=jp;var Vm=Rm("".slice);var Bm=Math.min;var Lm=Dm("endsWith");var Hm=!Lm&&!!function(){var t=Em(String.prototype,"endsWith");return t&&!t.writable}();$m({target:"String",proto:true,forced:!Hm&&!Lm},{endsWith:function dw(t){var e=_m(Fm(this));Nm(t);var r=arguments.length>1?arguments[1]:void 0;var a=e.length;var i=void 0===r?a:Bm(jm(r),a);var n=_m(t);return Vm(e,i-n.length,i)===n}});var Mm=A;var Um=tg;var zm=vr;var qm=J;var Gm=Za;var Wm=rf;var Km=Z;var Ym=zt;var Jm=ug;var Xm=Tg;Um("match",function(t,e,r){return[function a(e){var r=Km(this);var a=qm(e)?void 0:Ym(e,t);return a?Mm(a,e,r):new RegExp(e)[t](Wm(r))},function(t){var a=zm(this);var i=Wm(t);var n=r(e,a,i);if(n.done)return n.value;if(!a.global)return Xm(a,i);var o=a.unicode;a.lastIndex=0;var s=[];var l=0;var c;while(null!==(c=Xm(a,i))){var f=Wm(c[0]);s[l]=f;if(""===f)a.lastIndex=Jm(i,Gm(a.lastIndex),o);l++}return 0===l?null:s}]});var Qm=Wi;var Zm=co;var ty=S.f;var ey=Za;var ry=rf;var ay=$p;var iy=Z;var ny=jp;var oy=Zm("".slice);var sy=Math.min;var ly=ny("startsWith");var cy=!ly&&!!function(){var t=ty(String.prototype,"startsWith");return t&&!t.writable}();Qm({target:"String",proto:true,forced:!cy&&!ly},{startsWith:function pw(t){var e=ry(iy(this));ay(t);var r=ey(sy(arguments.length>1?arguments[1]:void 0,e.length));var a=ry(t);return oy(e,r,r+a.length)===a}});var fy={getBootstrapVersion:function gw(){var e=5;try{var r=t.fn.dropdown.Constructor.VERSION;if(void 0!==r)e=parseInt(r,10)}catch(a){}try{var i=bootstrap.Tooltip.VERSION;if(void 0!==i)e=parseInt(i,10)}catch(a){}return e},getIconsPrefix:function bw(t){return{bootstrap3:"glyphicon",bootstrap4:"fa",bootstrap5:"bi","bootstrap-table":"icon",bulma:"fa",foundation:"fa",materialize:"material-icons",semantic:"fa"}[t]||"fa"},getIcons:function mw(t){return{glyphicon:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},fa:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},bi:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},icon:{paginationSwitchDown:"icon-arrow-up-circle",paginationSwitchUp:"icon-arrow-down-circle",refresh:"icon-refresh-cw",toggleOff:"icon-toggle-right",toggleOn:"icon-toggle-right",columns:"icon-list",detailOpen:"icon-plus",detailClose:"icon-minus",fullscreen:"icon-maximize",search:"icon-search",clearSearch:"icon-trash-2"},"material-icons":{paginationSwitchDown:"grid_on",paginationSwitchUp:"grid_off",refresh:"refresh",toggleOff:"tablet",toggleOn:"tablet_android",columns:"view_list",detailOpen:"add",detailClose:"remove",fullscreen:"fullscreen",sort:"sort",search:"search",clearSearch:"delete"}}[t]||{}},getSearchInput:function yw(e){if("string"===typeof e.options.searchSelector)return t(e.options.searchSelector);return e.$toolbar.find(".search input")},extend:function ww(){var t=this;for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];var n=r[0]||{};var o=1;var s=false;var l;if("boolean"===typeof n){s=n;n=r[o]||{};o++}if("object"!==i(n)&&"function"!==typeof n)n={};for(;o<r.length;o++){var c=r[o];if("undefined"===typeof c||null===c)continue;for(var f in c){var u=c[f];if("__proto__"===f||n===u)continue;var h=Array.isArray(u);if(s&&u&&(this.isObject(u)||h)){var v=n[f];if(h&&Array.isArray(v))if(v.every(function(e){return!t.isObject(e)&&!Array.isArray(e)})){n[f]=u;continue}if(h&&!Array.isArray(v))l=[];else if(!h&&!this.isObject(v))l={};else l=v;n[f]=this.extend(s,l,u)}else if(void 0!==u)n[f]=u}}return n},sprintf:function Sw(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),a=1;a<e;a++)r[a-1]=arguments[a];var i=true;var n=0;var o=t.replace(/%s/g,function(){var t=r[n++];if("undefined"===typeof t){i=false;return""}return t});return i?o:""},isObject:function xw(t){if("object"!==i(t)||null===t)return false;var e=t;while(null!==Object.getPrototypeOf(e))e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e},isEmptyObject:function Ow(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function kw(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function Pw(t,e){var r=b(t),a;try{for(r.s();!(a=r.n()).done;){var i=a.value;if(i.field===e)return i.title}}catch(n){r.e(n)}finally{r.f()}return""},setFieldIndex:function Cw(t){var e=0;var r=[];var a=b(t[0]),i;try{for(a.s();!(i=a.n()).done;){var n=i.value;e+=n.colspan||1}}catch(o){a.e(o)}finally{a.f()}for(var s=0;s<t.length;s++){r[s]=[];for(var l=0;l<e;l++)r[s][l]=false}for(var c=0;c<t.length;c++){var f=b(t[c]),u;try{for(f.s();!(u=f.n()).done;){var h=u.value;var v=h.rowspan||1;var d=h.colspan||1;var p=r[c].indexOf(false);h.colspanIndex=p;if(1===d){h.fieldIndex=p;if("undefined"===typeof h.field)h.field=p}else h.colspanGroup=h.colspan;for(var g=0;g<v;g++)for(var m=0;m<d;m++)r[c+g][p+m]=true}}catch(o){f.e(o)}finally{f.f()}}},normalizeAccent:function Tw(t){if("string"!==typeof t)return t;return t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function Iw(t,e){var r;var a=(r=[]).concat.apply(r,c(t));var i=b(t),n;try{for(i.s();!(n=i.n()).done;){var o=n.value;var s=b(o),l;try{for(s.s();!(l=s.n()).done;){var f=l.value;if(f.colspanGroup>1){var u=0;var h=function y(t){var e=a.filter(function(e){return e.fieldIndex===t});var r=e[e.length-1];if(e.length>1)for(var i=0;i<e.length-1;i++)e[i].visible=r.visible;if(r.visible)u++};for(var v=f.colspanIndex;v<f.colspanIndex+f.colspanGroup;v++)h(v);f.colspan=u;f.visible=u>0}}}catch(d){s.e(d)}finally{s.f()}}}catch(d){i.e(d)}finally{i.f()}if(t.length<2)return;var p=b(e),g;try{var m=function w(){var t=g.value;var e=a.filter(function(e){return e.fieldIndex===t.fieldIndex});if(e.length>1){var r=b(e),i;try{for(r.s();!(i=r.n()).done;){var n=i.value;n.visible=t.visible}}catch(o){r.e(o)}finally{r.f()}}};for(p.s();!(g=p.n()).done;)m()}catch(d){p.e(d)}finally{p.f()}},getScrollBarWidth:function Aw(){if(void 0===this.cachedWidth){var e=t("<div/>").addClass("fixed-table-scroll-inner");var r=t("<div/>").addClass("fixed-table-scroll-outer");r.append(e);t("body").append(r);var a=e[0].offsetWidth;r.css("overflow","scroll");var i=e[0].offsetWidth;if(a===i)i=r[0].clientWidth;r.remove();this.cachedWidth=a-i}return this.cachedWidth},calculateObjectValue:function $w(t,e,r,a){var n=e;if("string"===typeof e){var o=e.split(".");if(o.length>1){n=window;var s=b(o),l;try{for(s.s();!(l=s.n()).done;){var f=l.value;n=n[f]}}catch(u){s.e(u)}finally{s.f()}}else n=window[e]}if(null!==n&&"object"===i(n))return n;if("function"===typeof n)return n.apply(t,r||[]);if(!n&&"string"===typeof e&&r&&this.sprintf.apply(this,[e].concat(c(r))))return this.sprintf.apply(this,[e].concat(c(r)));return a},compareObjects:function Rw(t,e,r){var a=Object.keys(t);var i=Object.keys(e);if(r&&a.length!==i.length)return false;for(var n=0,o=a;n<o.length;n++){var s=o[n];if(i.includes(s)&&t[s]!==e[s])return false}return true},regexCompare:function Ew(t,e){try{var r=e.match(/^\/(.*?)\/([gim]*)$/);if(t.toString().search(r?new RegExp(r[1],r[2]):new RegExp(e,"gim"))!==-1)return true}catch(a){return false}return false},escapeApostrophe:function jw(t){return t.toString().replace(/'/g,"&#39;")},escapeHTML:function _w(t){if(!t)return t;return t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")},unescapeHTML:function Nw(t){if("string"!==typeof t||!t)return t;return t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'")},removeHTML:function Fw(t){if(!t)return t;return t.toString().replace(/(<([^>]+)>)/gi,"").replace(/&[#A-Za-z0-9]+;/gi,"").trim()},getRealDataAttr:function Dw(t){for(var e=0,r=Object.entries(t);e<r.length;e++){var a=l(r[e],2),i=a[0],n=a[1];var o=i.split(/(?=[A-Z])/).join("-").toLowerCase();if(o!==i){t[o]=n;delete t[i]}}return t},getItemField:function Vw(t,e,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;var i=t;if("undefined"!==typeof a)r=a;if("string"!==typeof e||t.hasOwnProperty(e))return r?this.escapeHTML(t[e]):t[e];var n=e.split(".");var o=b(n),s;try{for(o.s();!(s=o.n()).done;){var l=s.value;i=i&&i[l]}}catch(c){o.e(c)}finally{o.f()}return r?this.escapeHTML(i):i},isIEBrowser:function Bw(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function Lw(t,e){var r=b(t),a;try{for(r.s();!(a=r.n()).done;){var i=a.value;if(JSON.stringify(i)===JSON.stringify(e))return t.indexOf(i)}}catch(n){r.e(n)}finally{r.f()}return-1},trToData:function Hw(e,r){var a=this;var i=[];var n=[];r.each(function(r,o){var s=t(o);var l={};l._id=s.attr("id");l._class=s.attr("class");l._data=a.getRealDataAttr(s.data());l._style=s.attr("style");s.find(">td,>th").each(function(i,o){var s=t(o);var c=+s.attr("colspan")||1;var f=+s.attr("rowspan")||1;var u=i;for(;n[r]&&n[r][u];u++);for(var h=u;h<u+c;h++)for(var v=r;v<r+f;v++){if(!n[v])n[v]=[];n[v][h]=true}var d=e[u].field;l[d]=a.escapeApostrophe(s.html().trim());l["_".concat(d,"_id")]=s.attr("id");l["_".concat(d,"_class")]=s.attr("class");l["_".concat(d,"_rowspan")]=s.attr("rowspan");l["_".concat(d,"_colspan")]=s.attr("colspan");l["_".concat(d,"_title")]=s.attr("title");l["_".concat(d,"_data")]=a.getRealDataAttr(s.data());l["_".concat(d,"_style")]=s.attr("style")});i.push(l)});return i},sort:function Mw(t,e,r,a,i,n){if(void 0===t||null===t)t="";if(void 0===e||null===e)e="";if(a.sortStable&&t===e){t=i;e=n}if(this.isNumeric(t)&&this.isNumeric(e)){t=parseFloat(t);e=parseFloat(e);if(t<e)return r*-1;if(t>e)return r;return 0}if(a.sortEmptyLast){if(""===t)return 1;if(""===e)return-1}if(t===e)return 0;if("string"!==typeof t)t=t.toString();if(t.localeCompare(e)===-1)return r*-1;return r},getEventName:function Uw(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";e=e||"".concat(+new Date).concat(~~(1e6*Math.random()));return"".concat(t,"-").concat(e)},hasDetailViewIcon:function zw(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function qw(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function Gw(t){var e=b(t),r;try{for(e.s();!(r=e.n()).done;){var a=r.value;for(var i=0,n=Object.keys(a);i<n.length;i++){var o=n[i];if(o.startsWith("_")&&(o.endsWith("_rowspan")||o.endsWith("_colspan")))return true}}}catch(s){e.e(s)}finally{e.f()}return false},deepCopy:function Ww(t){if(void 0===t)return t;return this.extend(true,Array.isArray(t)?[]:{},t)},debounce:function Kw(t,e,r){var a;return function i(){var i=this;var n=arguments;var o=function l(){a=null;if(!r)t.apply(i,n)};var s=r&&!a;clearTimeout(a);a=setTimeout(o,e);if(s)t.apply(i,n)}}};var uy="1.22.6";var hy=fy.getBootstrapVersion();var vy={3:{classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-select",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-end">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[hy];var dy={id:void 0,firstLoad:true,height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",striped:false,headerStyle:function Yw(t){return{}},rowStyle:function Jw(t,e){return{}},rowAttributes:function Xw(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:false,virtualScrollItemHeight:void 0,sortable:true,sortClass:void 0,silentSort:true,sortEmptyLast:false,sortName:void 0,sortOrder:void 0,sortReset:false,sortStable:false,sortResetPage:false,rememberOrder:false,serverSort:true,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:true,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function Qw(t){return t},queryParamsType:"limit",responseHandler:function Zw(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:false,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:false,paginationLoop:true,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:false,paginationLoadMore:false,search:false,searchable:false,searchHighlight:false,searchOnEnterKey:false,strictSearch:false,regexSearch:false,searchSelector:false,visibleSearch:false,showButtonIcons:true,showButtonText:false,showSearchButton:false,showSearchClearButton:false,trimOnSearch:true,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:true,showFooter:false,footerStyle:function tS(t){return{}},searchAccentNeutralise:false,showColumns:false,showSearch:false,showPageGo:false,showColumnsToggleAll:false,showColumnsSearch:false,minimumCountColumns:1,showPaginationSwitch:false,showRefresh:false,showToggle:false,showFullscreen:false,smartDisplay:true,escape:false,escapeTitle:true,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:false,ignoreClickToSelectOn:function eS(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:false,checkboxHeader:true,maintainMetaData:false,multipleSelectRow:false,uniqueId:void 0,cardView:false,detailView:false,detailViewIcon:true,detailViewByClick:false,detailViewAlign:"left",detailFormatter:function rS(t,e){return""},detailFilter:function aS(t,e){return true},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["search","paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:vy.classes.buttonsPrefix,buttonsClass:vy.classes.buttons,iconsPrefix:void 0,icons:{},iconSize:void 0,fixedScroll:false,loadingFontSize:"auto",loadingTemplate:function iS(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function nS(t,e){return false},onClickCell:function oS(t,e,r,a){return false},onDblClickCell:function sS(t,e,r,a){return false},onClickRow:function lS(t,e){return false},onDblClickRow:function cS(t,e){return false},onSort:function fS(t,e){return false},onCheck:function uS(t){return false},onUncheck:function hS(t){return false},onCheckAll:function vS(t){return false},onUncheckAll:function dS(t){return false},onCheckSome:function pS(t){return false},onUncheckSome:function gS(t){return false},onLoadSuccess:function bS(t){return false},onLoadError:function mS(t){return false},onColumnSwitch:function yS(t,e){return false},onColumnSwitchAll:function wS(t){return false},onPageChange:function SS(t,e){return false},onSearch:function xS(t){return false},onShowSearch:function OS(){return false},onToggle:function kS(t){return false},onPreBody:function PS(t){return false},onPostBody:function CS(){return false},onPostHeader:function TS(){return false},onPostFooter:function IS(){return false},onExpandRow:function AS(t,e,r){return false},onCollapseRow:function $S(t,e){return false},onRefreshOptions:function RS(t){return false},onRefresh:function ES(t){return false},onResetView:function jS(){return false},onScrollBody:function _S(){return false},onTogglePagination:function NS(t){return false},onVirtualScroll:function FS(t,e){return false}};var py={formatLoadingMessage:function DS(){return"Loading, please wait"},formatRecordsPerPage:function VS(t){return"".concat(t," rows per page")},formatShowingRows:function BS(t,e,r,a){if(void 0!==a&&a>0&&a>r)return"Showing ".concat(t," to ").concat(e," of ").concat(r," rows (filtered from ").concat(a," total rows)");return"Showing ".concat(t," to ").concat(e," of ").concat(r," rows")},formatSRPaginationPreText:function LS(){return"previous page"},formatSRPaginationPageText:function HS(t){return"to page ".concat(t)},formatSRPaginationNextText:function MS(){return"next page"},formatDetailPagination:function US(t){return"Showing ".concat(t," rows")},formatSearch:function zS(){return"Search"},formatShowSearch:function qS(){return"Show Search"},formatPageGo:function(){return"Go"},formatClearSearch:function GS(){return"Clear Search"},formatNoMatches:function WS(){return"No matching records found"},formatPaginationSwitch:function KS(){return"Hide/Show pagination"},formatPaginationSwitchDown:function YS(){return"Show pagination"},formatPaginationSwitchUp:function JS(){return"Hide pagination"},formatRefresh:function XS(){return"Refresh"},formatToggleOn:function QS(){return"Show card view"},formatToggleOff:function ZS(){return"Hide card view"},formatColumns:function tx(){return"Columns"},formatColumnsToggleAll:function ex(){return"Toggle all"},formatFullscreen:function rx(){return"Fullscreen"},formatAllRows:function ax(){return"All"}};var gy={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:false,checkbox:false,checkboxEnabled:true,clickToSelect:true,showSelectTitle:false,sortable:false,sortName:void 0,order:"asc",sorter:void 0,visible:true,ignore:false,switchable:true,switchableLabel:void 0,cardVisible:true,searchable:true,formatter:void 0,footerFormatter:void 0,footerStyle:void 0,detailFormatter:void 0,searchFormatter:true,searchHighlightFormatter:false,escape:void 0,events:void 0};var by=["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","sortBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"];var my={"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","column-switch-all.bs.table":"onColumnSwitchAll","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"};Object.assign(dy,py);var yy={VERSION:uy,THEME:"bootstrap".concat(hy),CONSTANTS:vy,DEFAULTS:dy,COLUMN_DEFAULTS:gy,METHODS:by,EVENTS:my,LOCALES:{en:py,"en-US":py}};var wy=50;var Sy=4;var xy=function(){function t(e){var r=this;n(this,t);this.rows=e.rows;this.scrollEl=e.scrollEl;this.contentEl=e.contentEl;this.callback=e.callback;this.itemHeight=e.itemHeight;this.cache={};this.scrollTop=this.scrollEl.scrollTop;this.initDOM(this.rows,e.fixedScroll);this.scrollEl.scrollTop=this.scrollTop;this.lastCluster=0;var a=function i(){if(r.lastCluster!==(r.lastCluster=r.getNum())){r.initDOM(r.rows);r.callback(r.startIndex,r.endIndex)}};this.scrollEl.addEventListener("scroll",a,false);this.destroy=function(){r.contentEl.innerHtml="";r.scrollEl.removeEventListener("scroll",a,false)}}return s(t,[{key:"initDOM",value:function e(t,r){if("undefined"===typeof this.clusterHeight){this.cache.scrollTop=this.scrollEl.scrollTop;this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0];this.getRowsHeight(t)}else if(0===this.blockHeight)this.getRowsHeight(t);var a=this.initData(t,this.getNum(r));var i=a.rows.join("");var n=this.checkChanges("data",i);var o=this.checkChanges("top",a.topOffset);var s=this.checkChanges("bottom",a.bottomOffset);var l=[];if(n&&o){if(a.topOffset)l.push(this.getExtra("top",a.topOffset));l.push(i);if(a.bottomOffset)l.push(this.getExtra("bottom",a.bottomOffset));this.startIndex=a.start;this.endIndex=a.end;this.contentEl.innerHTML=l.join("");if(r)this.contentEl.scrollTop=this.cache.scrollTop}else if(s)this.contentEl.lastChild.style.height="".concat(a.bottomOffset,"px")}},{key:"getRowsHeight",value:function r(){if("undefined"===typeof this.itemHeight||0===this.itemHeight){var t=this.contentEl.children;var e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=this.itemHeight*wy;this.clusterRows=wy*Sy;this.clusterHeight=this.blockHeight*Sy}},{key:"getNum",value:function a(t){this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop;return Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function i(t,e){if(t.length<wy)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var r=Math.max((this.clusterRows-wy)*e,0);var a=r+this.clusterRows;var i=Math.max(r*this.itemHeight,0);var n=Math.max((t.length-a)*this.itemHeight,0);var o=[];var s=r;if(i<1)s++;for(var l=r;l<a;l++)t[l]&&o.push(t[l]);return{start:r,end:a,topOffset:i,bottomOffset:n,rowsAbove:s,rows:o}}},{key:"checkChanges",value:function o(t,e){var r=e!==this.cache[t];this.cache[t]=e;return r}},{key:"getExtra",value:function l(t,e){var r=document.createElement("tr");r.className="virtual-scroll-".concat(t);if(e)r.style.height="".concat(e,"px");return r.outerHTML}}])}();var Oy=function(){function e(r,a){n(this,e);this.options=a;this.$el=t(r);this.$el_=this.$el.clone();this.timeoutId_=0;this.timeoutFooter_=0}return s(e,[{key:"init",value:function r(){this.initConstants();this.initLocale();this.initContainer();this.initTable();this.initHeader();this.initData();this.initHiddenRows();this.initToolbar();this.initPagination();this.initBody();this.initSearchText();this.initServer()}},{key:"initConstants",value:function a(){var e=this.options;this.constants=yy.CONSTANTS;this.constants.theme=t.fn.bootstrapTable.theme;this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var r=fy.getIconsPrefix(t.fn.bootstrapTable.theme);if("string"===typeof e.icons)e.icons=fy.calculateObjectValue(null,e.icons);e.iconsPrefix=e.iconsPrefix||t.fn.bootstrapTable.defaults.iconsPrefix||r;e.icons=Object.assign(fy.getIcons(e.iconsPrefix),t.fn.bootstrapTable.defaults.icons,e.icons);var a=e.buttonsPrefix?"".concat(e.buttonsPrefix,"-"):"";this.constants.buttonsClass=[e.buttonsPrefix,a+e.buttonsClass,fy.sprintf("".concat(a,"%s"),e.iconSize)].join(" ").trim();this.buttons=fy.calculateObjectValue(this,e.buttons,[],{});if("object"!==i(this.buttons))this.buttons={}}},{key:"initLocale",value:function o(){if(this.options.locale){var r=t.fn.bootstrapTable.locales;var a=this.options.locale.split(/-|_/);a[0]=a[0].toLowerCase();if(a[1])a[1]=a[1].toUpperCase();var i={};if(r[this.options.locale])i=r[this.options.locale];else if(r[a.join("-")])i=r[a.join("-")];else if(r[a[0]])i=r[a[0]];this._defaultLocales=this._defaultLocales||{};for(var n=0,o=Object.entries(i);n<o.length;n++){var s=l(o[n],2),c=s[0],f=s[1];var u=this._defaultLocales.hasOwnProperty(c)?this._defaultLocales[c]:e.DEFAULTS[c];if(this.options[c]!==u)continue;this.options[c]=f;this._defaultLocales[c]=f}}}},{key:"initContainer",value:function f(){var e=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"";var r=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"";var a=fy.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=t('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(e,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(a,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(r,"\n      </div>\n    "));this.$container.insertAfter(this.$el);this.$tableContainer=this.$container.find(".fixed-table-container");this.$tableHeader=this.$container.find(".fixed-table-header");this.$tableBody=this.$container.find(".fixed-table-body");this.$tableLoading=this.$container.find(".fixed-table-loading");this.$tableFooter=this.$el.find("tfoot");if(this.options.buttonsToolbar)this.$toolbar=t("body").find(this.options.buttonsToolbar);else this.$toolbar=this.$container.find(".fixed-table-toolbar");this.$pagination=this.$container.find(".fixed-table-pagination");this.$tableBody.append(this.$el);this.$container.after('<div class="clearfix"></div>');this.$el.addClass(this.options.classes);this.$tableLoading.addClass(this.options.classes);if(this.options.striped)this.$el.addClass("table-striped");if(this.options.height){this.$tableContainer.addClass("fixed-height");if(this.options.showFooter)this.$tableContainer.addClass("has-footer");if(this.options.classes.split(" ").includes("table-bordered")){this.$tableBody.append('<div class="fixed-table-border"></div>');this.$tableBorder=this.$tableBody.find(".fixed-table-border");this.$tableLoading.addClass("fixed-table-border")}this.$tableFooter=this.$container.find(".fixed-table-footer")}}},{key:"initTable",value:function u(){var r=this;var a=[];this.$header=this.$el.find(">thead");if(!this.$header.length)this.$header=t('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el);else if(this.options.theadClasses)this.$header.addClass(this.options.theadClasses);this._headerTrClasses=[];this._headerTrStyles=[];this.$header.find("tr").each(function(e,i){var n=t(i);var o=[];n.find("th").each(function(e,r){var a=t(r);if("undefined"!==typeof a.data("field"))a.data("field","".concat(a.data("field")));var i=Object.assign({},a.data());for(var n in i)if(t.fn.bootstrapTable.columnDefaults.hasOwnProperty(n))delete i[n];o.push(fy.extend({},{_data:fy.getRealDataAttr(i),title:a.html(),class:a.attr("class"),titleTooltip:a.attr("title"),rowspan:a.attr("rowspan")?+a.attr("rowspan"):void 0,colspan:a.attr("colspan")?+a.attr("colspan"):void 0},a.data()))});a.push(o);if(n.attr("class"))r._headerTrClasses.push(n.attr("class"));if(n.attr("style"))r._headerTrStyles.push(n.attr("style"))});if(!Array.isArray(this.options.columns[0]))this.options.columns=[this.options.columns];this.options.columns=fy.extend(true,[],a,this.options.columns);this.columns=[];this.fieldsColumnsIndex=[];fy.setFieldIndex(this.options.columns);this.options.columns.forEach(function(t,a){t.forEach(function(t,i){var n=fy.extend({},e.COLUMN_DEFAULTS,t,{passed:t});if("undefined"!==typeof n.fieldIndex){r.columns[n.fieldIndex]=n;r.fieldsColumnsIndex[n.field]=n.fieldIndex}r.options.columns[a][i]=n})});if(!this.options.data.length){var i=fy.trToData(this.columns,this.$el.find(">tbody>tr"));if(i.length){this.options.data=i;this.fromHtml=true}}if(!(this.options.pagination&&"server"!==this.options.sidePagination))this.footerData=fy.trToData(this.columns,this.$el.find(">tfoot>tr"));if(this.footerData)this.$el.find("tfoot").html("<tr></tr>");if(!this.options.showFooter||this.options.cardView)this.$tableFooter.hide();else this.$tableFooter.show()}},{key:"initHeader",value:function h(){var e=this;var r={};var a=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]};fy.updateFieldGroup(this.options.columns,this.columns);this.options.columns.forEach(function(t,n){var o=[];o.push("<tr".concat(fy.sprintf(' class="%s"',e._headerTrClasses[n])," ").concat(fy.sprintf(' style="%s"',e._headerTrStyles[n]),">"));var s="";if(0===n&&fy.hasDetailViewIcon(e.options)){var c=e.options.columns.length>1?' rowspan="'.concat(e.options.columns.length,'"'):"";s='<th class="detail"'.concat(c,'>\n          <div class="fht-cell"></div>\n          </th>')}if(s&&"right"!==e.options.detailViewAlign)o.push(s);t.forEach(function(t,a){var s=fy.sprintf(' class="%s"',t["class"]);var c=t.widthUnit;var f=parseFloat(t.width);var u=t.halign?t.halign:t.align;var h=fy.sprintf("text-align: %s; ",u);var v=fy.sprintf("text-align: %s; ",t.align);var d=fy.sprintf("vertical-align: %s; ",t.valign);d+=fy.sprintf("width: %s; ",(t.checkbox||t.radio)&&!f?!t.showSelectTitle?"36px":void 0:f?f+c:void 0);if("undefined"===typeof t.fieldIndex&&!t.visible)return;var p=fy.calculateObjectValue(null,e.options.headerStyle,[t]);var g=[];var b=[];var m="";if(p&&p.css)for(var y=0,w=Object.entries(p.css);y<w.length;y++){var S=l(w[y],2),x=S[0],O=S[1];g.push("".concat(x,": ").concat(O))}if(p&&p.classes)m=fy.sprintf(' class="%s"',t["class"]?[t["class"],p.classes].join(" "):p.classes);if("undefined"!==typeof t.fieldIndex){e.header.fields[t.fieldIndex]=t.field;e.header.styles[t.fieldIndex]=v+d;e.header.classes[t.fieldIndex]=s;e.header.formatters[t.fieldIndex]=t.formatter;e.header.detailFormatters[t.fieldIndex]=t.detailFormatter;e.header.events[t.fieldIndex]=t.events;e.header.sorters[t.fieldIndex]=t.sorter;e.header.sortNames[t.fieldIndex]=t.sortName;e.header.cellStyles[t.fieldIndex]=t.cellStyle;e.header.searchables[t.fieldIndex]=t.searchable;if(!t.visible)return;if(e.options.cardView&&!t.cardVisible)return;r[t.field]=t}if(Object.keys(t._data||{}).length>0)for(var k=0,P=Object.entries(t._data);k<P.length;k++){var C=l(P[k],2),T=C[0],I=C[1];b.push("data-".concat(T,"='").concat("object"===i(I)?JSON.stringify(I):I,"'"))}o.push("<th".concat(fy.sprintf(' title="%s"',t.titleTooltip)),t.checkbox||t.radio?fy.sprintf(' class="bs-checkbox %s"',t["class"]||""):m||s,fy.sprintf(' style="%s"',h+d+g.join("; ")||void 0),fy.sprintf(' rowspan="%s"',t.rowspan),fy.sprintf(' colspan="%s"',t.colspan),fy.sprintf(' data-field="%s"',t.field),0===a&&n>0?" data-not-first-th":"",b.length>0?b.join(" "):"",">");o.push(fy.sprintf('<div class="th-inner %s">',e.options.sortable&&t.sortable?"sortable".concat("center"===u?" sortable-center":""," both"):""));var A=e.options.escape&&e.options.escapeTitle?fy.escapeHTML(t.title):t.title;var $=A;if(t.checkbox){A="";if(!e.options.singleSelect&&e.options.checkboxHeader)A='<label><input name="btSelectAll" type="checkbox" /><span></span></label>';e.header.stateField=t.field}if(t.radio){A="";e.header.stateField=t.field}if(!A&&t.showSelectTitle)A+=$;o.push(A);o.push("</div>");o.push('<div class="fht-cell"></div>');o.push("</div>");o.push("</th>")});if(s&&"right"===e.options.detailViewAlign)o.push(s);o.push("</tr>");if(o.length>3)a.push(o.join(""))});this.$header.html(a.join(""));this.$header.find("th[data-field]").each(function(e,a){t(a).data(r[t(a).data("field")])});this.$container.off("click",".th-inner").on("click",".th-inner",function(r){var a=t(r.currentTarget);if(e.options.detailView&&!a.parent().hasClass("bs-checkbox"))if(a.closest(".bootstrap-table")[0]!==e.$container[0])return false;if(e.options.sortable&&a.parent().data().sortable)e.onSort(r)});var n=fy.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(n);if(!this.options.showHeader||this.options.cardView){this.$header.hide();this.$tableHeader.hide();this.$tableLoading.css("top",0)}else{this.$header.show();this.$tableHeader.show();this.$tableLoading.css("top",this.$header.outerHeight()+1);this.getCaret();t(window).on(n,function(){return e.resetView()})}this.$selectAll=this.$header.find('[name="btSelectAll"]');this.$selectAll.off("click").on("click",function(r){r.stopPropagation();var a=t(r.currentTarget).prop("checked");e[a?"checkAll":"uncheckAll"]();e.updateSelected()})}},{key:"initData",value:function v(t,e){if("append"===e)this.options.data=this.options.data.concat(t);else if("prepend"===e)this.options.data=[].concat(t).concat(this.options.data);else{t=t||fy.deepCopy(this.options.data);this.options.data=Array.isArray(t)?t:t[this.options.dataField]}this.data=c(this.options.data);if(this.options.sortReset)this.unsortedData=c(this.data);if("server"===this.options.sidePagination)return;this.initSort()}},{key:"initSort",value:function d(){var t=this;var e=this.options.sortName;var r="desc"===this.options.sortOrder?-1:1;var a=this.header.fields.indexOf(this.options.sortName);var i=0;if(a!==-1){if(this.options.sortStable)this.data.forEach(function(t,e){if(!t.hasOwnProperty("_position"))t._position=e});if(this.options.customSort)fy.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]);else this.data.sort(function(i,n){if(t.header.sortNames[a])e=t.header.sortNames[a];var o=fy.getItemField(i,e,t.options.escape);var s=fy.getItemField(n,e,t.options.escape);var l=fy.calculateObjectValue(t.header,t.header.sorters[a],[o,s,i,n]);if(void 0!==l){if(t.options.sortStable&&0===l)return r*(i._position-n._position);return r*l}return fy.sort(o,s,r,t.options,i._position,n._position)});if(void 0!==this.options.sortClass){clearTimeout(i);i=setTimeout(function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)},250)}}else if(this.options.sortReset)this.data=c(this.unsortedData)}},{key:"sortBy",value:function p(t){this.options.sortName=t.field;this.options.sortOrder=t.hasOwnProperty("sortOrder")?t.sortOrder:"asc";this._sort()}},{key:"onSort",value:function g(e){var r=e.type,a=e.currentTarget;var i="keypress"===r?t(a):t(a).parent();var n=this.$header.find("th").eq(i.index());this.$header.add(this.$header_).find("span.order").remove();if(this.options.sortName===i.data("field")){var o=this.options.sortOrder;var s=this.columns[this.fieldsColumnsIndex[i.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[i.data("field")]].order;if(void 0===o)this.options.sortOrder="asc";else if("asc"===o)this.options.sortOrder=this.options.sortReset?"asc"===s?"desc":void 0:"desc";else if("desc"===this.options.sortOrder)this.options.sortOrder=this.options.sortReset?"desc"===s?"asc":void 0:"asc";if(void 0===this.options.sortOrder)this.options.sortName=void 0}else{this.options.sortName=i.data("field");if(this.options.rememberOrder)this.options.sortOrder="asc"===i.data("order")?"desc":"asc";else this.options.sortOrder=this.columns[this.fieldsColumnsIndex[i.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[i.data("field")]].order}i.add(n).data("order",this.options.sortOrder);this.getCaret();this._sort()}},{key:"_sort",value:function m(){if("server"===this.options.sidePagination&&this.options.serverSort){this.options.pageNumber=1;this.trigger("sort",this.options.sortName,this.options.sortOrder);this.initServer(this.options.silentSort);return}if(this.options.pagination&&this.options.sortResetPage){this.options.pageNumber=1;this.initPagination()}this.trigger("sort",this.options.sortName,this.options.sortOrder);this.initSort();this.initBody()}},{key:"initToolbar",value:function y(){var e=this;var r=this.options;var a=[];var n=0;var o;var s=0;if(this.$toolbar.find(".bs-bars").children().length)t("body").append(t(r.toolbar));this.$toolbar.html("");if("string"===typeof r.toolbar||"object"===i(r.toolbar))t(fy.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,r.toolbarAlign)).appendTo(this.$toolbar).append(t(r.toolbar));a=['<div class="'.concat(["columns","columns-".concat(r.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(r.buttonsAlign)].join(" "),'">')];if("string"===typeof r.buttonsOrder)r.buttonsOrder=r.buttonsOrder.replace(/\[|\]| |'/g,"").split(",");this.buttons=Object.assign(this.buttons,{search:{text:r.formatSearch(),icon:r.icons.search,render:false,event:this.toggleShowSearch,attributes:{"aria-label":r.formatShowSearch(),title:r.formatShowSearch()}},paginationSwitch:{text:r.pagination?r.formatPaginationSwitchUp():r.formatPaginationSwitchDown(),icon:r.pagination?r.icons.paginationSwitchDown:r.icons.paginationSwitchUp,render:false,event:this.togglePagination,attributes:{"aria-label":r.formatPaginationSwitch(),title:r.formatPaginationSwitch()}},refresh:{text:r.formatRefresh(),icon:r.icons.refresh,render:false,event:this.refresh,attributes:{"aria-label":r.formatRefresh(),title:r.formatRefresh()}},toggle:{text:r.formatToggleOn(),icon:r.icons.toggleOff,render:false,event:this.toggleView,attributes:{"aria-label":r.formatToggleOn(),title:r.formatToggleOn()}},fullscreen:{text:r.formatFullscreen(),icon:r.icons.fullscreen,render:false,event:this.toggleFullscreen,attributes:{"aria-label":r.formatFullscreen(),title:r.formatFullscreen()}},columns:{render:false,html:function z(){var z=[];z.push('<div class="keep-open '.concat(e.constants.classes.buttonsDropdown,'">\n            <button class="').concat(e.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(e.constants.dataToggle,'="dropdown"\n            aria-label="').concat(r.formatColumns(),'" title="').concat(r.formatColumns(),'">\n            ').concat(r.showButtonIcons?fy.sprintf(e.constants.html.icon,r.iconsPrefix,r.icons.columns):"","\n            ").concat(r.showButtonText?r.formatColumns():"","\n            ").concat(e.constants.html.dropdownCaret,"\n            </button>\n            ").concat(e.constants.html.toolbarDropdown[0]));if(r.showColumnsSearch){z.push(fy.sprintf(e.constants.html.toolbarDropdownItem,fy.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',e.constants.classes.input,r.formatSearch())));z.push(e.constants.html.toolbarDropdownSeparator)}if(r.showColumnsToggleAll){var t=e.getVisibleColumns().length===e.columns.filter(function(t){return!e.isSelectionColumn(t)}).length;z.push(fy.sprintf(e.constants.html.toolbarDropdownItem,fy.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',t?'checked="checked"':"",r.formatColumnsToggleAll())));z.push(e.constants.html.toolbarDropdownSeparator)}var a=0;e.columns.forEach(function(t){if(t.visible)a++});e.columns.forEach(function(t,i){if(e.isSelectionColumn(t))return;if(r.cardView&&!t.cardVisible)return;if(t.ignore)return;var n=t.visible?' checked="checked"':"";var o=a<=r.minimumCountColumns&&n?' disabled="disabled"':"";if(t.switchable){z.push(fy.sprintf(e.constants.html.toolbarDropdownItem,fy.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',t.field,i,n,o,t.switchableLabel||t.title)));s++}});z.push(e.constants.html.toolbarDropdown[1],"</div>");return z.join("")}}});var c={};for(var f=0,u=Object.entries(this.buttons);f<u.length;f++){var h=l(u[f],2),v=h[0],d=h[1];var p=void 0;if(d.hasOwnProperty("html")){if("function"===typeof d.html)p=d.html();else if("string"===typeof d.html)p=d.html}else{var g=this.constants.buttonsClass;if(d.hasOwnProperty("attributes")&&d.attributes["class"])g+=" ".concat(d.attributes["class"]);p='<button class="'.concat(g,'" type="button" name="').concat(v,'"');if(d.hasOwnProperty("attributes"))for(var m=0,y=Object.entries(d.attributes);m<y.length;m++){var w=l(y[m],2),S=w[0],x=w[1];if("class"===S)continue;p+=" ".concat(S,'="').concat(x,'"')}p+=">";if(r.showButtonIcons&&d.hasOwnProperty("icon"))p+="".concat(fy.sprintf(this.constants.html.icon,r.iconsPrefix,d.icon)," ");if(r.showButtonText&&d.hasOwnProperty("text"))p+=d.text;p+="</button>"}c[v]=p;var O="show".concat(v.charAt(0).toUpperCase()).concat(v.substring(1));var k=r[O];if((!d.hasOwnProperty("render")||d.hasOwnProperty("render")&&d.render)&&(void 0===k||true===k))r[O]=true;if(!r.buttonsOrder.includes(v))r.buttonsOrder.push(v)}var P=b(r.buttonsOrder),C;try{for(P.s();!(C=P.n()).done;){var T=C.value;var I=r["show".concat(T.charAt(0).toUpperCase()).concat(T.substring(1))];if(I)a.push(c[T])}}catch(A){P.e(A)}finally{P.f()}a.push("</div>");if(this.showToolbar||a.length>2)this.$toolbar.append(a.join(""));if(r.showSearch)this.$toolbar.find('button[name="showSearch"]').off("click").on("click",function(){return e.toggleShowSearch()});var $=function q(){var t=l(E[R],2),r=t[0],a=t[1];if(a.hasOwnProperty("event")){if("function"===typeof a.event||"string"===typeof a.event){var i="string"===typeof a.event?window[a.event]:a.event;e.$toolbar.find('button[name="'.concat(r,'"]')).off("click").on("click",function(){return i.call(e)});return 1}var n=function c(){var t=l(s[o],2),a=t[0],i=t[1];var n="string"===typeof i?window[i]:i;e.$toolbar.find('button[name="'.concat(r,'"]')).off(a).on(a,function(){return n.call(e)})};for(var o=0,s=Object.entries(a.event);o<s.length;o++)n()}};for(var R=0,E=Object.entries(this.buttons);R<E.length;R++)if($())continue;if(r.showColumns){o=this.$toolbar.find(".keep-open");var j=o.find('input[type="checkbox"]:not(".toggle-all")');var _=o.find('input[type="checkbox"].toggle-all');if(s<=r.minimumCountColumns)o.find("input").prop("disabled",true);o.find("li, label").off("click").on("click",function(t){t.stopImmediatePropagation()});j.off("click").on("click",function(r){var a=r.currentTarget;var i=t(a);e._toggleColumn(i.val(),i.prop("checked"),false);e.trigger("column-switch",i.data("field"),i.prop("checked"));_.prop("checked",j.filter(":checked").length===e.columns.filter(function(t){return!e.isSelectionColumn(t)}).length)});_.off("click").on("click",function(r){var a=r.currentTarget;e._toggleAllColumns(t(a).prop("checked"));e.trigger("column-switch-all",t(a).prop("checked"))});if(r.showColumnsSearch){var N=o.find('[name="columnsSearch"]');var F=o.find(".dropdown-item-marker");N.on("keyup paste change",function(e){var r=e.currentTarget;var a=t(r);var i=a.val().toLowerCase();F.show();j.each(function(e,r){var a=t(r);var n=a.parents(".dropdown-item-marker");var o=n.text().toLowerCase();if(!o.includes(i))n.hide()})})}}var D=function G(t){var a=t.is("select")?"change":"keyup drop blur mouseup";t.off(a).on(a,function(t){if(r.searchOnEnterKey&&13!==t.keyCode)return;if([37,38,39,40].includes(t.keyCode))return;clearTimeout(n);n=setTimeout(function(){e.onSearch({currentTarget:t.currentTarget})},r.searchTimeOut)})};if((r.search||this.showSearchClearButton)&&"string"!==typeof r.searchSelector){a=[];var V=fy.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,r.formatSearch(),r.showButtonIcons?fy.sprintf(this.constants.html.icon,r.iconsPrefix,r.icons.search):"",r.showButtonText?r.formatSearch():"");var B=fy.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,r.formatClearSearch(),r.showButtonIcons?fy.sprintf(this.constants.html.icon,r.iconsPrefix,r.icons.clearSearch):"",r.showButtonText?r.formatClearSearch():"");var L='<input class="'.concat(this.constants.classes.input,"\n        ").concat(fy.sprintf(" %s%s",this.constants.classes.inputPrefix,r.iconSize),'\n        search-input" type="search" aria-label="').concat(r.formatSearch(),'" placeholder="').concat(r.formatSearch(),'" autocomplete="off">');var H=L;if(r.showSearchButton||r.showSearchClearButton){var M=(r.showSearchButton?V:"")+(r.showSearchClearButton?B:"");H=r.search?fy.sprintf(this.constants.html.inputGroup,L,M):M}a.push(fy.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(r.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),H));this.$toolbar.append(a.join(""));var U=fy.getSearchInput(this);if(r.showSearchButton){this.$toolbar.find(".search button[name=search]").off("click").on("click",function(){clearTimeout(n);n=setTimeout(function(){e.onSearch({currentTarget:U})},r.searchTimeOut)});if(r.searchOnEnterKey)D(U)}else D(U);if(r.showSearchClearButton)this.$toolbar.find(".search button[name=clearSearch]").click(function(){e.resetSearch()})}else if("string"===typeof r.searchSelector)D(fy.getSearchInput(this))}},{key:"onSearch",value:function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.currentTarget,a=e.firedByInitSearchText;var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:true;if(void 0!==r&&t(r).length&&i){var n=t(r).val().trim();if(this.options.trimOnSearch&&t(r).val()!==n)t(r).val(n);if(this.searchText===n)return;var o=fy.getSearchInput(this);var s=r instanceof jQuery?r:t(r);if(s.is(o)||s.hasClass("search-input")){this.searchText=n;this.options.searchText=n}}if(!a)this.options.pageNumber=1;this.initSearch();if(a){if("client"===this.options.sidePagination)this.updatePagination()}else this.updatePagination();this.trigger("search",this.searchText)}},{key:"initSearch",value:function S(){var t=this;this.filterOptions=this.filterOptions||this.options.filterOptions;if("server"!==this.options.sidePagination){if(this.options.customSearch){this.data=fy.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]);if(this.options.sortReset)this.unsortedData=c(this.data);this.initSort();return}var e=this.searchText&&(this.fromHtml?fy.escapeHTML(this.searchText):this.searchText);var r=e?e.toLowerCase():"";var a=fy.isEmptyObject(this.filterColumns)?null:this.filterColumns;if(this.options.searchAccentNeutralise)r=fy.normalizeAccent(r);if("function"===typeof this.filterOptions.filterAlgorithm)this.data=this.options.data.filter(function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,a])});else if("string"===typeof this.filterOptions.filterAlgorithm)this.data=a?this.options.data.filter(function(e){var r=t.filterOptions.filterAlgorithm;if("and"===r){for(var i in a)if(Array.isArray(a[i])&&!a[i].includes(e[i])||!Array.isArray(a[i])&&e[i]!==a[i])return false}else if("or"===r){var n=false;for(var o in a)if(Array.isArray(a[o])&&a[o].includes(e[o])||!Array.isArray(a[o])&&e[o]===a[o])n=true;return n}return true}):c(this.options.data);var i=this.getVisibleFields();this.data=r?this.data.filter(function(a,n){for(var o=0;o<t.header.fields.length;o++){if(!t.header.searchables[o]||t.options.visibleSearch&&i.indexOf(t.header.fields[o])===-1)continue;var s=fy.isNumeric(t.header.fields[o])?parseInt(t.header.fields[o],10):t.header.fields[o];var l=t.columns[t.fieldsColumnsIndex[s]];var c=void 0;if("string"===typeof s&&!a.hasOwnProperty(s)){c=a;var f=s.split(".");for(var u=0;u<f.length;u++)if(null!==c[f[u]])c=c[f[u]];else{c=null;break}}else c=a[s];if(t.options.searchAccentNeutralise)c=fy.normalizeAccent(c);if(l&&l.searchFormatter)c=fy.calculateObjectValue(l,t.header.formatters[o],[c,a,n,l.field],c);if("string"===typeof c||"number"===typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===r||t.options.regexSearch&&fy.regexCompare(c,e))return true;var h=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm;var v=h.exec(t.searchText);var d=false;if(v){var p=v[1]||"".concat(v[5],"l");var g=v[2]||v[3];var b=parseInt(c,10);var m=parseInt(g,10);switch(p){case">":case"<l":d=b>m;break;case"<":case">l":d=b<m;break;case"<=":case"=<":case">=l":case"=>l":d=b<=m;break;case">=":case"=>":case"<=l":case"=<l":d=b>=m}}if(d||"".concat(c).toLowerCase().includes(r))return true}}return false}):this.data;if(this.options.sortReset)this.unsortedData=c(this.data);this.initSort()}}},{key:"initPagination",value:function x(){var e=this;var r=this.options;if(!r.pagination){this.$pagination.hide();return}this.$pagination.show();var a=[];var i=false;var n;var o;var s;var l;var c;var f;var u;var h=this.getData({includeHiddenRows:false});var v=r.pageList;if("string"===typeof v)v=v.replace(/\[|\]| /g,"").toLowerCase().split(",");v=v.map(function(t){if("string"===typeof t)return t.toLowerCase()===r.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?r.formatAllRows():+t;return t});this.paginationParts=r.paginationParts;if("string"===typeof this.paginationParts)this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",");if("server"!==r.sidePagination)r.totalRows=h.length;this.totalPages=0;if(r.totalRows){if(r.pageSize===r.formatAllRows()){r.pageSize=r.totalRows;i=true}this.totalPages=~~((r.totalRows-1)/r.pageSize)+1;r.totalPages=this.totalPages}if(this.totalPages>0&&r.pageNumber>this.totalPages)r.pageNumber=this.totalPages;this.pageFrom=(r.pageNumber-1)*r.pageSize+1;this.pageTo=r.pageNumber*r.pageSize;if(this.pageTo>r.totalRows)this.pageTo=r.totalRows;if(this.options.pagination&&"server"!==this.options.sidePagination)this.options.totalNotFiltered=this.options.data.length;if(!this.options.showExtendedPagination)this.options.totalNotFiltered=void 0;if(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))a.push('<div class="'.concat(this.constants.classes.pull,"-").concat(r.paginationDetailHAlign,' pagination-detail">'));if(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var d=this.options.totalRows+("client"===this.options.sidePagination&&this.options.paginationLoadMore&&!this._paginationLoaded?" +":"");var p=this.paginationParts.includes("pageInfoShort")?r.formatDetailPagination(d):r.formatShowingRows(this.pageFrom,this.pageTo,d,r.totalNotFiltered);a.push('<span class="pagination-info">\n      '.concat(p,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){a.push('<div class="page-list">');var g=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(i?r.formatAllRows():r.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];v.forEach(function(t,a){if(!r.smartDisplay||0===a||v[a-1]<r.totalRows||t===r.formatAllRows()){var n;if(i)n=t===r.formatAllRows()?e.constants.classes.dropdownActive:"";else n=t===r.pageSize?e.constants.classes.dropdownActive:"";g.push(fy.sprintf(e.constants.html.pageDropdownItem,n,t))}});g.push("".concat(this.constants.html.pageDropdown[1],"</div>"));a.push(r.formatRecordsPerPage(g.join("")))}if(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))a.push("</div></div>");if(this.paginationParts.includes("pageList")){a.push('<div class="'.concat(this.constants.classes.pull,"-").concat(r.paginationHAlign,' pagination">'),fy.sprintf(this.constants.html.pagination[0],fy.sprintf(" pagination-%s",r.iconSize)),fy.sprintf(this.constants.html.paginationItem," page-pre",r.formatSRPaginationPreText(),r.paginationPreText));if(this.totalPages<r.paginationSuccessivelySize){o=1;s=this.totalPages}else{o=r.pageNumber-r.paginationPagesBySide;s=o+2*r.paginationPagesBySide}if(r.pageNumber<r.paginationSuccessivelySize-1)s=r.paginationSuccessivelySize;if(r.paginationSuccessivelySize>this.totalPages-o)o=o-(r.paginationSuccessivelySize-(this.totalPages-o))+1;if(o<1)o=1;if(s>this.totalPages)s=this.totalPages;var b=Math.round(r.paginationPagesBySide/2);var m=function P(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return fy.sprintf(e.constants.html.paginationItem,a+(t===r.pageNumber?" ".concat(e.constants.classes.paginationActive):""),r.formatSRPaginationPageText(t),t)};if(o>1){var y=r.paginationPagesBySide;if(y>=o)y=o-1;for(n=1;n<=y;n++)a.push(m(n));if(o-1===y+1){n=o-1;a.push(m(n))}else if(o-1>y)if(o-2*r.paginationPagesBySide>r.paginationPagesBySide&&r.paginationUseIntermediate){n=Math.round((o-b)/2+b);a.push(m(n," page-intermediate"))}else a.push(fy.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","..."))}for(n=o;n<=s;n++)a.push(m(n));if(this.totalPages>s){var w=this.totalPages-(r.paginationPagesBySide-1);if(s>=w)w=s+1;if(s+1===w-1){n=s+1;a.push(m(n))}else if(w>s+1)if(this.totalPages-s>2*r.paginationPagesBySide&&r.paginationUseIntermediate){n=Math.round((this.totalPages-b-s)/2+s);a.push(m(n," page-intermediate"))}else a.push(fy.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."));for(n=w;n<=this.totalPages;n++)a.push(m(n))}a.push(fy.sprintf(this.constants.html.paginationItem," page-next",r.formatSRPaginationNextText(),r.paginationNextText));a.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(a.join(""));var S=["bottom","both"].includes(r.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(S);if(!r.onlyInfoPagination){l=this.$pagination.find(".page-list a");c=this.$pagination.find(".page-pre");f=this.$pagination.find(".page-next");u=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator");if(this.totalPages<=1)this.$pagination.find("div.pagination").hide();if(r.smartDisplay)if(v.length<2||r.totalRows<=v[0])this.$pagination.find("div.page-list").hide();this.$pagination[this.getData().length?"show":"hide"]();if(!r.paginationLoop){if(1===r.pageNumber)c.addClass("disabled");if(r.pageNumber===this.totalPages)f.addClass("disabled")}if(i)r.pageSize=r.formatAllRows();l.off("click").on("click",function(t){return e.onPageListChange(t)});c.off("click").on("click",function(t){return e.onPagePre(t)});f.off("click").on("click",function(t){return e.onPageNext(t)});u.off("click").on("click",function(t){return e.onPageNumber(t)});if(this.options.showPageGo){var x=this,O=this.$pagination.find("ul.pagination"),k=O.find("li.pageGo");if(!k.length){k=t(['<li class="pageGo">',fy.sprintf('<input type="text" class="form-control" value="%s">',this.options.pageNumber),'<button class="btn'+fy.sprintf(" btn-%s",this.constants.buttonsClass)+fy.sprintf(" btn-%s",r.iconSize)+'" title="'+r.formatPageGo()+'" '+' type="button">'+r.formatPageGo(),"</button>","</li>"].join("")).appendTo(O);k.find("button").click(function(){var t=parseInt(k.find("input").val())||1;if(t<1||t>x.options.totalPages)t=1;x.selectPage(t)})}}}}},{key:"updatePagination",value:function O(e){if(e&&t(e.currentTarget).hasClass("disabled"))return;if(!this.options.maintainMetaData)this.resetRows();this.initPagination();this.trigger("page-change",this.options.pageNumber,this.options.pageSize);if("server"===this.options.sidePagination||"client"===this.options.sidePagination&&this.options.paginationLoadMore&&!this._paginationLoaded&&this.options.pageNumber===this.totalPages)this.initServer();else this.initBody()}},{key:"onPageListChange",value:function k(e){e.preventDefault();var r=t(e.currentTarget);r.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive);this.options.pageSize=r.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+r.text();this.$toolbar.find(".page-size").text(this.options.pageSize);this.updatePagination(e);return false}},{key:"onPagePre",value:function P(e){if(t(e.target).hasClass("disabled"))return;e.preventDefault();if(this.options.pageNumber-1===0)this.options.pageNumber=this.options.totalPages;else this.options.pageNumber--;this.updatePagination(e);return false}},{key:"onPageNext",value:function C(e){if(t(e.target).hasClass("disabled"))return;e.preventDefault();if(this.options.pageNumber+1>this.options.totalPages)this.options.pageNumber=1;else this.options.pageNumber++;this.updatePagination(e);return false}},{key:"onPageNumber",value:function T(e){e.preventDefault();if(this.options.pageNumber===+t(e.currentTarget).text())return;this.options.pageNumber=+t(e.currentTarget).text();this.updatePagination(e);return false}},{key:"initRow",value:function I(t,e,r,a){var n=this;var o=[];var s={};var c=[];var f="";var u={};var h=[];if(fy.findIndex(this.hiddenRows,t)>-1)return;s=fy.calculateObjectValue(this.options,this.options.rowStyle,[t,e],s);if(s&&s.css)for(var v=0,d=Object.entries(s.css);v<d.length;v++){var p=l(d[v],2),g=p[0],b=p[1];c.push("".concat(g,": ").concat(b))}u=fy.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u);if(u)for(var m=0,y=Object.entries(u);m<y.length;m++){var w=l(y[m],2),S=w[0],x=w[1];h.push("".concat(S,'="').concat(fy.escapeHTML(x),'"'))}if(t._data&&!fy.isEmptyObject(t._data))for(var O=0,k=Object.entries(t._data);O<k.length;O++){var P=l(k[O],2),C=P[0],T=P[1];if("index"===C)return;f+=" data-".concat(C,"='").concat("object"===i(T)?JSON.stringify(T):T,"'")}o.push("<tr",fy.sprintf(" %s",h.length?h.join(" "):void 0),fy.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),fy.sprintf(' class="%s"',s.classes||(Array.isArray(t)?void 0:t._class)),fy.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),fy.sprintf(' data-uniqueid="%s"',fy.getItemField(t,this.options.uniqueId,false)),fy.sprintf(' data-has-detail-view="%s"',this.options.detailView&&fy.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),fy.sprintf("%s",f),">");if(this.options.cardView)o.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";if(fy.hasDetailViewIcon(this.options)){I="<td>";if(fy.calculateObjectValue(null,this.options.detailFilter,[e,t]))I+='\n          <a class="detail-icon" href="#">\n          '.concat(fy.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ");I+="</td>"}if(I&&"right"!==this.options.detailViewAlign)o.push(I);this.header.fields.forEach(function(r,a){var i=n.columns[a];var s="";var f=fy.getItemField(t,r,n.options.escape,i.escape);var u="";var h="";var v={};var d="";var p=n.header.classes[a];var g="";var b="";var m="";var y="";var w="";var S="";if((n.fromHtml||n.autoMergeCells)&&"undefined"===typeof f)if(!i.checkbox&&!i.radio)return;if(!i.visible)return;if(n.options.cardView&&!i.cardVisible)return;if(c.concat([n.header.styles[a]]).length)b+="".concat(c.concat([n.header.styles[a]]).join("; "));if(t["_".concat(r,"_style")])b+="".concat(t["_".concat(r,"_style")]);if(b)g=' style="'.concat(b,'"');if(t["_".concat(r,"_id")])d=fy.sprintf(' id="%s"',t["_".concat(r,"_id")]);if(t["_".concat(r,"_class")])p=fy.sprintf(' class="%s"',t["_".concat(r,"_class")]);if(t["_".concat(r,"_rowspan")])y=fy.sprintf(' rowspan="%s"',t["_".concat(r,"_rowspan")]);if(t["_".concat(r,"_colspan")])w=fy.sprintf(' colspan="%s"',t["_".concat(r,"_colspan")]);if(t["_".concat(r,"_title")])S=fy.sprintf(' title="%s"',t["_".concat(r,"_title")]);v=fy.calculateObjectValue(n.header,n.header.cellStyles[a],[f,t,e,r],v);if(v.classes)p=' class="'.concat(v.classes,'"');if(v.css){var x=[];for(var O=0,k=Object.entries(v.css);O<k.length;O++){var P=l(k[O],2),C=P[0],T=P[1];x.push("".concat(C,": ").concat(T))}g=' style="'.concat(x.concat(n.header.styles[a]).join("; "),'"')}u=fy.calculateObjectValue(i,n.header.formatters[a],[f,t,e,r],f);if(!(i.checkbox||i.radio))u="undefined"===typeof u||null===u?n.options.undefinedText:u;if(i.searchable&&n.searchText&&n.options.searchHighlight&&!(i.checkbox||i.radio)){var I="";var A=n.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(n.options.searchAccentNeutralise){var $=new RegExp("".concat(fy.normalizeAccent(A)),"gmi");var R=$.exec(fy.normalizeAccent(u));if(R)A=u.substring(R.index,R.index+A.length)}var E=new RegExp("(".concat(A,")"),"gim");var j="<mark>$1</mark>";var _=u&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(u);if(_){var N=(new DOMParser).parseFromString(u.toString(),"text/html").documentElement.textContent;var F=N.replace(E,j);N=N.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");I=u.replace(new RegExp("(>\\s*)(".concat(N,")(\\s*)"),"gm"),"$1".concat(F,"$3"))}else I=u.toString().replace(E,j);u=fy.calculateObjectValue(i,i.searchHighlightFormatter,[u,n.searchText],I)}if(t["_".concat(r,"_data")]&&!fy.isEmptyObject(t["_".concat(r,"_data")]))for(var D=0,V=Object.entries(t["_".concat(r,"_data")]);D<V.length;D++){var B=l(V[D],2),L=B[0],H=B[1];if("index"===L)return;m+=" data-".concat(L,'="').concat(H,'"')}if(i.checkbox||i.radio){h=i.checkbox?"checkbox":h;h=i.radio?"radio":h;var M=i["class"]||"";var U=fy.isObject(u)&&u.hasOwnProperty("checked")?u.checked:(true===u||f)&&false!==u;var z=!i.checkboxEnabled||u&&u.disabled;s=[n.options.cardView?'<div class="card-view '.concat(M,'">'):'<td class="bs-checkbox '.concat(M,'"').concat(p).concat(g,">"),'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(n.options.selectItemName,'"\n            type="').concat(h,'"\n            ').concat(fy.sprintf('value="%s"',t[n.options.idField]),"\n            ").concat(fy.sprintf('checked="%s"',U?"checked":void 0),"\n            ").concat(fy.sprintf('disabled="%s"',z?"disabled":void 0)," />\n            <span></span>\n            </label>"),n.header.formatters[a]&&"string"===typeof u?u:"",n.options.cardView?"</div>":"</td>"].join("");t[n.header.stateField]=true===u||!!f||u&&u.checked}else if(n.options.cardView){var q=n.options.showHeader?'<span class="card-view-title '.concat(v.classes||"",'"').concat(g,">").concat(fy.getFieldTitle(n.columns,r),"</span>"):"";s='<div class="card-view">'.concat(q,'<span class="card-view-value ').concat(v.classes||"",'"').concat(g,">").concat(u,"</span></div>");if(n.options.smartDisplay&&""===u)s='<div class="card-view"></div>'}else s="<td".concat(d).concat(p).concat(g).concat(m).concat(y).concat(w).concat(S,">").concat(u,"</td>");o.push(s)});if(I&&"right"===this.options.detailViewAlign)o.push(I);if(this.options.cardView)o.push("</div></td>");o.push("</tr>");return o.join("")}},{key:"initBody",value:function A(e,r){var a=this;var i=this.getData();this.trigger("pre-body",i);this.$body=this.$el.find(">tbody");if(!this.$body.length)this.$body=t("<tbody></tbody>").appendTo(this.$el);if(!this.options.pagination||"server"===this.options.sidePagination){this.pageFrom=1;this.pageTo=i.length}var n=[];var o=t(document.createDocumentFragment());var s=false;var l=[];this.autoMergeCells=fy.checkAutoMergeCells(i.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var f=i[c];var u=this.initRow(f,c,i,o);s=s||!!u;if(u&&"string"===typeof u){var h=this.options.uniqueId;if(h&&f.hasOwnProperty(h)){var v=f[h];var d=this.$body.find(fy.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',v));var p=d.next();if(p.is("tr.detail-view")){l.push(c);if(!r||v!==r)u+=p[0].outerHTML}}if(!this.options.virtualScroll)o.append(u);else n.push(u)}}if(!s)this.$body.html('<tr class="no-records-found">'.concat(fy.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+fy.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>"));else if(!this.options.virtualScroll)this.$body.html(o);else{if(this.virtualScroll)this.virtualScroll.destroy();this.virtualScroll=new xy({rows:n,fixedScroll:e,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function g(t,e){a.fitHeader();a.initBodyEvent();a.trigger("virtual-scroll",t,e)}})}l.forEach(function(t){a.expandRow(t)});if(!e)this.scrollTo(0);this.initBodyEvent();this.initFooter();this.resetView();this.updateSelected();if("server"!==this.options.sidePagination)this.options.totalRows=i.length;this.trigger("post-body",i)}},{key:"initBodyEvent",value:function $(){var e=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(r){var a=t(r.currentTarget);if(a.find(".detail-icon").length||a.index()-fy.getDetailViewIndexOffset(e.options)<0)return;var i=a.parent();var n=t(r.target).parents(".card-views").children();var o=t(r.target).parents(".card-view");var s=i.data("index");var l=e.data[s];var c=e.options.cardView?n.index(o):a[0].cellIndex;var f=e.getVisibleFields();var u=f[c-fy.getDetailViewIndexOffset(e.options)];var h=e.columns[e.fieldsColumnsIndex[u]];var v=fy.getItemField(l,u,e.options.escape,h.escape);e.trigger("click"===r.type?"click-cell":"dbl-click-cell",u,v,l,a);e.trigger("click"===r.type?"click-row":"dbl-click-row",l,i,u);if("click"===r.type&&e.options.clickToSelect&&h.clickToSelect&&!fy.calculateObjectValue(e.options,e.options.ignoreClickToSelectOn,[r.target])){var d=i.find(fy.sprintf('[name="%s"]',e.options.selectItemName));if(d.length)d[0].click()}if("click"===r.type&&e.options.detailViewByClick)e.toggleDetailView(s,e.header.detailFormatters[e.fieldsColumnsIndex[u]])}).off("mousedown").on("mousedown",function(t){e.multipleSelectRowCtrlKey=t.ctrlKey||t.metaKey;e.multipleSelectRowShiftKey=t.shiftKey});this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(r){r.preventDefault();e.toggleDetailView(t(r.currentTarget).parent().parent().data("index"));return false});this.$selectItem=this.$body.find(fy.sprintf('[name="%s"]',this.options.selectItemName));this.$selectItem.off("click").on("click",function(r){r.stopImmediatePropagation();var a=t(r.currentTarget);e._toggleCheck(a.prop("checked"),a.data("index"))});this.header.events.forEach(function(r,a){var i=r;if(!i)return;if("string"===typeof i)i=fy.calculateObjectValue(null,i);if(!i)throw new Error("Unknown event in the scope: ".concat(r));var n=e.header.fields[a];var o=e.getVisibleFields().indexOf(n);if(o===-1)return;o+=fy.getDetailViewIndexOffset(e.options);var s=function c(r){if(!i.hasOwnProperty(r))return 1;var a=i[r];e.$body.find(">tr:not(.no-records-found)").each(function(i,s){var l=t(s);var c=l.find(e.options.cardView?".card-views>.card-view":">td").eq(o);var f=r.indexOf(" ");var u=r.substring(0,f);var h=r.substring(f+1);c.find(h).off(u).on(u,function(t){var r=l.data("index");var i=e.data[r];var o=i[n];a.apply(e,[t,o,i,r])})})};for(var l in i)if(s(l))continue})}},{key:"initServer",value:function R(e,r,a){var i=this;var n={};var o=this.header.fields.indexOf(this.options.sortName);var s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[o])s.sortName=this.header.sortNames[o];if(this.options.pagination&&"server"===this.options.sidePagination){s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize;s.pageNumber=this.options.pageNumber}if(!this.options.firstLoad&&!firstLoadTable.includes(this.options.id)){firstLoadTable.push(this.options.id);return}if(!(a||this.options.url)&&!this.options.ajax)return;if("limit"===this.options.queryParamsType){s={search:s.searchText,sort:s.sortName,order:s.sortOrder};if(this.options.pagination&&"server"===this.options.sidePagination){s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1);s.limit=this.options.pageSize;if(0===s.limit||this.options.pageSize===this.options.formatAllRows())delete s.limit}}if(this.options.search&&"server"===this.options.sidePagination&&this.options.searchable&&this.columns.filter(function(t){return t.searchable}).length){s.searchable=[];var l=b(this.columns),c;try{for(l.s();!(c=l.n()).done;){var f=c.value;if(!f.checkbox&&f.searchable&&(this.options.visibleSearch&&f.visible||!this.options.visibleSearch))s.searchable.push(f.field)}}catch(u){l.e(u)}finally{l.f()}}if(!fy.isEmptyObject(this.filterColumnsPartial))s.filter=JSON.stringify(this.filterColumnsPartial,null);fy.extend(s,r||{});n=fy.calculateObjectValue(this.options,this.options.queryParams,[s],n);if(false===n)return;if(!e)this.showLoading();var h=fy.extend({},fy.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:a||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(n):n,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function v(t,r,a){var n=fy.calculateObjectValue(i.options,i.options.responseHandler,[t,a],t);if("client"===i.options.sidePagination&&i.options.paginationLoadMore)i._paginationLoaded=i.data.length===n.length;i.load(n);i.trigger("load-success",n,a&&a.status,a);if(!e)i.hideLoading();if("server"===i.options.sidePagination&&i.options.pageNumber>1&&n[i.options.totalField]>0&&!n[i.options.dataField].length)i.updatePagination()},error:function d(t){if(t&&0===t.status&&i._xhrAbort){i._xhrAbort=false;return}var r=[];if("server"===i.options.sidePagination){r={};r[i.options.totalField]=0;r[i.options.dataField]=[]}i.load(r);i.trigger("load-error",t&&t.status,t);if(!e)i.hideLoading()}});if(this.options.ajax)fy.calculateObjectValue(this,this.options.ajax,[h],null);else{if(this._xhr&&4!==this._xhr.readyState){this._xhrAbort=true;this._xhr.abort()}this._xhr=t.ajax(h)}return n}},{key:"initSearchText",value:function E(){if(this.options.search){this.searchText="";if(""!==this.options.searchText){var t=fy.getSearchInput(this);t.val(this.options.searchText);this.onSearch({currentTarget:t,firedByInitSearchText:true})}}}},{key:"getCaret",value:function j(){var e=this;this.$header.find("th").each(function(r,a){t(a).find(".sortable").removeClass("desc asc").addClass(t(a).data("field")===e.options.sortName?e.options.sortOrder:"both")})}},{key:"updateSelected",value:function _(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e);this.$selectItem.each(function(e,r){t(r).closest("tr")[t(r).prop("checked")?"addClass":"removeClass"]("selected")})}},{key:"updateRows",value:function N(){var e=this;this.$selectItem.each(function(r,a){e.data[t(a).data("index")][e.header.stateField]=t(a).prop("checked")})}},{key:"resetRows",value:function F(){var t=b(this.data),e;try{for(t.s();!(e=t.n()).done;){var r=e.value;this.$selectAll.prop("checked",false);this.$selectItem.prop("checked",false);if(this.header.stateField)r[this.header.stateField]=false}}catch(a){t.e(a)}finally{t.f()}this.initHiddenRows()}},{key:"trigger",value:function D(r){var a,i;var n="".concat(r,".bs.table");for(var o=arguments.length,s=new Array(o>1?o-1:0),l=1;l<o;l++)s[l-1]=arguments[l];(a=this.options)[e.EVENTS[n]].apply(a,[].concat(s,[this]));this.$el.trigger(t.Event(n,{sender:this}),s);(i=this.options).onAll.apply(i,[n].concat([].concat(s,[this])));this.$el.trigger(t.Event("all.bs.table",{sender:this}),[n,s])}},{key:"resetHeader",value:function V(){var t=this;clearTimeout(this.timeoutId_);this.timeoutId_=setTimeout(function(){return t.fitHeader()},this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function B(){var e=this;if(this.$el.is(":hidden")){this.timeoutId_=setTimeout(function(){return e.fitHeader()},100);return}var r=this.$tableBody.get(0);var a=this.hasScrollBar&&r.scrollHeight>r.clientHeight+this.$header.outerHeight()?fy.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var i=this.$tableHeader.find(":focus");if(i.length>0){var n=i.parents("th");if(n.length>0){var o=n.attr("data-field");if(void 0!==o){var s=this.$header.find("[data-field='".concat(o,"']"));if(s.length>0)s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(true,true);this.$selectAll_=this.$header_.find('[name="btSelectAll"]');this.$tableHeader.css("margin-right",a).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_);this.$tableLoading.css("width",this.$el.outerWidth());var l=t(".focus-temp:visible:eq(0)");if(l.length>0){l.focus();this.$header.find(".focus-temp").removeClass("focus-temp")}this.$header.find("th[data-field]").each(function(r,a){e.$header_.find(fy.sprintf('th[data-field="%s"]',t(a).data("field"))).data(t(a).data())});var c=this.getVisibleFields();var f=this.$header_.find("th");var u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);while(u.length&&u.find('>td[colspan]:not([colspan="1"])').length)u=u.next();var h=u.find("> *").length;u.find("> *").each(function(r,a){var i=t(a);if(fy.hasDetailViewIcon(e.options))if(0===r&&"right"!==e.options.detailViewAlign||r===h-1&&"right"===e.options.detailViewAlign){var n=f.filter(".detail");var o=n.innerWidth()-n.find(".fht-cell").width();n.find(".fht-cell").width(i.innerWidth()-o);return}var s=r-fy.getDetailViewIndexOffset(e.options);var l=e.$header_.find(fy.sprintf('th[data-field="%s"]',c[s]));if(l.length>1)l=t(f[i[0].cellIndex]);var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(i.innerWidth()-u)});this.horizontalScroll();this.trigger("post-header")}},{key:"initFooter",value:function L(){if(!this.options.showFooter||this.options.cardView)return;var t=this.getData();var e=[];var r="";if(fy.hasDetailViewIcon(this.options))r='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>';if(r&&"right"!==this.options.detailViewAlign)e.push(r);var a=b(this.columns),i;try{for(a.s();!(i=a.n()).done;){var n=i.value;var o="";var s="";var c=[];var f={};var u=fy.sprintf(' class="%s"',n["class"]);if(!n.visible||this.footerData&&this.footerData.length>0&&!(n.field in this.footerData[0]))continue;if(this.options.cardView&&!n.cardVisible)return;o=fy.sprintf("text-align: %s; ",n.falign?n.falign:n.align);s=fy.sprintf("vertical-align: %s; ",n.valign);f=fy.calculateObjectValue(null,n.footerStyle||this.options.footerStyle,[n]);if(f&&f.css)for(var h=0,v=Object.entries(f.css);h<v.length;h++){var d=l(v[h],2),p=d[0],g=d[1];c.push("".concat(p,": ").concat(g))}if(f&&f.classes)u=fy.sprintf(' class="%s"',n["class"]?[n["class"],f.classes].join(" "):f.classes);e.push("<th",u,fy.sprintf(' style="%s"',o+s+c.concat().join("; ")||void 0));var m=0;if(this.footerData&&this.footerData.length>0)m=this.footerData[0]["_".concat(n.field,"_colspan")]||0;if(m)e.push(' colspan="'.concat(m,'" '));e.push(">");e.push('<div class="th-inner">');var y="";if(this.footerData&&this.footerData.length>0)y=this.footerData[0][n.field]||"";e.push(fy.calculateObjectValue(n,n.footerFormatter,[t,y],y));e.push("</div>");e.push('<div class="fht-cell"></div>');e.push("</div>");e.push("</th>")}}catch(w){a.e(w)}finally{a.f()}if(r&&"right"===this.options.detailViewAlign)e.push(r);if(!this.options.height&&!this.$tableFooter.length){this.$el.append("<tfoot><tr></tr></tfoot>");this.$tableFooter=this.$el.find("tfoot")}if(!this.$tableFooter.find("tr").length)this.$tableFooter.html("<table><thead><tr></tr></thead></table>");this.$tableFooter.find("tr").html(e.join(""));this.trigger("post-footer",this.$tableFooter)}},{key:"fitFooter",value:function H(){var e=this;if(this.$el.is(":hidden")){setTimeout(function(){return e.fitFooter()},100);return}var r=this.$tableBody.get(0);var a=this.hasScrollBar&&r.scrollHeight>r.clientHeight+this.$header.outerHeight()?fy.getScrollBarWidth():0;this.$tableFooter.css("margin-right",a).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var i=this.$tableFooter.find("th");var n=this.$body.find(">tr:first-child:not(.no-records-found)");i.find(".fht-cell").width("auto");while(n.length&&n.find('>td[colspan]:not([colspan="1"])').length)n=n.next();var o=n.find("> *").length;n.find("> *").each(function(r,a){var n=t(a);if(fy.hasDetailViewIcon(e.options))if(0===r&&"left"===e.options.detailViewAlign||r===o-1&&"right"===e.options.detailViewAlign){var s=i.filter(".detail");var l=s.innerWidth()-s.find(".fht-cell").width();s.find(".fht-cell").width(n.innerWidth()-l);return}var c=i.eq(r);var f=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(n.innerWidth()-f)});this.horizontalScroll()}},{key:"horizontalScroll",value:function M(){var t=this;this.$tableBody.off("scroll").on("scroll",function(){var e=t.$tableBody.scrollLeft();if(t.options.showHeader&&t.options.height)t.$tableHeader.scrollLeft(e);if(t.options.showFooter&&!t.options.cardView)t.$tableFooter.scrollLeft(e);t.trigger("scroll-body",t.$tableBody)})}},{key:"getVisibleFields",value:function U(){var t=[];var e=b(this.header.fields),r;try{for(e.s();!(r=e.n()).done;){var a=r.value;var i=this.columns[this.fieldsColumnsIndex[a]];if(!i||!i.visible||this.options.cardView&&!i.cardVisible)continue;t.push(a)}}catch(n){e.e(n)}finally{e.f()}return t}},{key:"initHiddenRows",value:function z(){this.hiddenRows=[]}},{key:"getOptions",value:function q(){var t=fy.extend({},this.options);delete t.data;return fy.extend(true,{},t)}},{key:"refreshOptions",value:function G(t){if(fy.compareObjects(this.options,t,true))return;this.options=fy.extend(this.options,t);this.trigger("refresh-options",this.options);this.destroy();this.init()}},{key:"getData",value:function W(t){var e=this;var r=this.options.data;if((this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort||!fy.isEmptyObject(this.filterColumns)||"function"===typeof this.options.filterOptions.filterAlgorithm||!fy.isEmptyObject(this.filterColumnsPartial))&&(!t||!t.unfiltered))r=this.data;if(t&&!t.includeHiddenRows){var a=this.getHiddenRows();r=r.filter(function(t){return fy.findIndex(a,t)===-1})}if(t&&t.useCurrentPage)r=r.slice(this.pageFrom-1,this.pageTo);if(t&&t.formatted)r.forEach(function(t){for(var r=0,a=Object.entries(t);r<a.length;r++){var i=l(a[r],2),n=i[0],o=i[1];var s=e.columns[e.fieldsColumnsIndex[n]];if(!s)return;t[n]=fy.calculateObjectValue(s,e.header.formatters[s.fieldIndex],[o,t,t.index,s.field],o)}});return r}},{key:"getSelections",value:function K(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter(function(e){return true===e[t.header.stateField]})}},{key:"load",value:function Y(t){var e=false;var r=t;if(this.options.pagination&&"server"===this.options.sidePagination){this.options.totalRows=r[this.options.totalField];this.options.totalNotFiltered=r[this.options.totalNotFilteredField];this.footerData=r[this.options.footerField]?[r[this.options.footerField]]:void 0}e=this.options.fixedScroll||r.fixedScroll;r=Array.isArray(r)?r:r[this.options.dataField];this.initData(r);this.initSearch();this.initPagination();this.initBody(e)}},{key:"append",value:function J(t){this.initData(t,"append");this.initSearch();this.initPagination();this.initSort();this.initBody(true)}},{key:"prepend",value:function X(t){this.initData(t,"prepend");this.initSearch();this.initPagination();this.initSort();this.initBody(true)}},{key:"remove",value:function Q(t){var e=0;for(var r=this.options.data.length-1;r>=0;r--){var a=this.options.data[r];var i=fy.getItemField(a,t.field,this.options.escape,a.escape);if(void 0===i&&"$index"!==t.field)continue;if(!a.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(r)||t.values.includes(i)){e++;this.options.data.splice(r,1)}}if(!e)return;if("server"===this.options.sidePagination){this.options.totalRows-=e;this.data=c(this.options.data)}this.initSearch();this.initPagination();this.initSort();this.initBody(true)}},{key:"removeAll",value:function Z(){if(this.options.data.length>0){this.options.data.splice(0,this.options.data.length);this.initSearch();this.initPagination();this.initBody(true)}}},{key:"insertRow",value:function tt(t){if(!t.hasOwnProperty("index")||!t.hasOwnProperty("row"))return;this.options.data.splice(t.index,0,t.row);this.initSearch();this.initPagination();this.initSort();this.initBody(true)}},{key:"updateRow",value:function et(t){var e=Array.isArray(t)?t:[t];var r=b(e),a;try{for(r.s();!(a=r.n()).done;){var i=a.value;if(!i.hasOwnProperty("index")||!i.hasOwnProperty("row"))continue;if(i.hasOwnProperty("replace")&&i.replace)this.options.data[i.index]=i.row;else fy.extend(this.options.data[i.index],i.row)}}catch(n){r.e(n)}finally{r.f()}this.initSearch();this.initPagination();this.initSort();this.initBody(true)}},{key:"getRowByUniqueId",value:function rt(t){var e=this.options.uniqueId;var r=this.options.data.length;var a=t;var i=null;var n;var o;for(n=r-1;n>=0;n--){o=this.options.data[n];var s=fy.getItemField(o,e,this.options.escape,o.escape);if(void 0===s)continue;if("string"===typeof s)a=t.toString();else if("number"===typeof s)if(Number(s)===s&&s%1===0)a=parseInt(t,10);else if(s===Number(s)&&0!==s)a=parseFloat(t);if(s===a){i=o;break}}return i}},{key:"updateByUniqueId",value:function at(t){var e=Array.isArray(t)?t:[t];var r=null;var a=b(e),i;try{for(a.s();!(i=a.n()).done;){var n=i.value;if(!n.hasOwnProperty("id")||!n.hasOwnProperty("row"))continue;var o=this.options.data.indexOf(this.getRowByUniqueId(n.id));if(o===-1)continue;if(n.hasOwnProperty("replace")&&n.replace)this.options.data[o]=n.row;else fy.extend(this.options.data[o],n.row);r=n.id}}catch(s){a.e(s)}finally{a.f()}this.initSearch();this.initPagination();this.initSort();this.initBody(true,r)}},{key:"removeByUniqueId",value:function it(t){var e=this.options.data.length;var r=this.getRowByUniqueId(t);if(r)this.options.data.splice(this.options.data.indexOf(r),1);if(e===this.options.data.length)return;if("server"===this.options.sidePagination){this.options.totalRows-=1;this.data=c(this.options.data)}this.initSearch();this.initPagination();this.initBody(true)}},{key:"_updateCellOnly",value:function nt(e,r){var a=this.initRow(this.options.data[r],r);var i=this.getVisibleFields().indexOf(e);if(i===-1)return;i+=fy.getDetailViewIndexOffset(this.options);this.$body.find(">tr[data-index=".concat(r,"]")).find(">td:eq(".concat(i,")")).replaceWith(t(a).find(">td:eq(".concat(i,")")));this.initBodyEvent();this.initFooter();this.resetView();this.updateSelected()}},{key:"updateCell",value:function ot(t){if(!t.hasOwnProperty("index")||!t.hasOwnProperty("field")||!t.hasOwnProperty("value"))return;this.options.data[t.index][t.field]=t.value;if(false===t.reinit){this._updateCellOnly(t.field,t.index);return}this.initSort();this.initBody(true)}},{key:"updateCellByUniqueId",value:function st(t){var e=this;var r=Array.isArray(t)?t:[t];r.forEach(function(t){var r=t.id,a=t.field,i=t.value;var n=e.options.data.indexOf(e.getRowByUniqueId(r));if(n===-1)return;e.options.data[n][a]=i});if(false===t.reinit){this._updateCellOnly(t.field,this.options.data.indexOf(this.getRowByUniqueId(t.id)));return}this.initSort();this.initBody(true)}},{key:"showRow",value:function lt(t){this._toggleRow(t,true)}},{key:"hideRow",value:function ct(t){this._toggleRow(t,false)}},{key:"_toggleRow",value:function ft(t,e){var r;if(t.hasOwnProperty("index"))r=this.getData()[t.index];else if(t.hasOwnProperty("uniqueId"))r=this.getRowByUniqueId(t.uniqueId);if(!r)return;var a=fy.findIndex(this.hiddenRows,r);if(!e&&a===-1)this.hiddenRows.push(r);else if(e&&a>-1)this.hiddenRows.splice(a,1);this.initBody(true);this.initPagination()}},{key:"getHiddenRows",value:function ut(t){if(t){this.initHiddenRows();this.initBody(true);this.initPagination();return}var e=this.getData();var r=[];var a=b(e),i;try{for(a.s();!(i=a.n()).done;){var n=i.value;if(this.hiddenRows.includes(n))r.push(n)}}catch(o){a.e(o)}finally{a.f()}this.hiddenRows=r;return r}},{key:"showColumn",value:function ht(t){var e=this;var r=Array.isArray(t)?t:[t];r.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],true,true)})}},{key:"hideColumn",value:function vt(t){var e=this;var r=Array.isArray(t)?t:[t];r.forEach(function(t){e._toggleColumn(e.fieldsColumnsIndex[t],false,true)})}},{key:"_toggleColumn",value:function dt(t,e,r){if(t===-1||this.columns[t].visible===e)return;this.columns[t].visible=e;this.initHeader();this.initSearch();this.initPagination();this.initBody();if(this.options.showColumns){var a=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",false);if(r)a.filter(fy.sprintf('[value="%s"]',t)).prop("checked",e);if(a.filter(":checked").length<=this.options.minimumCountColumns)a.filter(":checked").prop("disabled",true)}}},{key:"getVisibleColumns",value:function pt(){var t=this;return this.columns.filter(function(e){return e.visible&&!t.isSelectionColumn(e)})}},{key:"getHiddenColumns",value:function gt(){return this.columns.filter(function(t){var e=t.visible;return!e})}},{key:"isSelectionColumn",value:function bt(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function mt(){this._toggleAllColumns(true)}},{key:"hideAllColumns",value:function yt(){this._toggleAllColumns(false)}},{key:"_toggleAllColumns",value:function wt(e){var r=this;var a=b(this.columns.slice().reverse()),i;try{for(a.s();!(i=a.n()).done;){var n=i.value;if(n.switchable){if(!e&&this.options.showColumns&&this.getVisibleColumns().filter(function(t){return t.switchable}).length===this.options.minimumCountColumns)continue;n.visible=e}}}catch(o){a.e(o)}finally{a.f()}this.initHeader();this.initSearch();this.initPagination();this.initBody();if(this.options.showColumns){var s=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",false);if(e)s.prop("checked",e);else s.get().reverse().forEach(function(a){if(s.filter(":checked").length>r.options.minimumCountColumns)t(a).prop("checked",e)});if(s.filter(":checked").length<=this.options.minimumCountColumns)s.filter(":checked").prop("disabled",true)}}},{key:"mergeCells",value:function St(t){var e=t.index;var r=this.getVisibleFields().indexOf(t.field);var a=t.rowspan||1;var i=t.colspan||1;var n;var o;var s=this.$body.find(">tr[data-index]");r+=fy.getDetailViewIndexOffset(this.options);var l=s.eq(e).find(">td").eq(r);if(e<0||r<0||e>=this.data.length)return;for(n=e;n<e+a;n++)for(o=r;o<r+i;o++)s.eq(n).find(">td").eq(o).hide();l.attr("rowspan",a).attr("colspan",i).show()}},{key:"checkAll",value:function xt(){this._toggleCheckAll(true)}},{key:"uncheckAll",value:function Ot(){this._toggleCheckAll(false)}},{key:"_toggleCheckAll",value:function kt(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t);this.$selectItem.filter(":enabled").prop("checked",t);this.updateRows();this.updateSelected();var r=this.getSelections();if(t){this.trigger("check-all",r,e);return}this.trigger("uncheck-all",r,e)}},{key:"checkInvert",value:function Pt(){var e=this.$selectItem.filter(":enabled");var r=e.filter(":checked");e.each(function(e,r){t(r).prop("checked",!t(r).prop("checked"))});this.updateRows();this.updateSelected();this.trigger("uncheck-some",r);r=this.getSelections();this.trigger("check-some",r)}},{key:"check",value:function Ct(t){this._toggleCheck(true,t)}},{key:"uncheck",value:function Tt(t){this._toggleCheck(false,t)}},{key:"_toggleCheck",value:function It(t,e){var r=this.$selectItem.filter('[data-index="'.concat(e,'"]'));var a=this.data[e];if(r.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var i=b(this.options.data),n;try{for(i.s();!(n=i.n()).done;){var o=n.value;o[this.header.stateField]=false}}catch(s){i.e(s)}finally{i.f()}this.$selectItem.filter(":checked").not(r).prop("checked",false)}a[this.header.stateField]=t;if(this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0){var c=this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],f=l(c,2),u=f[0],h=f[1];for(var v=u+1;v<h;v++){this.data[v][this.header.stateField]=true;this.$selectItem.filter('[data-index="'.concat(v,'"]')).prop("checked",true)}}this.multipleSelectRowCtrlKey=false;this.multipleSelectRowShiftKey=false;this.multipleSelectRowLastSelectedIndex=t?e:-1}r.prop("checked",t);this.updateSelected();this.trigger(t?"check":"uncheck",this.data[e],r)}},{key:"checkBy",value:function At(t){this._toggleCheckBy(true,t)}},{key:"uncheckBy",value:function $t(t){this._toggleCheckBy(false,t)}},{key:"_toggleCheckBy",value:function Rt(t,e){var r=this;if(!e.hasOwnProperty("field")||!e.hasOwnProperty("values"))return;var a=[];this.data.forEach(function(i,n){if(!i.hasOwnProperty(e.field))return false;if(e.values.includes(i[e.field])){var o=r.$selectItem.filter(":enabled").filter(fy.sprintf('[data-index="%s"]',n));var s=e.hasOwnProperty("onlyCurrentPage")?e.onlyCurrentPage:false;o=t?o.not(":checked"):o.filter(":checked");if(!o.length&&s)return;o.prop("checked",t);i[r.header.stateField]=t;a.push(i);r.trigger(t?"check":"uncheck",i,o)}});this.updateSelected();this.trigger(t?"check-some":"uncheck-some",a)}},{key:"refresh",value:function Et(t){if(t&&t.url)this.options.url=t.url;if(t&&t.pageNumber)this.options.pageNumber=t.pageNumber;if(t&&t.pageSize)this.options.pageSize=t.pageSize;table.rememberSelecteds={};table.rememberSelectedIds={};this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function jt(){this.$el.insertBefore(this.$container);t(this.options.toolbar).insertBefore(this.$el);this.$container.next().remove();this.$container.remove();this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"");var e=fy.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(e)}},{key:"resetView",value:function _t(t){var e=0;if(t&&t.height)this.options.height=t.height;this.$tableContainer.toggleClass("has-card-view",this.options.cardView);if(this.options.height){var r=this.$tableBody.get(0);this.hasScrollBar=r.scrollWidth>r.clientWidth}if(!this.options.cardView&&this.options.showHeader&&this.options.height){this.$tableHeader.show();this.resetHeader();e+=this.$header.outerHeight(true)+1}else{this.$tableHeader.hide();this.trigger("post-header")}if(!this.options.cardView&&this.options.showFooter){this.$tableFooter.show();this.fitFooter();if(this.options.height)e+=this.$tableFooter.outerHeight(true)}if(this.$container.hasClass("fullscreen")){this.$tableContainer.css("height","");this.$tableContainer.css("width","")}else if(this.options.height){if(this.$tableBorder){this.$tableBorder.css("width","");this.$tableBorder.css("height","")}var a=this.$toolbar.outerHeight(true);var i=this.$pagination.outerHeight(true);var n=this.options.height-a-i;var o=this.$tableBody.find(">table");var s=o.outerHeight();this.$tableContainer.css("height","".concat(n,"px"));if(this.$tableBorder&&o.is(":visible")){var l=n-s-2;if(this.hasScrollBar)l-=fy.getScrollBarWidth();this.$tableBorder.css("width","".concat(o.outerWidth(),"px"));this.$tableBorder.css("height","".concat(l,"px"))}}if(this.options.cardView){this.$el.css("margin-top","0");this.$tableContainer.css("padding-bottom","0");this.$tableFooter.hide()}else{this.getCaret();this.$tableContainer.css("padding-bottom","".concat(e,"px"))}this.trigger("reset-view")}},{key:"showLoading",value:function Nt(){this.$tableLoading.toggleClass("open",true);var t=this.options.loadingFontSize;if("auto"===this.options.loadingFontSize){t=.04*this.$tableLoading.width();t=Math.max(12,t);t=Math.min(32,t);t="".concat(t,"px")}this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function Ft(){this.$tableLoading.toggleClass("open",false)}},{key:"toggleShowSearch",value:function Dt(){this.$el.parents(".select-table").siblings().slideToggle()}},{key:"togglePagination",value:function Vt(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"";var e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(fy.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e));this.updatePagination();this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function Bt(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen");this.resetView()}},{key:"toggleView",value:function Lt(){this.options.cardView=!this.options.cardView;this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"";var e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(fy.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)).attr("aria-label",e).attr("title",e);this.initBody();this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function Ht(t){var e=fy.getSearchInput(this);var r=t||"";e.val(r);this.searchText=r;this.onSearch({currentTarget:e},false)}},{key:"filterBy",value:function Mt(t,e){this.filterOptions=fy.isEmptyObject(e)?this.options.filterOptions:fy.extend(this.options.filterOptions,e);this.filterColumns=fy.isEmptyObject(t)?{}:t;this.options.pageNumber=1;this.initSearch();this.updatePagination()}},{key:"scrollTo",value:function Ut(e){var r={unit:"px",value:0};if("object"===i(e))r=Object.assign(r,e);else if("string"===typeof e&&"bottom"===e)r.value=this.$tableBody[0].scrollHeight;else if("string"===typeof e||"number"===typeof e)r.value=e;var Ut=r.value;if("rows"===r.unit){Ut=0;this.$body.find("> tr:lt(".concat(r.value,")")).each(function(e,r){Ut+=t(r).outerHeight(true)})}this.$tableBody.scrollTop(Ut)}},{key:"getScrollPosition",value:function zt(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function qt(t){if(t>0&&t<=this.options.totalPages){this.options.pageNumber=t;this.updatePagination()}}},{key:"prevPage",value:function Gt(){if(this.options.pageNumber>1){this.options.pageNumber--;this.updatePagination()}}},{key:"nextPage",value:function Wt(){if(this.options.pageNumber<this.options.totalPages){this.options.pageNumber++;this.updatePagination()}}},{key:"toggleDetailView",value:function Kt(t,e){var r=this.$body.find(fy.sprintf('> tr[data-index="%s"]',t));if(r.next().is("tr.detail-view"))this.collapseRow(t);else this.expandRow(t,e);this.resetView()}},{key:"expandRow",value:function Yt(t,e){var r=this.data[t];var a=this.$body.find(fy.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon)a.find("a.detail-icon").html(fy.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose));if(a.next().is("tr.detail-view"))return;a.after(fy.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',a.children("td").length));var i=a.next().find("td");var n=e||this.options.detailFormatter;var o=fy.calculateObjectValue(this.options,n,[t,r,i],"");if(1===i.length)i.append(o);this.trigger("expand-row",t,r,i)}},{key:"expandRowByUniqueId",value:function Jt(t){var e=this.getRowByUniqueId(t);if(!e)return;this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function Xt(t){var e=this.data[t];var r=this.$body.find(fy.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(!r.next().is("tr.detail-view"))return;if(this.options.detailViewIcon)r.find("a.detail-icon").html(fy.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen));this.trigger("collapse-row",t,e,r.next());r.next().remove()}},{key:"collapseRowByUniqueId",value:function Qt(t){var e=this.getRowByUniqueId(t);if(!e)return;this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function Zt(){var e=this.$body.find("> tr[data-index][data-has-detail-view]");for(var r=0;r<e.length;r++)this.expandRow(t(e[r]).data("index"))}},{key:"collapseAllRows",value:function te(){var e=this.$body.find("> tr[data-index][data-has-detail-view]");for(var r=0;r<e.length;r++)this.collapseRow(t(e[r]).data("index"))}},{key:"updateColumnTitle",value:function ee(e){if(!e.hasOwnProperty("field")||!e.hasOwnProperty("title"))return;this.columns[this.fieldsColumnsIndex[e.field]].title=this.options.escape&&this.options.escapeTitle?fy.escapeHTML(e.title):e.title;if(this.columns[this.fieldsColumnsIndex[e.field]].visible){this.$header.find("th[data-field]").each(function(r,a){if(t(a).data("field")===e.field){t(t(a).find(".th-inner")[0]).html(e.title);return false}});this.resetView()}}},{key:"updateFormatText",value:function re(t,e){if(!/^format/.test(t)||!this.options[t])return;if("string"===typeof e)this.options[t]=function(){return e};else if("function"===typeof e)this.options[t]=e;this.initToolbar();this.initPagination();this.initBody()}}])}();Oy.VERSION=yy.VERSION;Oy.DEFAULTS=yy.DEFAULTS;Oy.LOCALES=yy.LOCALES;Oy.COLUMN_DEFAULTS=yy.COLUMN_DEFAULTS;Oy.METHODS=yy.METHODS;Oy.EVENTS=yy.EVENTS;t.BootstrapTable=Oy;t.fn.bootstrapTable=function(e){for(var r=arguments.length,a=new Array(r>1?r-1:0),n=1;n<r;n++)a[n-1]=arguments[n];var o;this.each(function(r,n){var s=t(n).data("bootstrap.table");if("string"===typeof e){var l;if(!yy.METHODS.includes(e))throw new Error("Unknown method: ".concat(e));if(!s)return;o=(l=s)[e].apply(l,a);if("destroy"===e)t(n).removeData("bootstrap.table");return}if(s){console.warn("You cannot initialize the table more than once!");return}var c=fy.extend(true,{},Oy.DEFAULTS,t(n).data(),"object"===i(e)&&e);s=new t.BootstrapTable(n,c);t(n).data("bootstrap.table",s);s.init()});return"undefined"===typeof o?this:o};t.fn.bootstrapTable.Constructor=Oy;t.fn.bootstrapTable.theme=yy.THEME;t.fn.bootstrapTable.VERSION=yy.VERSION;t.fn.bootstrapTable.defaults=Oy.DEFAULTS;t.fn.bootstrapTable.columnDefaults=Oy.COLUMN_DEFAULTS;t.fn.bootstrapTable.events=Oy.EVENTS;t.fn.bootstrapTable.locales=Oy.LOCALES;t.fn.bootstrapTable.methods=Oy.METHODS;t.fn.bootstrapTable.utils=fy;t(function(){t('[data-toggle="table"]').bootstrapTable()});return Oy});var TABLE_EVENTS="all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table";var firstLoadTable=[];var union=function(t,e){if($.isPlainObject(e))addRememberRow(t,e);else if($.isArray(e))$.each(e,function(e,r){if($.isPlainObject(r))addRememberRow(t,r);else if($.inArray(r,t)==-1)t[t.length]=r});else if($.inArray(e,t)==-1)t[t.length]=e;return t};var difference=function(t,e){if($.isPlainObject(e))removeRememberRow(t,e);else if($.isArray(e))$.each(e,function(e,r){if($.isPlainObject(r))removeRememberRow(t,r);else{var a=$.inArray(r,t);if(a!=-1)t.splice(a,1)}});else{var r=$.inArray(e,t);if(r!=-1)t.splice(r,1)}return t};function getRememberRowIds(t,e){if($.isArray(t))props=$.map(t,function(t){return t[e]});else props=[t[e]];return props}function addRememberRow(t,e){var r=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId;var a=getRememberRowIds(t,r);if($.inArray(e[r],a)==-1)t[t.length]=e}function removeRememberRow(t,e){var r=null==table.options.uniqueId?table.options.columns[1].field:table.options.uniqueId;var a=getRememberRowIds(t,r);var i=$.inArray(e[r],a);if(i!=-1)t.splice(i,1)}var _={union:union,difference:difference};
