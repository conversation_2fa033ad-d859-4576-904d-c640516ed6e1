<view class="container">
  <view class="news-list">
    <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <view class="news-icon iconfont {{index % 4 === 0 ? 'icon-gonggao' : index % 4 === 1 ? 'icon-tongzhi' : index % 4 === 2 ? 'icon-xinwen' : 'icon-gonggao'}}"></view>
      <view class="news-content">
        <view class="news-info">
          <view class="news-title">{{item.title}}</view>
          <view class="news-desc">{{item.summary}}</view>
        </view>
        <view class="news-footer">
          <text class="news-time">{{item.time}}</text>
          <view class="news-right">
            <text class="news-type {{item.type === 'notice' ? 'notice' : ''}}">{{item.type === 'notice' ? '通知' : '公告'}}</text>
            <text class="iconfont icon-arrow"></text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{newsList.length === 0}}">
    <image class="empty-image" src="/static/images/empty.png" mode="aspectFit" />
    <text class="empty-text">暂无社区动态</text>
  </view>
</view> 