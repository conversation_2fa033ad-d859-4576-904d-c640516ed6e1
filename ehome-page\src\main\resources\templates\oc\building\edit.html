<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改楼栋信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-building-edit">
            <input name="building_id" type="hidden" th:value="${building.building_id}">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼栋序号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="name" placeholder="1,2,3,A,B,C" required>
                </div>
                <label class="col-sm-2 control-label">排序：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" placeholder="排序" name="order_index" value="0">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">单元总数：</label>
                <div class="col-sm-4">
                    <input class="form-control"  readonly type="number" name="total_units" value="1">
                </div>
                <label class="col-sm-2 control-label">户数：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" name="house_count" value="0" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">房屋面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" placeholder="平方米" name="house_area">
                </div>
                <label class="col-sm-2 control-label">楼栋管家：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="请输入楼栋管家" name="manager">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/building";
        
        $("#form-building-edit").validate({
            onkeyup: false,
            rules: {
                name: {
                    remote: {
                        url: prefix + "/checkName",
                        type: "post",
                        dataType: "json",
                        data: {
                            "building_id": function() {
                                return $("input[name='building_id']").val();
                            },
                            "name": function() {
                                return $.common.trim($("input[name='name']").val());
                            }
                        },
                        dataFilter: function(result) {
                            return result;
                        }
                    }
                },
            },
            messages: {
                "name": {
                    required: "请输入楼栋名称",
                    remote: "楼栋名称已经存在"
                }
            },
            focusCleanup: true
        });

        $(function(){
            $('#form-building-edit').renderForm({url:prefix+'/record'});
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/editSave", $('#form-building-edit').serialize());
            }
        }

        var getCommunityName = function(obj){
            var community_name = $(obj).find("option:selected").text();
            $("input[name='community_name']").val(community_name);
        }
    </script>
</body>
</html> 