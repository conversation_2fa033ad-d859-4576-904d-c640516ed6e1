import { getStateManager } from '../../utils/stateManager.js'

Page({
  data: {
    communityInfo: {
      communityName: '',
      communityBanner: '',
      address: '',
      propertyCompany: '',
      servicePhone: '',
      officeHours: '',
      intro: '',
      facilities: [
        // { name: '健身房', icon: '/static/icons/gym.png' }
      ]
    }
  },
  onLoad() {
    this.loadCommunityInfo()
  },
  loadCommunityInfo() {
    // 从状态管理器获取小区信息
    const stateManager = getStateManager()
    const state = stateManager.getState()
    const info = state.communityInfo || {}
    this.setData({ communityInfo: info })
    wx.setNavigationBarTitle({ title: info.communityName || '小区信息' })
  },
  callServicePhone() {
    const phone = this.data.communityInfo.servicePhone
    if (phone) wx.makePhoneCall({ phoneNumber: phone })
  },
  goToNoticeList() {
    wx.navigateTo({ url: '/pages/notice/list' })
  },
  openMap() {
    const { latitude, longitude, communityName, address } = this.data.communityInfo
    if (latitude && longitude) {
      wx.openLocation({
        latitude, longitude, name: communityName, address
      })
    } else {
      wx.showToast({ title: '暂无位置信息', icon: 'none' })
    }
  }
})