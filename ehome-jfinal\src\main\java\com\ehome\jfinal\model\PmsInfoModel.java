package com.ehome.jfinal.model;

import com.ehome.common.constant.Constants;
import com.ehome.common.utils.CacheUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.jfinal.annotation.Table;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Table(tableName = "eh_pms_info", primaryKey = "pms_id")
public class PmsInfoModel extends BaseModel<PmsInfoModel> {
    public static final PmsInfoModel dao = new PmsInfoModel().dao();

    public PmsInfoModel getPmsInfoById(String pmsId) {
        return dao.findById(pmsId);
    }

   public String getPmsName(String pmsId) {
        if (StringUtils.isNotBlank(pmsId)) {
            Object pmsName = CacheUtils.get(Constants.SYS_AUTH_CACHE, "pmsName_"+pmsId);
           if (pmsName != null) {
                return pmsName.toString();
            }
            PmsInfoModel model = dao.findById(pmsId);
            if (model == null) {
                return pmsId;
            }
            String _pmsName = model.get("pms_name");
            CacheUtils.put(Constants.SYS_AUTH_CACHE, "pmsName_"+pmsId,_pmsName);
            return _pmsName;
        }
       return "";
    }

   public List<Map> queryCommunity(String pmsId) {
        List<Map> _list = new ArrayList<Map>();
        if (StringUtils.isNotBlank(pmsId)) {
            List<Map> list = (List<Map>) CacheUtils.get(Constants.SYS_AUTH_CACHE, "community_"+pmsId);
           if (list != null) {
                return list;
            }
            List<OcInfoModel>  newList = OcInfoModel.dao.find("select * from eh_community where pms_id = ?", pmsId);
            if (_list == null) {
                return new ArrayList<Map>();
            }
            for (OcInfoModel model : newList) {
                _list.add(model.toMap());
            }
            CacheUtils.put(Constants.SYS_AUTH_CACHE, "community_"+pmsId,_list);
            return _list;
        }
       return _list;
    }
}
