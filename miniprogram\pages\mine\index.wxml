<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-header">
    <view class="user-card">
      <block wx:if="{{isLogin}}">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-info">
          <text class="nickname">{{userInfo.nickName}}</text>
          <text class="status {{isHouseAuth ? 'auth' : ''}}">{{isHouseAuth ? '已认证业主' : '未认证访客'}}</text>
          
        </view>
      </block>
      <block wx:else>
        <image class="avatar" src="/static/images/default-avatar.png" mode="aspectFill"></image>
        <view class="login-btn" bindtap="goToLogin">点击登录</view>
      </block>
    </view>
  </view>

  <!-- 我的服务 -->
  <view class="section-title">我的服务</view>
  <view class="menu-list">
    <view class="menu-item" bindtap="goToHouseInfo">
      <view class="menu-item-left">
        <view class="iconfont icon-house"></view>
        <text>我的房屋</text>
      </view>
      <view class="menu-item-right">
        <text class="desc" wx:if="{{isAuth}}">{{userInfo.houseInfo || '暂无房屋信息'}}</text>
        <text class="desc" wx:if="{{isHouseAuth && ownerInfo}}">{{ownerInfo.houseStr}}</text>
        <view class="iconfont icon-arrow"></view>
      </view>
    </view>

    <view class="menu-item" bindtap="goToProfile">
      <view class="menu-item-left">
        <view class="iconfont icon-profile"></view>
        <text>个人信息</text>
      </view>
      <view class="menu-item-right">
        <text class="desc" wx:if="{{isLogin}}">{{userInfo.mobile || '未绑定手机号'}}</text>
        <view class="iconfont icon-arrow"></view>
      </view>
    </view>

    <view class="menu-item" bindtap="goToSuggestion">
      <view class="menu-item-left">
        <view class="iconfont icon-suggestion"></view>
        <text>投诉建议</text>
      </view>
      <view class="menu-item-right">
        <view class="iconfont icon-arrow"></view>
      </view>
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="section-title">其他功能</view>
  <view class="menu-list">
    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-item-left">
        <view class="iconfont icon-about"></view>
        <text>关于我们</text>
      </view>
      <view class="menu-item-right">
        <view class="iconfont icon-arrow"></view>
      </view>
    </view>

    <button class="contact-btn" open-type="contact">
      <view class="menu-item">
        <view class="menu-item-left">
          <view class="iconfont icon-service"></view>
          <text>联系客服</text>
        </view>
        <view class="menu-item-right">
          <view class="iconfont icon-arrow"></view>
        </view>
      </view>
    </button>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-btn" bindtap="handleLogout" wx:if="{{isLogin}}">退出登录</view>

  <!-- 版本信息 -->
  <view class="version-info">当前版本 1.0.0</view>
</view> 