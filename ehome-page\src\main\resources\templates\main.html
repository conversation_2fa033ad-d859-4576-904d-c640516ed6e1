<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('房屋信息列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <style>
        body {
            background: #f4f6fa;
        }
        .dashboard-container {
            margin: 20px auto 0 auto;
            padding: 0 24px;
        }
        .stats-row {
            display: flex;
            gap: 24px;
            margin-bottom: 15px;
        }
        .stat-card {
            flex: 1;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 20px 0 10px 0;
            text-align: center;
            transition: box-shadow 0.2s;
        }
        .stat-card:hover {
            box-shadow: 0 4px 16px rgba(0,123,255,0.08);
        }
        .stat-icon {
            font-size: 35.2px;
            margin-bottom: 10px;
        }
        .stat-title {
            font-size: 17.6px;
            color: #888;
            margin-bottom: 6px;
        }
        .stat-value {
            font-size: 32px;
            font-weight: 600;
            color: #007bff;
        }
        .main-row {
            display: flex;
            gap: 24px;
        }
        .left-col, .right-col {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 24px 20px 20px 20px;
        }
        .left-col {
            flex: 2;
            min-width: 340px;
        }
        .right-col {
            flex: 1.2;
            min-width: 260px;
        }
        .section-title {
            font-size: 18.4px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }
        .announcement-list {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 220px;
            overflow-y: auto;
        }
        .announcement-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            color: #444;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .announcement-list li:last-child {
            border-bottom: none;
        }
        .announcement-title {
            display: flex;
            align-items: center;
        }
        .announcement-title .fa {
            color: #007bff;
            margin-right: 8px;
        }
        .announcement-date {
            color: #aaa;
            font-size: 15.2px;
            margin-left: 12px;
        }
        .quick-entry {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 18px;
        }
        .quick-btn {
            flex: 1 1 40%;
            min-width: 110px;
            background: #f4f8ff;
            border: none;
            border-radius: 8px;
            padding: 18px 0 10px 0;
            font-size: 15px;
            color: #007bff;
            text-align: center;
            transition: background 0.2s;
            margin-bottom: 0;
            cursor: pointer;
            text-decoration: none;
        }
        .quick-btn:hover {
            background: #e3f0ff;
            color: #0056b3;
        }
        .quick-btn i {
            font-size: 24px;
            margin-bottom: 6px;
            display: block;
        }
        .sys-info {
            color: #666;
            font-size: 16px;
            margin-top: 18px;
            line-height: 1.7;
        }
        .footer {
            text-align: center;
            color: #aaa;
            font-size: 13px;
            margin: 40px 0 10px 0;
            background-color: inherit;
            border-top: none;
        }
        @media (max-width: 900px) {
            .dashboard-container { max-width: 100%; }
            .stats-row, .main-row { flex-direction: column; gap: 16px; }
            .left-col, .right-col { min-width: 0; }
        }
    </style>

</head>
<body class="gray-bg">
<div class="dashboard-container">
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-icon"><i class="fa fa-users text-primary"></i></div>
            <div class="stat-title">业主总数</div>
            <div class="stat-value" id="ownerCount">--</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fa fa-building text-success"></i></div>
            <div class="stat-title">房屋总数</div>
            <div class="stat-value" id="houseCount">--</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fa fa-car text-warning"></i></div>
            <div class="stat-title">车辆总数</div>
            <div class="stat-value" id="vehicleCount">--</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fa fa-tasks text-info"></i></div>
            <div class="stat-title">待办事项</div>
            <div class="stat-value" id="todoCount">--</div>
        </div>
    </div>
    <div class="main-row">
        <div class="left-col">
            <div class="section-title"><i class="fa fa-bullhorn"></i> 最新公告</div>
            <ul class="announcement-list" id="announcementList">
                <li>公告加载中...</li>
            </ul>
        </div>
        <div class="right-col">
            <div class="section-title"><i class="fa fa-rocket"></i> 快捷入口</div>
            <div class="quick-entry">
                <a data-href="oc/owner/mgr" class="quick-btn"><i class="fa fa-user"></i>业主管理</a>
                <a data-href="oc/house/mgr" class="quick-btn"><i class="fa fa-building"></i>房屋管理</a>
                <a data-href="oc/vehicle/mgr" class="quick-btn"><i class="fa fa-car"></i>车辆管理</a>
                <a data-href="oc/fee/mgr" class="quick-btn"><i class="fa fa-money"></i>费用管理</a>
            </div>
            <div class="section-title" style="margin-top:24px;"><i class="fa fa-info-circle"></i> 系统简介</div>
            <div class="sys-info">
                本系统为物业管理平台，支持业主、房屋、车辆、费用等全流程管理，助力社区数字化升级。<br>
                如有疑问请联系技术支持。
            </div>
        </div>
    </div>
    <div class="footer">
        &copy; 2025 物业管理系统 | 技术支持：新太新
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: layout-latest-js" />
<script th:inline="javascript">
    $(function () {

        $('.quick-entry a').click(function (){
            var $a = $(this);
            if ($a.attr('data-href')) {
                var url =  $a.attr('data-href');
                var title =  $a.text();
                $.modal.openTab(title, ctx+url);
            }
        });

        $.ajax({
            url: ctx+"oc/home/<USER>",
            type: "POST",
            dataType: "json",
            success: function (res) {
                if (res.code === 0 && res.data) {
                    $("#ownerCount").text(res.data.ownerCount);
                    $("#houseCount").text(res.data.houseCount);
                    $("#vehicleCount").text(res.data.vehicleCount);
                    // 待办事项可根据实际业务补充，这里暂用0
                    $("#todoCount").text('0');
                    var annList = res.data.announcements;
                    var $ul = $("#announcementList");
                    $ul.empty();
                    if (annList && annList.length > 0) {
                        annList.forEach(function (a) {
                            $ul.append(
                                '<li><span class="announcement-title" onclick="viewNotice(\'' + a.id + '\')"><i class="fa fa-bullhorn"></i>' +
                                a.title + '</span>' +
                                (a.publish_date ? '<span class="announcement-date">' + a.publish_date + '</span>' : '') +
                                '</li>'
                            );
                        });
                    } else {
                        $ul.append('<li>暂无公告</li>');
                    }
                } else {
                    $("#ownerCount,#houseCount,#vehicleCount,#todoCount").text("--");
                    $("#announcementList").html('<li>公告获取失败</li>');
                }
            },
            error: function () {
                $("#ownerCount,#houseCount,#vehicleCount,#todoCount").text("--");
                $("#announcementList").html('<li>公告获取失败</li>');
            }
        });
    });

    function viewNotice(id) {
        $.modal.popupRight("公告详情", ctx + 'system/notice/view/' + id);
    }

</script>
</body>
</html>