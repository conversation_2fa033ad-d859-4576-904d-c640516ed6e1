<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ehome</artifactId>
        <groupId>com.ehome</groupId>
        <version>4.7.9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>ehome-oc</artifactId>

    <description>
        oc服务入口
    </description>

    <dependencies>
        <!-- framework 依赖 -->
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-jfinal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
