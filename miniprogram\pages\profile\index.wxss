.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 20rpx;
}

.profile-list {
  background: #fff;
  padding: 0 30rpx;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.profile-item:last-child {
  border-bottom: none;
}

.profile-item .label {
  font-size: 28rpx;
  color: #333;
}

.profile-item .value {
  display: flex;
  align-items: center;
}

.profile-item .value text {
  font-size: 28rpx;
  color: #666;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
}

.btn-text {
  color: #07c160 !important;
  margin-left: 20rpx;
}

.auth-text {
  color: #07c160 !important;
}

/* 手机号绑定弹窗 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.popup-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.bind-phone-btn {
  width: 100% !important;
  height: 88rpx;
  line-height: 88rpx;
  background: #07c160 !important;
  color: #fff !important;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-bottom: 30rpx;
  font-weight: 500;
  border: none;
}

.bind-phone-btn::after {
  border: none;
}

.popup-close {
  font-size: 28rpx;
  color: #999;
  padding: 20rpx;
} 