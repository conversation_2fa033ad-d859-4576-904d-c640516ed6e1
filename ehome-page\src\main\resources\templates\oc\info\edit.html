<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改小区信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-edit">
            <input name="oc_id" th:value="${ocInfo.oc_id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">小区名称：</label>
                <div class="col-sm-8">
                    <input id="oc_name" name="oc_name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">小区地址：</label>
                <div class="col-sm-8">
                    <input id="oc_address" name="oc_address" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系人信息：</label>
                <div class="col-sm-8">
                    <input id="oc_link" name="oc_link" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">服务电话：</label>
                <div class="col-sm-8">
                    <input id="service_phone" name="service_phone" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">小区状态：</label>
                <div class="col-sm-8">
                    <select name="oc_state" class="form-control">
                        <option value="0">正常</option>
                        <option value="1">维护中</option>
                        <option value="2">关闭</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">所属物业：</label>
                <div class="col-sm-8">
                    <input id="pms_name" name="pms_name" type="hidden">
                    <select name="pms_id" class="form-control" onchange="getPmsName(this);" data-url="/pmsMap">
                       <option value="">--</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">小区面积：</label>
                <div class="col-sm-8">
                    <input id="oc_area" name="oc_area" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">楼宇栋数：</label>
                <div class="col-sm-8">
                    <input id="building_num" name="building_num" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">所属社区：</label>
                <div class="col-sm-8">
                    <input id="community_name" name="community_name" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    var prefix = ctx + "oc/info";
	    
	    $("#form-config-edit").validate({
	    	onkeyup: false,
	        rules: {
	            oc_name: {
	                remote: {
	                    url: prefix + "/checkName",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "oc_id": function() {
	                            return $("input[name='oc_id']").val();
	                        },
	                        "oc_name": function() {
	                            return $.common.trim($("#oc_name").val());
	                        }
	                    },
                        dataFilter: function(result) {
                            return result;
                        }
	                }
	            },
	        },
	        messages: {
	            "oc_name": {
                    required: "请输入小区名称",
	                remote: "小区名称已经存在"
	            }
	        },
	        focusCleanup: true
	    });

        $(function(){
            $('#form-config-edit').renderSelect({prefix:prefix},function(){
                $('#form-config-edit').renderForm({url:prefix+'/record'});
            });
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/editSave", $('#form-config-edit').serialize());
            }
        }

        var getPmsName = function(obj){
            var pms_name = $(obj).find("option:selected").text();
            $("input[name='pms_name']").val(pms_name);
        }
    </script>
</body>
</html>
