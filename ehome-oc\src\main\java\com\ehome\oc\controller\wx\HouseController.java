package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/wx/house")
public class HouseController extends BaseWxController {

    @Autowired
    private IHouseInfoService houseInfoService;

    /**
     * 获取房屋列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        try {
            Long wxUserId = getCurrentUserId();
            if (wxUserId == null) {
                return AjaxResult.error("用户未登录");
            }

            List<HouseInfo> list = new ArrayList<HouseInfo>();
            List<Record> houseRecordList = Db.find("SELECT t1.* from eh_house_info t1,eh_owner t2 ,eh_house_owner_rel t3 where t1.house_id = t3.house_id and  t2.owner_id = t3.owner_id and t2.mobile = ?", getCurrentUser().getMobile());
            if(houseRecordList!=null){
                for(Record record:houseRecordList){
                    HouseInfo house = houseInfoService.recordToObj(record);
                    list.add(house);
                }
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", list);
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取房屋列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取房屋列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取房屋详情
     */
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam Long houseId) {
        try {
            Long wxUserId = getCurrentUserId();
            Record info = Db.findFirst("select * from eh_house_info where house_id = ?",houseId);
            if (info == null) {
                return AjaxResult.error("房屋不存在");
            }
            return AjaxResult.success(info.toMap());
        } catch (Exception e) {
            return AjaxResult.error("获取房屋详情失败: " + e.getMessage());
        }
    }

    /**
     * 添加房屋
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody HouseInfo houseInfo) {
        try {
            // 获取当前登录用户ID
            Long wxUserId = getCurrentUserId();
            if (wxUserId == null) {
                return AjaxResult.error("用户未登录");
            }

            // 设置用户ID
            houseInfo.setWxUserId(wxUserId);
            
            // 检查该用户的房屋数量是否超过限制
            int count = houseInfoService.countHouseByUserId(wxUserId);
            if (count >= 5) {
                return AjaxResult.error("最多只能添加5套房屋");
            }

            // 如果是第一套房屋，设置为默认
            if (count == 0) {
                houseInfo.setIsDefault(1);
            } else {
                houseInfo.setIsDefault(0);
            }

            // 设置初始状态为待审核
            houseInfo.setStatus(0);
            
            // 添加房屋
            int rows = houseInfoService.addHouse(houseInfo);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("添加失败");
        } catch (Exception e) {
            logger.error("添加房屋失败: " + e.getMessage(), e);
            return AjaxResult.error("添加房屋失败: " + e.getMessage());
        }
    }

    /**
     * 更新房屋
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody HouseInfo house) {
        try {
            Long wxUserId = getCurrentUserId();
            // 验证是否是当前用户的房屋
            HouseInfo oldHouse = houseInfoService.selectHouseById(house.getHouseId());
            if (oldHouse == null) {
                return AjaxResult.error("房屋不存在");
            }
            if (!oldHouse.getWxUserId().equals(wxUserId)) {
                return AjaxResult.error("无权修改该房屋信息");
            }

            // 设置不允许修改的字段
            house.setWxUserId(oldHouse.getWxUserId());
            house.setCommunityId(oldHouse.getCommunityId());
            house.setCommunityName(oldHouse.getCommunityName());
            house.setIsDefault(oldHouse.getIsDefault());
            
            int rows = houseInfoService.updateHouse(house);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("修改失败");
        } catch (Exception e) {
            return AjaxResult.error("修改房屋失败: " + e.getMessage());
        }
    }

    /**
     * 删除房屋
     */
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody Map<String, Long> params) {
        try {
            Long wxUserId = getCurrentUserId();
            Long houseId = params.get("houseId");
            // 验证是否是当前用户的房屋
            HouseInfo house = houseInfoService.selectHouseById(houseId);
            if (house == null) {
                return AjaxResult.error("房屋不存在");
            }
            if (!house.getWxUserId().equals(wxUserId)) {
                return AjaxResult.error("无权删除该房屋");
            }

            int rows = houseInfoService.deleteHouse(houseId);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("删除失败");
        } catch (Exception e) {
            return AjaxResult.error("删除房屋失败: " + e.getMessage());
        }
    }

    /**
     * 设置默认房屋
     */
    @PostMapping("/set-default")
    public AjaxResult setDefault(@RequestBody Map<String, Long> params) {
        try {
            Long wxUserId = getCurrentUserId();
            Long houseId = params.get("houseId");
            // 验证是否是当前用户的房屋
            HouseInfo house = houseInfoService.selectHouseById(houseId);
            if (house == null) {
                return AjaxResult.error("房屋不存在");
            }
            if (!house.getWxUserId().equals(wxUserId)) {
                return AjaxResult.error("无权设置该房屋");
            }

            int rows = houseInfoService.setDefaultHouse(houseId, wxUserId);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("设置失败");
        } catch (Exception e) {
            return AjaxResult.error("设置默认房屋失败: " + e.getMessage());
        }
    }
} 