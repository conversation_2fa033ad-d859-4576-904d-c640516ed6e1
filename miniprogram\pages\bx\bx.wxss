.form {
  padding: 40rpx 32rpx 0 32rpx;
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 48rpx;
}
.form-item {
  margin-bottom: 48rpx;
}
.label {
  font-size: 32rpx;
  color: #222;
  font-weight: 700;
  margin-bottom: 18rpx;
  display: block;
}
.required {
  color: #ff4d4f;
  margin-left: 4rpx;
  font-size: 28rpx;
}
.custom-radio-group {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-top: 16rpx;
}
.custom-radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.custom-radio-circle {
  width: 42rpx;
  height: 42rpx;
  border-radius: 50%;
  border: 2rpx solid #d9d9d9;
  background: #fff;
  margin-right: 18rpx;
  position: relative;
  box-sizing: border-box;
}
.custom-radio-circle.checked {
  border-color: #1890ff;
  background: #1890ff;
}
.custom-radio-circle.checked::after {
  content: '';
  display: block;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  left: 10rpx;
  top: 10rpx;
}
.custom-radio-label {
  font-size: 32rpx;
  color: #222;
}
input {
  width: 100%;
  background: #f5f6fa;
  border-radius: 12rpx;
  border: 1rpx solid #e5e6eb;
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #222;
  margin-top: 12rpx;
  box-sizing: border-box;
}
textarea {
  width: 100%;
  background: #f5f6fa;
  border-radius: 12rpx;
  border: 1rpx solid #e5e6eb;
  min-height: 140rpx;
  line-height: 1.6;
  padding: 16rpx 20rpx;
  font-size: 32rpx;
  color: #222;
  margin-top: 12rpx;
  box-sizing: border-box;
  resize: none;
}
input::placeholder,
textarea::placeholder {
  color: #bbb;
  font-size: 28rpx;
}

input[readonly] {
  color: #aaa;
  background: #f5f5f5;
}

.media-list {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 12rpx;
}
.media-thumb {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
  object-fit: cover;
  background: #eee;
  border: 2rpx solid #e5e5e5;
}
.media-add {
  width: 140rpx;
  height: 140rpx;
  background: #f2f3f5;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #bbb;
}
.submit-btn {
  width: 100%!important;
  height: 70rpx;
  line-height:40rpx;
  background: #1890ff;
  color: #fff;
  font-size: 26rpx;
  border-radius: 12rpx;
  margin-top: 32rpx;
  margin-bottom: 40rpx;
  letter-spacing: 2rpx;
  border: none;
  box-shadow: none;
  outline: none;
}
button::after {
  border: none !important;
  border-radius: 0 !important;
}
button[disabled],
button[loading] {
  background: #e5e5e5 !important;
  color: #aaa !important;
}
.label-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.history-link {
  color: #1890ff;
  font-size: 26rpx;
  margin-left: 16rpx;
  text-decoration: underline;
} 