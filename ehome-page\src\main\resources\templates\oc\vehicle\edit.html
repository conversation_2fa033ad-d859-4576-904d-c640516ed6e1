<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改车辆')" />
    <style>
       .bind-btn{
        line-height: 31px;
       }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-vehicle-edit">
            <input name="vehicle_id" type="hidden" th:value="${vehicle.vehicle_id}">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">车牌号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="plate_no" required>
                </div>
                <label class="col-sm-2 control-label">车辆类型：</label>
                <div class="col-sm-4">
                    <select class="form-control" name="vehicle_type">
                        <option value="">请选择车辆类型</option>
                        <option value="业主车辆">业主车辆</option>
                        <option value="非业主车辆">非业主车辆</option>
                       
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">车主姓名：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_real_name">
                </div>
                <label class="col-sm-2 control-label">车主手机号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_phone">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定住户：</label>
                <div class="col-sm-6">
                    <input type="hidden" name="owner_id" class="form-control">
                    <input class="form-control" type="text" readonly name="owner_name">
                </div>
                <div class="col-sm-4">
                    <a class="bind-btn" href="javascript:void(0);" onclick="editBindOwner();">修改</a>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定房屋：</label>
                <div class="col-sm-6">
                        <input type="hidden" name="house_id" class="form-control">
                        <input class="form-control" type="text" readonly name="house_name" placeholder="请选择房屋">
                </div>
                <div class="col-sm-4">
                    <a class="bind-btn" href="javascript:void(0);" onclick="editBindHouse();">修改</a>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定车位：</label>
                <div class="col-sm-6">
                    <input type="hidden" name="parking_space_id" class="form-control">
                    <input class="form-control" type="text" readonly name="parking_space">
                </div>
                <div class="col-sm-4">
                    <a class="bind-btn" href="javascript:void(0);" onclick="editBindHouse();">修改</a>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
   
    <script type="text/javascript">
        var prefix = ctx + "oc/vehicle";
        
        $("#form-vehicle-edit").validate({
            focusCleanup: true
        });

        $(function() {
            $('#form-vehicle-edit').renderForm({url:prefix+'/record'});
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-vehicle-edit').serialize());
            }
        }

        function editBindHouse() {
            $.modal.open('xx', ctx + 'oc/house/selectDialog', '700px', '80%', function(){

            });
        }

        // 供弹窗回调
        function setHouse(houseId, houseName) {
            $("input[name='house_id']").val(houseId);
            $("input[name='house_name']").val(houseName);
            layer.closeAll();
        }
    </script>
</body>
</html>