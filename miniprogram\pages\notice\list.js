// 通知列表页面重构版本
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { formatTime, filterHtmlTags } from '../../utils/common.js'

const app = getApp()
const loadingManager = getLoadingManager()

Page({
  data: {
    newsList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.initializePage()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    this.loadMoreData()
  },

  // 初始化页面
  async initializePage() {
    await this.loadNewsList(true)
  },

  // 刷新数据
  async refreshData() {
    this.setData({ 
      page: 1, 
      hasMore: true,
      newsList: []
    })
    
    await this.loadNewsList(true)
    wx.stopPullDownRefresh()
  },

  // 加载更多数据
  async loadMoreData() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({ page: this.data.page + 1 })
    await this.loadNewsList(false)
  },

  // 获取新闻列表
  async loadNewsList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      if (isRefresh) {
        loadingManager.show('加载中...')
      }
      
      const res = await app.request({
        url: '/api/wx/data/notices',
        method: 'GET',
        data: {
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      })
      
      if (res.code === 0) {
        const newList = this.processNewsList(res.data.list || [])
        const hasMore = newList.length === this.data.pageSize
        
        this.setData({
          newsList: isRefresh ? newList : [...this.data.newsList, ...newList],
          hasMore
        })
      } else {
        throw new Error(res.msg || '获取新闻列表失败')
      }
    } catch (error) {
      handleError(error, '获取新闻列表')
      
      // 如果是首次加载失败，显示空状态
      if (isRefresh) {
        this.setData({ newsList: [] })
      }
    } finally {
      this.setData({ loading: false })
      if (isRefresh) {
        loadingManager.hide()
      }
    }
  },

  // 处理新闻列表数据
  processNewsList(list) {
    return list.map(item => ({
      ...item,
      time: formatTime(item.createTime, 'YYYY-MM-DD'),
      summary: this.generateSummary(item.content),
      title: filterHtmlTags(item.title)
    }))
  },

  // 生成摘要
  generateSummary(content) {
    if (!content) return ''
    
    const cleanContent = filterHtmlTags(content)
    return cleanContent.length > 100 
      ? cleanContent.substring(0, 100) + '...' 
      : cleanContent
  },

  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '文章ID无效',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/notice/detail?id=${id}`
    })
  }
})