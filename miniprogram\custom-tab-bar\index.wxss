.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.tab-bar-border {
  display: none;
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 10rpx 0;
}

.tab-bar-item .icon {
  font-size: 44rpx;
  color: #999999;
  margin-bottom: 6rpx;
  position: relative;
  transition: all 0.3s;
}

.tab-bar-item .icon.active {
  color: #07c160;
  transform: scale(1.1);
}

.tab-bar-item .text {
  font-size: 24rpx;
  color: #999999;
  transition: all 0.3s;
}

.tab-bar-item .text.active {
  color: #07c160;
  font-weight: 500;
}

/* 点击反馈 */
.tab-bar-item:active {
  opacity: 0.8;
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';  /* Project id 4812128 */
  src: url('//at.alicdn.com/t/c/font_4812128_q9qns0jhzks.woff2?t=1747539217409') format('woff2'),
  url('//at.alicdn.com/t/c/font_4812128_q9qns0jhzks.woff?t=1747539217409') format('woff'),
  url('//at.alicdn.com/t/c/font_4812128_q9qns0jhzks.ttf?t=1747539217409') format('truetype');
}

.icon {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* tabBar 图标 */
.icon-home:before { content: "\e8b6"; }
.icon-finance:before { content: "\e64a"; }
.icon-auth:before { content: "\e72f"; }
.icon-mine:before { content: "\e600"; }