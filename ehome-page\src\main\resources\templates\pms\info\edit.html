<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改物业信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-config-edit">
            <input name="pms_id" th:value="${pmsInfo.pms_id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">物业名称：</label>
                <div class="col-sm-8">
                    <input id="pms_name" name="pms_name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">办公地址：</label>
                <div class="col-sm-8">
                    <input id="address" name="address" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">物业管理员：</label>
                <div class="col-sm-8">
                    <input id="manager" name="manager" placeholder="管理员账号" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系电话：</label>
                <div class="col-sm-8">
                    <input id="phone" name="phone" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">公司法人：</label>
                <div class="col-sm-8">
                    <input id="legal_person" name="legal_person" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">成立日期：</label>
                <div class="col-sm-8">
                    <input id="establishment_date" name="establishment_date" class="form-control time-input" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">员工数量：</label>
                <div class="col-sm-8">
                    <input id="employee_count" name="employee_count" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">注册号：</label>
                <div class="col-sm-8">
                    <input id="registration_number" name="registration_number" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
	    var prefix = ctx + "pms/info";
	    
	    $("#form-config-edit").validate({
	    	onkeyup: false,
	        rules: {
	            name: {
	                remote: {
	                    url: prefix + "/checkName",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "pms_id": function() {
	                            return $("input[name='pms_id']").val();
	                        },
	                        "name": function() {
	                            return $.common.trim($("#name").val());
	                        }
	                    },
                        dataFilter: function(result) {
                            return result;
                        }
	                }
	            },
	        },
	        messages: {
	            "name": {
                    required: "请输入物业名称",
	                remote: "物业名称已经存在"
	            }
	        },
	        focusCleanup: true
	    });

        $(function(){
            $('#form-config-edit').renderForm({url:prefix+'/record'});
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/editSave", $('#form-config-edit').serialize());
            }
        }
    </script>
</body>
</html>
