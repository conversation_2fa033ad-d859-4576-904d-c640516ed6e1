<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费标准列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- 搜索区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>月份：</label>
                            <input class="time-input" data-type="month" type="text" name="begin_month" placeholder="请输入月份"/>
                            <span>至</span>
                            <input class="time-input" data-type="month" type="text" name="end_month" placeholder="请输入月份"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索
                            </a>
                  
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- 工具栏 -->
        <div class="btn-group-sm" id="toolbar" role="group">
           公示统计数据
        </div>

        <!-- 表格 -->
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/ccbTran";

    $(function() {
        var options = {
            url: prefix + "/monthDataStat",
            modalName: "月度流水",
            columns: [
                {field: 'index', title: '序号', formatter: function(value, row, index) {
                    return index + 1;
                }},
                {field: 'tran_month', title: '月份'},
                {field: 'income', title: '收入'},
                {field: 'expense', title: '支出'},
                {field: 'operate', title: '操作', formatter: function(value, row, index) {
                    return "<a class='btn btn-primary btn-xs btn-edit' onclick='monthDetail(" + row.tran_month + ")'>明细记录</a>";
                }}
            ]
        };
        $.table.init(options);
    });


    function monthDetail(monthId) {
        $.modal.openTab("月度流水明细", ctx + "oc/ccbTran/tranRecord?monthId=" + monthId);
    }

</script>
</body>
</html>