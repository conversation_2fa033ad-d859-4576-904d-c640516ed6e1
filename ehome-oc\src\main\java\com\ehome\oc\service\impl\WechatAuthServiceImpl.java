package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.CacheUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IWechatAuthService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class WechatAuthServiceImpl implements IWechatAuthService {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    private static final String WECHAT_CACHE = "wechat-cache";
    private static final String ACCESS_TOKEN_KEY = "access_token";

    @Value("${wechat.appid}")
    private String appId;

    @Value("${wechat.secret}")
    private String secret;

    @Autowired
    private RestTemplate restTemplate;

    // 微信接口地址
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";
    private static final String PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";

    @Override
    @Deprecated
    public AjaxResult login(String code, Map<String, Object> userInfo, String encryptedData, String iv) {
        logger.info("微信登录: code={}, userInfo={}", code, userInfo);
        try {
            Map<String, Object> wxLoginResult = new HashMap<>();
            String[] openInfo = getOpenId(code);
            String openid = null;
            String sessionKey = null;
            String unionId = null;
            if(openInfo!=null){
                openid = openInfo[0];
                sessionKey = openInfo[1];
                // 尝试从code2session接口获取unionid
                unionId = openInfo[2];
                wxLoginResult.put("openid", openid);
                wxLoginResult.put("session_key",sessionKey);
            }

            // 解密用户信息
            String decryptedData = null;
            if (StringUtils.isNotEmpty(encryptedData) && StringUtils.isNotEmpty(iv)) {
                try {
                    decryptedData = WxCryptUtils.decrypt(sessionKey, encryptedData, iv);
                    // 解析解密后的用户数据
                    JSONObject userInfoJSON = JSON.parseObject(decryptedData);
                    // 从解密数据中获取unionId（如果存在）
                    if (userInfoJSON.containsKey("unionId")) {
                        unionId = userInfoJSON.getString("unionId");
                    }
                    // 更新用户信息
                    userInfo.putAll(userInfoJSON);
                } catch (Exception e) {
                    logger.error("解密用户信息失败", e);
                }
            }

            // 保存用户信息
            WxUser wechatUser = new WxUser();
            wechatUser.setOpenId(openid);
            wechatUser.setNickName((String) userInfo.get("nickName"));
            wechatUser.setAvatarUrl((String) userInfo.get("avatarUrl"));
            wechatUser.setGender((String) userInfo.get("gender"));

            Record wxInfo = wechatUserAdd(wechatUser);
            wechatUser.setUserId(wxInfo.getLong("id"));
            wechatUser.setMobile(wxInfo.getStr("mobile"));
            boolean hasBindPhone = StringUtils.isNotEmpty(wechatUser.getMobile());
            userInfo.put("phoneNumber", wechatUser.getMobile());

            // 返回登录信息
            Map<String, Object> result = new HashMap<>();
            result.put("token", "Bearer " + openid);
            result.put("userInfo", userInfo);
            result.put("mobile", wechatUser.getMobile());
            result.put("userId", wechatUser.getUserId());
            result.put("openId", wechatUser.getOpenId());
            result.put("hasBindPhone", hasBindPhone);
            if (StringUtils.isNotEmpty(unionId)) {
                result.put("unionId", unionId);
            }
            return AjaxResult.success("登录成功", result);
        } catch (Exception e) {
            return AjaxResult.error("登录失败: " + e.getMessage());
        }
    }

    private Record wechatUserAdd(WxUser wechatUser){
        String openid = wechatUser.getOpenId();
        Record record = Db.findFirst("select * from wx_user where openid = ?", openid);
        if(record == null){
            Record record1 = new Record().set("openid", openid);
            record1.set("nickname", wechatUser.getNickName());
            record1.set("avatar_url", wechatUser.getAvatarUrl());
            Db.save("wx_user", "user_id", record1);
        } else {
            Record record2 = new Record().set("openid", openid);
            record2.set("user_id", record.getLong("id"));
            record2.set("nickname", wechatUser.getNickName());
            record2.set("avatar_url", wechatUser.getAvatarUrl());
            Db.update("wx_user", "user_id", record2);
        }
        return Db.findFirst("select * from wx_user where openid = ?", openid);
    }

    @Override
    public AjaxResult decryptPhoneNumber(String code, String phoneCode) {
        try {
            // 1. 获取接口调用凭证access_token
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                return AjaxResult.error("获取access_token失败");
            }

            // 2. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 3. 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", phoneCode);

            // 4. 发送请求获取手机号
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            String url = PHONE_NUMBER_URL + "?access_token=" + accessToken;
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            // 5. 解析响应
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.getInteger("errcode") == 0) {
                JSONObject phoneInfo = jsonResponse.getJSONObject("phone_info");
                String phoneNumber = phoneInfo.getString("phoneNumber");

                Map<String, Object> result = new HashMap<>();
                result.put("phoneNumber", phoneNumber);
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("获取手机号失败: " + jsonResponse.getString("errmsg"));
            }
        } catch (Exception e) {
            return AjaxResult.error("获取手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信接口调用凭证
     */
    private String getAccessToken() {
        try {
            // 先从缓存获取
            String accessToken = (String) CacheUtils.get(WECHAT_CACHE, ACCESS_TOKEN_KEY);
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }

            // 缓存中没有，则请求微信服务器获取
            Map<String, String> params = new HashMap<>();
            params.put("appid", appId);
            params.put("secret", secret);

            ResponseEntity<String> response = restTemplate.getForEntity(
                ACCESS_TOKEN_URL,
                String.class,
                params
            );

            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.containsKey("access_token")) {
                accessToken = jsonResponse.getString("access_token");
                // 存入缓存
                CacheUtils.put(WECHAT_CACHE, ACCESS_TOKEN_KEY, accessToken);
                return accessToken;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public AjaxResult updateUserPhone(String userId,String phoneNumber) {
        try {
            Db.update("update wx_user set mobile = ? where user_id = ?", phoneNumber, userId);
            return AjaxResult.success("更新手机号成功");
        } catch (Exception e) {
            return AjaxResult.error("更新手机号失败: " + e.getMessage());
        }
    }

    @Override
    public AjaxResult getAuthStatus() {
        Map<String, Object> authStatus = new HashMap<>();
        authStatus.put("isHouseAuth", true);
        return AjaxResult.success(authStatus);
    }

    private String[] getOpenId(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        String responseBody = response.getBody();

        if (response.getStatusCode() == HttpStatus.OK && responseBody != null) {
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("openid");
            String sessionKey = jsonObject.getString("session_key");
            String unionId = jsonObject.getString("unionid"); // 尝试获取unionid
            return new String[]{openid, sessionKey, unionId};
        } else {
            return null;
        }
    }

    /**
     * 微信数据解密工具类
     */
    private static class WxCryptUtils {
        public static String decrypt(String sessionKey, String encryptedData, String iv) throws Exception {
            byte[] keyBytes = Base64.getDecoder().decode(sessionKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decrypted = cipher.doFinal(encryptedBytes);
            return new String(decrypted, StandardCharsets.UTF_8);
        }
    }

}