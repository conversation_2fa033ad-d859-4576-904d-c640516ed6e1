<view class="container">
  <view class="detail-card">
    <view class="detail-header">
      <text class="detail-title">{{detail.title}}</text>
      <text class="detail-date">{{detail.date}}</text>
    </view>
    
    <view class="detail-info">
      <view class="info-item">
        <text class="info-label">金额</text>
        <text class="info-value {{detail.type === 'income' ? 'income' : 'expense'}}">
          {{detail.type === 'income' ? '+' : '-'}}¥{{detail.amount}}
        </text>
      </view>
      
      <view class="info-item">
        <text class="info-label">类型</text>
        <text class="info-value">{{detail.categoryName}}</text>
      </view>
      
      <view class="info-item">
        <text class="info-label">经办人</text>
        <text class="info-value">{{detail.operator}}</text>
      </view>
    </view>

    <view class="detail-remark" wx:if="{{detail.remark}}">
      <text class="remark-title">备注</text>
      <text class="remark-content">{{detail.remark}}</text>
    </view>

    <!-- 附件列表 -->
    <view class="attachments" wx:if="{{detail.attachments.length > 0}}">
      <text class="attach-title">附件</text>
      <view class="attach-list">
        <view wx:for="{{detail.attachments}}" 
              wx:key="id" 
              class="attach-item"
              bindtap="viewAttachment"
              data-url="{{item.url}}">
          <text class="attach-icon">
            <text class="iconfont icon-attach"></text>
          </text>
          <text class="attach-name">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 