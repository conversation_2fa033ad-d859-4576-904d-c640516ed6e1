/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.scrollarea {
  flex: 1;
  overflow-y: auto;
}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 社区头部样式 */
.community-header {
  position: relative;
  height: 320rpx;
  width: 100%;
  overflow: hidden;
  flex-shrink: 0;
}

.community-banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.community-info {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.community-name {
  font-size: 40rpx;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.weather-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: #fff;
  font-size: 28rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 用户卡片样式 */
.user-card {
  margin: 24rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  border: 4rpx solid rgba(7,193,96,0.1);
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.05);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0; /* 防止文本溢出 */
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.phone {
  font-size: 28rpx;
  color: #666;
  margin-left:10rpx;
  margin-right:10rpx;
  margin-bottom: 12rpx;
}

.auth-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.status {
  font-size: 24rpx;
  color: #ff9500;
  background: rgba(255,149,0,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.status.auth {
  color: #07c160;
  background: rgba(7,193,96,0.1);
}

.status.1 {
  color: #07c160;
  background: rgba(7,193,96,0.12);
}
.status.2 {
  color: #2b8cff;
  background: rgba(43,140,255,0.12);
}
.status.3 {
  color: #ff9500;
  background: rgba(255,149,0,0.12);
}
.status {
  color: #bbb;
  background: #f0f0f0;
}

.house-info {
  font-size: 24rpx;
  color: #666;
  background: #f7f8fa;
  padding: 16rpx 16rpx;
  border-radius: 20rpx;
}

.auth-btn {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #07c160;
  background: rgba(7,193,96,0.1);
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  font-weight: 500;
}

.auth-btn:active {
  opacity: 0.8;
  transform: translateY(-50%) scale(0.98);
}

/* 功能区域样式 */
.function-grid {
  margin: 0rpx 20rpx;
  padding:20rpx 20rpx;
  background: #fff;
  display: grid;
  border-radius: 22rpx;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.grid-item:active {
  background: #f8f8f8;
  transform: scale(0.98);
}

.grid-item .iconfont {
  font-size: 56rpx;
  margin-bottom: 16rpx;
  color: #07c160;
  width: 96rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  background: rgba(7,193,96,0.1);
  border-radius: 24rpx;
  transition: all 0.3s;
}

.grid-item:active .iconfont {
  transform: scale(0.95);
}

.grid-item text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 社区动态样式 */
.community-news {
  margin: 24rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.06);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-title .more {
  font-size: 26rpx;
  color: #07c160;
  display: flex;
  align-items: center;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.news-item {
  display: flex;
  gap: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.news-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.news-icon {
  width: 88rpx;
  height: 88rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  border-radius: 16rpx;
}

/* 三种不同的图标样式 */
.icon-gonggao {
  color: #07c160;
  background: rgba(7,193,96,0.1);
}

.icon-tongzhi {
  color: #ff9500;
  background: rgba(255,149,0,0.1);
}

.icon-xinwen {
  color: #576b95;
  background: rgba(87,107,149,0.1);
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止文本溢出 */
}

.news-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.news-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.news-time {
  font-size: 24rpx;
  color: #999;
}

.news-type {
  font-size: 24rpx;
  color: #07c160;
  background: rgba(7,193,96,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.news-type.notice {
  color: #ff9500;
  background: rgba(255,149,0,0.1);
}

/* 登录区域样式 */
.login-section {
  margin: 24rpx;
  height: 280rpx;
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.login-section.show {
  display: block;  /* 需要显示时添加show类 */
}

.login-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.4);
  padding: 40rpx;
}

.login-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.login-desc {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.login-btn {
  background: #07c160 !important;
  color: #fff !important;
  font-size: 24rpx;
  padding: 0 60rpx;
  height: 88rpx;
  line-height: 56rpx;
  border-radius: 44rpx;
  font-weight: 500;
  border: none;
}

.login-btn::after {
  border: none;
}

