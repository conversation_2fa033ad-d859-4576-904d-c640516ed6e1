package com.ehome.oc.controller;

import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.domain.Unit;
import com.ehome.oc.service.IUnitService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/oc/unit")
public class UnitController extends BaseController {
    private String prefix = "oc/unit";

    @Autowired
    private IUnitService unitService;

    @GetMapping("/mgr")
    public String mgr() {
        return prefix + "/list";
    }

    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    @GetMapping("/list/{buildingId}")
    public String list(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        mmap.put("buildingId", buildingId);
        return prefix + "/list";
    }

    @GetMapping("/add/{buildingId}")
    public String add(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        mmap.put("buildingId", buildingId);
        return prefix + "/add";
    }

    @GetMapping("/edit/{unitId}")
    public String edit(@PathVariable("unitId") String unitId, ModelMap mmap) {
        Unit unit = unitService.selectUnitById(unitId);
        mmap.put("unit", unit);
        return prefix + "/edit";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Unit unit) {
        startPage();
        List<Unit> list = unitService.selectUnitList(unit);
        return getDataTable(list);
    }

    @Log(title = "单元管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Unit unit) {
        unit.setUnitId(Seq.getId());
        unit.setCreateBy(getSysUser().getLoginName());
        unit.setCreateTime(DateUtils.getTime());
        return toAjax(unitService.insertUnit(unit));
    }

    @Log(title = "单元管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Unit unit) {
        unit.setUpdateBy(getSysUser().getLoginName());
        unit.setUpdateTime(DateUtils.getTime());
        return toAjax(unitService.updateUnit(unit));
    }

    @Log(title = "单元管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(unitService.deleteUnitByIds(ids.split(",")));
    }

    @PostMapping("/queryBuilding")
    @ResponseBody
    public AjaxResult queryBuilding() {
        List<Record> list = Db.find("select building_id, name from eh_building where community_id = ?", getSysUser().getCommunityId());
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("building_id"), record.getStr("name"));
        });
        return AjaxResult.success(map);
    }
} 