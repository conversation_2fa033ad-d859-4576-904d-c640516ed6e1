import { createFeedbackManager } from '../../utils/feedbackManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { FEEDBACK_TYPES } from '../../constants/index.js'

const feedbackManager = createFeedbackManager(FEEDBACK_TYPES.COMPLAINT)
const loadingManager = getLoadingManager()

Page({
  data: {
    typeOptions: [],
    form: {
      type: '',
      content: '',
      mediaUrls: [],
      address: '',
      name: '',
      phone: ''
    },
    loading: false,
    contentError: ''
  },

  onLoad() {
    this.initPage()
  },

  // 初始化页面数据
  initPage() {
    const typeOptions = feedbackManager.getTypeOptions()
    const prefilledForm = feedbackManager.getPrefilledForm()
    
    this.setData({
      typeOptions,
      form: prefilledForm
    })
  },

  // 选择类型
  onTypeChange(e) {
    this.setData({ 
      'form.type': e.currentTarget.dataset.value 
    })
  },

  // 输入内容
  onContentInput(e) {
    const value = e.detail.value
    let contentError = ''
    if (!value || value.trim().length < 5) {
      contentError = '请输入至少5个字符的反馈内容'
    }
    this.setData({ 
      'form.content': value,
      contentError
    })
  },

  // 输入地址
  onAddressInput(e) {
    this.setData({ 
      'form.address': e.detail.value 
    })
  },

  // 输入姓名
  onNameInput(e) {
    this.setData({ 
      'form.name': e.detail.value 
    })
  },

  // 输入电话
  onPhoneInput(e) {
    this.setData({ 
      'form.phone': e.detail.value 
    })
  },

  // 选择媒体文件
  async chooseMedia() {
    try {
      const res = await this.chooseImage()
      const tempFilePaths = res.tempFilePaths
      
      loadingManager.show('上传中...')
      
      // 批量上传
      const uploadPromises = tempFilePaths.map(path => 
        feedbackManager.uploadMedia(path)
      )
      
      const uploadResults = await Promise.allSettled(uploadPromises)
      const successUploads = uploadResults
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value)
      
      if (successUploads.length > 0) {
        this.setData({
          'form.mediaUrls': this.data.form.mediaUrls.concat(successUploads)
        })
        
        if (successUploads.length < tempFilePaths.length) {
          wx.showToast({
            title: `${successUploads.length}/${tempFilePaths.length} 文件上传成功`,
            icon: 'none'
          })
        }
      } else {
        throw new Error('所有文件上传失败')
      }
      
    } catch (error) {
      handleError(error, '文件上传')
    } finally {
      loadingManager.hide()
    }
  },

  // 选择图片的Promise封装
  chooseImage() {
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: 3,
        success: resolve,
        fail: reject
      })
    })
  },

  // 提交表单
  async onSubmit() {
    if (this.data.loading) return

    const validation = feedbackManager.validateForm(this.data.form)
    if (!validation.valid) {
      this.setData({ contentError: validation.message })
      wx.showToast({
        title: validation.message,
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    loadingManager.show('提交中...')
    try {
      const result = await feedbackManager.submitFeedback(this.data.form)
      if (result.success) {
        wx.showToast({ 
          title: '提交成功', 
          icon: 'success' 
        })
        setTimeout(() => {
          this.goToHistory()
        }, 1500)
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      handleError(error, '提交投诉建议')
    } finally {
      this.setData({ loading: false })
      loadingManager.hide()
    }
  },

  // 跳转到历史记录
  goToHistory() {
    wx.navigateTo({ 
      url: '/pages/complaint/history' 
    })
  }
})