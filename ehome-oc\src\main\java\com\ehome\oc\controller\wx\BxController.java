package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;

@RestController
@RequestMapping("/api/wx/bx")
public class BxController extends BaseWxController {

    @Value("${ruoyi.profile}")
    private String profilePath;

    @PostMapping("/addData")
    public AjaxResult addData() {
        try {
            Long wxUserId = getCurrentUserId();
            JSONObject params = getParams();
            Record record = new Record();
            record.set("type",params.getString("type"));
            record.set("content",params.getString("content"));
            record.set("address",params.getString("address"));
            record.set("name",params.getString("name"));
            record.set("phone",params.getString("phone"));
            record.set("id", Seq.getId());
            record.set("community_id", getCurrentUser().getCommunityId());
            record.set("wx_user_id", wxUserId);
            record.set("create_time", new Date());
            record.set("update_time", new Date());
            record.set("create_by", getCurrentUser().getMobile());
            Db.save("eh_wx_bx", "id", record);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("新增失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("文件为空");
        }
        try {
            String originalFilename = file.getOriginalFilename();
            String ext = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                ext = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = System.currentTimeMillis() + "_" + (int)(Math.random()*1000) + ext;
            // 物理保存路径
            String uploadDir = profilePath + File.separator + "bx";
            File dir = new File(uploadDir);
            if (!dir.exists()) dir.mkdirs();
            File dest = new File(dir, fileName);

            // 图片压缩（仅处理jpg/png，其他类型直接保存）
            if (ext.equalsIgnoreCase(".jpg") || ext.equalsIgnoreCase(".jpeg") || ext.equalsIgnoreCase(".png")) {
                BufferedImage srcImg = ImageIO.read(file.getInputStream());
                int targetW = srcImg.getWidth();
                int targetH = srcImg.getHeight();
                int maxSide = Math.max(targetW, targetH);
                if (maxSide > 1280) {
                    double scale = 1280.0 / maxSide;
                    targetW = (int)(srcImg.getWidth() * scale);
                    targetH = (int)(srcImg.getHeight() * scale);
                }
                BufferedImage outImg = new BufferedImage(targetW, targetH, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = outImg.createGraphics();
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.drawImage(srcImg, 0, 0, targetW, targetH, null);
                g2d.dispose();
                try (OutputStream os = new FileOutputStream(dest)) {
                    ImageIO.write(outImg, ext.replace(".", "").toLowerCase(), os);
                }
            } else {
                file.transferTo(dest);
            }
            // 返回URL（/profile/bx/xxx.jpg）
            String fileUrl = "/profile/bx/" + fileName;
            return AjaxResult.success(fileUrl);
        } catch (Exception e) {
            return AjaxResult.error("上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/history")
    public AjaxResult history() {
        try {
            Long wxUserId = getCurrentUserId();
            List<Record> list = Db.find("select id, type, content, create_time, status, media_urls from eh_wx_bx where wx_user_id = ? order by create_time desc", wxUserId);
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record r : list) {
                Map<String, Object> m = new HashMap<>();
                m.put("id", r.get("id"));
                m.put("type", r.get("type"));
                m.put("content", r.get("content"));
                m.put("createTime", r.get("create_time"));
                m.put("status", r.get("status"));
                m.put("media_urls", r.get("media_urls"));
                result.add(m);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取历史失败: " + e.getMessage());
        }
    }
}
