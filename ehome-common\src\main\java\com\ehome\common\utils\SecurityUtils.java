package com.ehome.common.utils;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.exception.ServiceException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.HashMap;
import java.util.Map;

public class SecurityUtils {
    
    /**
     * 密钥
     */
    private static final String SECRET = "ehomeSecretKey";
    
    /**
     * token有效期（1小时）
     */
    private static final long EXPIRE_TIME = 24 * 60 * 60 * 1000;
    
    /**
     * Bearer前缀
     */
    private static final String TOKEN_PREFIX = "Bearer ";
    
    /**
     * 从token中获取用户信息
     */
    public static LoginUser getLoginUser(String token) {
        try {
            // 如果token以Bearer 开头，去掉这个前缀
            if (token != null && token.startsWith(TOKEN_PREFIX)) {
                token = token.substring(TOKEN_PREFIX.length());
            }

            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET)
                    .parseClaimsJws(token)
                    .getBody();
            
            LoginUser loginUser = new LoginUser();
            
            // 获取并安全处理claims中的值
            Object userId = claims.get("userId");
            if (userId != null) {
                loginUser.setUserId(Long.valueOf(userId.toString()));
            }

            Object openid = claims.get("openId");
            if (openid != null) {
                loginUser.setOpenId(openid.toString());
            }

            Object communityId = claims.get("communityId");
            if (communityId != null) {
                loginUser.setCommunityId(communityId.toString());
            }

            Object username = claims.get("username");
            if (username != null) {
                loginUser.setUsername(username.toString());
            }
            
            Object mobile = claims.get("mobile");
            if (mobile != null) {
                loginUser.setMobile(mobile.toString());
            }
            
            Object userType = claims.get("userType");
            if (userType != null) {
                loginUser.setUserType(userType.toString());
            }
            
            loginUser.setToken(token);
            
            return loginUser;
        } catch (Exception e) {
            throw new ServiceException("token解析失败");
        }
    }
    
    /**
     * 生成token
     */
    public static String createToken(LoginUser loginUser) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", loginUser.getUserId());
        claims.put("openId", loginUser.getOpenId());
        claims.put("username", loginUser.getUsername());
        claims.put("mobile", loginUser.getMobile());
        claims.put("userType", loginUser.getUserType());
        claims.put("communityId", loginUser.getCommunityId());

        return Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .setExpiration(new java.util.Date(System.currentTimeMillis() + EXPIRE_TIME))
                .compact();
    }
    
    /**
     * 验证token是否有效
     */
    public static boolean validateToken(String token) {
        try {
            // 如果token以Bearer 开头，去掉这个前缀
            if (token != null && token.startsWith(TOKEN_PREFIX)) {
                token = token.substring(TOKEN_PREFIX.length());
            }
            
            Jwts.parser().setSigningKey(SECRET).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 