<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费标准列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <!-- 搜索区域 -->
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>收费名称：</label>
                                <input type="text" name="feeName"/>
                            </li>
                            <li>
                                <label>收费类型：</label>
                                <select name="feeType">
                                    <option value="">所有</option>
                                    <option value="临时性收费">临时性收费</option>
                                    <option value="周期性收费">周期性收费</option>
                                </select>
                            </li>
                            <li>
                                <label>收费方式：</label>
                                <select name="collectionMethod">
                                    <option value="">所有</option>
                                    <option value="一次性收取">一次性收取</option>
                                    <option value="按月生成账单">按月生成账单</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                    <i class="fa fa-search"></i>&nbsp;搜索
                                </a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                    <i class="fa fa-refresh"></i>&nbsp;重置
                                </a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <!-- 工具栏 -->
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <!-- 表格 -->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/fee";
        
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "收费标准",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'fee_name',
                    title: '收费名称'
                },
                {
                    field: 'fee_type',
                    title: '收费类型'
                },
                {
                    field: 'precision_type',
                    title: '计费精度'
                },
                {
                    field: 'collection_method',
                    title: '收费方式'
                },
                {
                    field: 'calculation_method',
                    title: '金额计算方式'
                },
                {
                    field: 'unit_price',
                    title: '单价'
                },
                {
                    field: 'bill_generation_day',
                    title: '账单生成日'
                },
                {
                    field: 'is_active',
                    title: '状态',
                    formatter: function(value) {
                        return value ? '启用' : '禁用';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html> 