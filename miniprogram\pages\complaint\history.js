const app = getApp();
Page({
  data: {
    list: [],
    loading: true
  },
  onLoad() {
    this.getHistory();
  },
  async getHistory() {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/complaint/history',
        method: 'POST'
      });
      if (res.code === 0) {
        const statusMap = { 0: '待处理', 1: '处理中', 2: '已完成' };
        const list = (res.data || []).map(item => {
          let reply_content = '';
          if (item.status == 2) {
            reply_content = item.reply_content || '';
          }
          return {
            ...item,
            mediaUrls: item.media_urls ? JSON.parse(item.media_urls) : [],
            statusText: statusMap[item.status] || '待处理',
            reply_content
          };
        });
        this.setData({ list, loading: false });
      } else {
        this.setData({ list: [], loading: false });
      }
    } catch (e) {
      this.setData({ list: [], loading: false });
    }
  },
  onPullDownRefresh() {
    this.getHistory().then(() => wx.stopPullDownRefresh());
  }
}); 