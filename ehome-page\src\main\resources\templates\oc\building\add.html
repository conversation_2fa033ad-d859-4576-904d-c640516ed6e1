<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增楼栋信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-building-add">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼栋序号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="1,2,3,A,B,C" name="name" required>
                </div>
                <label class="col-sm-2 control-label">排序：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" placeholder="排序" name="order_index">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">房屋面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" placeholder="平方米" name="house_area">
                </div>
                <label class="col-sm-2 control-label">楼栋管家：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="请输入楼栋管家" name="manager">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/building";

        $("#form-building-add").validate({
            onkeyup: false,
            rules: {
                name: {
                    remote: {
                        url: prefix + "/checkName",
                        type: "post",
                        dataType: "json",
                        data: {
                            "name": function() {
                                return $.common.trim($("input[name='name']").val());
                            }
                        },
                        dataFilter: function(result) {
                            return result;
                        }
                    }
                },
            },
            messages: {
                "name": {
                    required: "请输入楼栋名称",
                    remote: "楼栋名称已经存在"
                }
            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                if($("input[name='house_area']").val() == ""){
                    $("input[name='house_area']").val(0);
                }
                var formData = $('#form-building-add').serialize();
                $.operate.save(prefix + "/addData", formData);
            }
        }

        var getCommunityName = function(obj){
            var community_name = $(obj).find("option:selected").text();
            $("input[name='community_name']").val(community_name);
        }
    </script>
</body>
</html> 