import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { formatTime, filterHtmlTags } from '../../utils/common.js'

const app = getApp()
const loadingManager = getLoadingManager()

Page({
  data: {
    detail: null,
    loading: true
  },

  onLoad(options) {
    const { id } = options
    if (!id) {
      this.handleInvalidId()
      return
    }
    this.initializePage(id)
  },

  onShow() {
    // 这里可以添加页面显示时的逻辑
  },

  // 初始化页面
  async initializePage(id) {
    await this.getNoticeDetail(id)
  },

  // 处理无效ID
  handleInvalidId() {
    wx.showModal({
      title: '提示',
      content: '文章参数无效',
      showCancel: false,
      success: () => {
        wx.navigateBack()
      }
    })
  },

  // 获取新闻详情
  async getNoticeDetail(id) {
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: `/api/wx/data/notice/detail/${id}`,
        method: 'GET'
      })
      
      if (res.code === 0 && res.data) {
        const detail = this.processDetailData(res.data)
        this.setData({ detail })
        
        // 设置页面标题
        if (detail.title) {
          wx.setNavigationBarTitle({
            title: detail.title.length > 10 
              ? detail.title.substring(0, 10) + '...' 
              : detail.title
          })
        }
      } else {
        throw new Error(res.msg || '获取文章详情失败')
      }
    } catch (error) {
      handleError(error, '获取文章详情')
      
      // 显示错误页面或返回上一页
      this.showErrorState()
    } finally {
      this.setData({ loading: false })
      loadingManager.hide()
    }
  },

  // 处理详情数据
  processDetailData(data) {
    return {
      ...data,
      date: data.date ? formatTime(data.date, 'YYYY-MM-DD HH:mm') : null,
      createTime: formatTime(data.createTime, 'YYYY-MM-DD HH:mm'),
      updateTime: data.updateTime ? formatTime(data.updateTime, 'YYYY-MM-DD HH:mm') : null,
      title: filterHtmlTags(data.title),
      // content保持HTML格式用于rich-text显示
      attachments: data.attachments || []
    }
  },

  // 显示错误状态
  showErrorState() {
    wx.showModal({
      title: '加载失败',
      content: '文章加载失败，是否重试？',
      confirmText: '重试',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 重新加载
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const options = currentPage.options
          if (options.id) {
            this.initializePage(options.id)
          }
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  // 查看附件
  async viewAttachment(e) {
    const url = e.currentTarget.dataset.url
    const name = e.currentTarget.dataset.name
    
    if (!url) {
      wx.showToast({
        title: '附件链接无效',
        icon: 'none'
      })
      return
    }

    try {
      loadingManager.show('下载中...')
      
      const res = await this.downloadFile(url)
      await this.openDocument(res.tempFilePath, name)
      
    } catch (error) {
      handleError(error, '附件下载')
    } finally {
      loadingManager.hide()
    }
  },

  // 下载文件
  downloadFile(url) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: resolve,
        fail: reject
      })
    })
  },

  // 打开文档
  openDocument(filePath, fileName) {
    return new Promise((resolve, reject) => {
      wx.openDocument({
        filePath: filePath,
        fileName: fileName,
        success: resolve,
        fail: reject
      })
    })
  },

  // 分享功能
  onShareAppMessage() {
    const detail = this.data.detail
    if (!detail) {
      return {
        title: '智慧小区',
        path: '/pages/index/index'
      }
    }

    return {
      title: detail.title || '智慧小区公告',
      path: `/pages/notice/detail?id=${detail.id}`,
      imageUrl: detail.coverImage || ''
    }
  }
})