const app = getApp()

Page({
  data: {
    // 直接用后端返回的所有字段，初始为空
  },

  onLoad(options) {
    if (!this.checkLoginStatus()) return
    if (options.id) {
      this.getHouseDetail(options.id)
    }
  },

  // 获取房屋详情
  async getHouseDetail(id) {
    try {
      wx.showLoading({ 
        title: '加载中',
        mask: true
      })
      const res = await app.request({
        url: '/api/wx/house/detail',
        method: 'GET',
        data: { houseId: id }
      })
      if (res.code === 0) {
        // 直接setData为后端data，字段名保持一致
        this.setData(res.data || {})
      } else {
        wx.showToast({
          title: res.msg || '获取房屋信息失败',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      wx.showToast({
        title: '获取房屋信息失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      wx.hideLoading()
    }
  }
})