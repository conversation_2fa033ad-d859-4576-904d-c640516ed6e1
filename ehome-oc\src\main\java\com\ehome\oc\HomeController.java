package com.ehome.oc;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/oc/home")
public class HomeController extends BaseController {

    /**
     * 首页统计数据和公告
     */
    @PostMapping("/summary")
    @ResponseBody
    public AjaxResult summary() {
        Map<String, Object> data = new HashMap<>();
        // 业主总数
        Long ownerCount = Db.queryLong("SELECT COUNT(*) FROM eh_owner where community_id = ?", getSysUser().getCommunityId());
        // 房屋总数
        Long houseCount = Db.queryLong("SELECT COUNT(*) FROM eh_house_info where community_id = ?", getSysUser().getCommunityId());
        // 车辆总数
        Long vehicleCount = Db.queryLong("SELECT COUNT(*) FROM eh_vehicle where community_id = ?", getSysUser().getCommunityId());
        // 最新公告（取5条）
        List<Record> announcementList = Db.find("SELECT notice_id id,notice_title title,create_time publish_date FROM sys_notice where community_id = ? and status = '0' ORDER BY create_time DESC LIMIT 8",getSysUser().getCommunityId());

        data.put("ownerCount", ownerCount);
        data.put("houseCount", houseCount);
        data.put("vehicleCount", vehicleCount);
        data.put("announcements",  getDataList(announcementList).getRows());

        return AjaxResult.success(data);
    }
}