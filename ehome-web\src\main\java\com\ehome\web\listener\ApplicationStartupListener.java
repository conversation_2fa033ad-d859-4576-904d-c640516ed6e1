package com.ehome.web.listener;

import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();
        
        System.out.println("\n----------------------------------------------------------");
        System.out.println("  应用启动成功!");
        System.out.println("----------------------------------------------------------");
        System.out.println("  系统信息:");
        System.out.println("  spring.profiles.active: \t" + env.getProperty("spring.profiles.active"));
        System.out.println("  server.port: \t" + env.getProperty("server.port"));
        System.out.println("  server.servlet.context-path: \t" + env.getProperty("server.servlet.context-path"));
        
        System.out.println("\n  应用信息:");
        System.out.println("  ruoyi.name: \t" + env.getProperty("ruoyi.name"));
        System.out.println("  ruoyi.version: \t" + env.getProperty("ruoyi.version"));
        System.out.println("  ruoyi.copyrightYear: \t" + env.getProperty("ruoyi.copyrightYear"));
        System.out.println("  ruoyi.profile: \t" + env.getProperty("ruoyi.profile"));
        
        System.out.println("\n  数据源配置:");
        System.out.println("  spring.datasource.druid.master.url: \t" + env.getProperty("spring.datasource.druid.master.url"));
        System.out.println("  spring.datasource.druid.master.username: \t" + env.getProperty("spring.datasource.druid.master.username"));
        System.out.println("  spring.datasource.druid.initialSize: \t" + env.getProperty("spring.datasource.druid.initialSize"));
        System.out.println("  spring.datasource.druid.minIdle: \t" + env.getProperty("spring.datasource.druid.minIdle"));
        System.out.println("  spring.datasource.druid.maxActive: \t" + env.getProperty("spring.datasource.druid.maxActive"));
        
        System.out.println("\n  Tomcat配置:");
        System.out.println("  server.tomcat.threads.max: \t" + env.getProperty("server.tomcat.threads.max"));
        System.out.println("  server.tomcat.threads.min-spare: \t" + env.getProperty("server.tomcat.threads.min-spare"));
        System.out.println("  server.tomcat.uri-encoding: \t" + env.getProperty("server.tomcat.uri-encoding"));
        System.out.println("  server.tomcat.basedir: \t" + env.getProperty("server.tomcat.basedir"));
        System.out.println("  server.tomcat.accesslog.enabled: \t" + env.getProperty("server.tomcat.accesslog.enabled"));
        System.out.println("  server.tomcat.accesslog.directory: \t" + env.getProperty("server.tomcat.accesslog.directory"));
        
        System.out.println("\n  系统配置:");
        System.out.println("  ruoyi.demoEnabled: \t" + env.getProperty("ruoyi.demoEnabled"));
        System.out.println("  shiro.user.captchaEnabled: \t" + env.getProperty("shiro.user.captchaEnabled"));
        System.out.println("  shiro.user.captchaType: \t" + env.getProperty("shiro.user.captchaType"));
        System.out.println("  shiro.session.expireTime: \t" + env.getProperty("shiro.session.expireTime") + "分钟");
        System.out.println("  shiro.rememberMe.enabled: \t" + env.getProperty("shiro.rememberMe.enabled"));
        
        System.out.println("\n  文件上传限制:");
        System.out.println("  spring.servlet.multipart.max-file-size: \t" + env.getProperty("spring.servlet.multipart.max-file-size"));
        System.out.println("  spring.servlet.multipart.max-request-size: \t" + env.getProperty("spring.servlet.multipart.max-request-size"));
        
        System.out.println("\n  日志配置:");
        String logPath = env.getProperty("logging.file.path");
        if (logPath == null) {
            logPath = env.getProperty("logging.path");
        }
        if (logPath == null) {
            logPath = env.getProperty("log.path");
        }
        System.out.println("  logging.file.path: \t" + logPath);
        System.out.println("  logging.level.com.ehome: \t" + env.getProperty("logging.level.com.ehome"));
        System.out.println("  logging.level.org.springframework: \t" + env.getProperty("logging.level.org.springframework"));
        
        System.out.println("\n  其他配置:");
        System.out.println("  spring.devtools.restart.enabled: \t" + env.getProperty("spring.devtools.restart.enabled"));
        System.out.println("  swagger.enabled: \t" + env.getProperty("swagger.enabled"));
        System.out.println("  xss.enabled: \t" + env.getProperty("xss.enabled"));
        System.out.println("----------------------------------------------------------\n");
    }
} 