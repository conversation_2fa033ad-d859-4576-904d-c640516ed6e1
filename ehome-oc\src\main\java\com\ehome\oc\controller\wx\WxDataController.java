package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.cache.ConcurrentHashMapCache;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.domain.OwnerInfo;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.IWechatAuthService;
import com.ehome.oc.service.IWxUserService;
import com.ehome.system.domain.SysNotice;
import com.ehome.system.service.ISysNoticeService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/wx/data")
public class WxDataController extends BaseWxController {
    @Autowired
    private IWechatAuthService wechatAuthService;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private IHouseInfoService houseInfoService;

    @Autowired
    private ConcurrentHashMapCache cache;


    @RequestMapping("/status")
    public AjaxResult status() {
        JSONObject params =  getParams();
        Map<String, Object> data = new HashMap<>();
        AjaxResult result = new AjaxResult();
        try {
           logger.info("获取认证状态参数: " + params);
           Long userId = getCurrentUserId();
           String openId =  getCurrentUser().getOpenId();;
           String mobile = getCurrentUser().getMobile();;

           OwnerInfo ownerInfo = new OwnerInfo();
           List<HouseInfo> houseList = new ArrayList<HouseInfo>();

           List<Record> houseRecordList = Db.find("SELECT t1.*,t2.house_info,t2.owner_id,t2.owner_name,t2.role,t2.mobile from eh_house_info t1,eh_owner t2 ,eh_house_owner_rel t3 where t1.house_id = t3.house_id and  t2.owner_id = t3.owner_id and t2.mobile = ?",mobile);
           if(houseRecordList!=null){
                for(Record record:houseRecordList){
                    HouseInfo house = houseInfoService.recordToObj(record);
                    houseList.add(house);
                    ownerInfo.setOwnerId(record.getStr("owner_id"));
                    ownerInfo.setOwnerName(record.getStr("owner_name"));
                    ownerInfo.setCommunityId(record.getStr("community_id"));
                    ownerInfo.setMobile(record.getStr("mobile"));
                    ownerInfo.setRole(record.getStr("role"));
                    ownerInfo.setHouseStr(record.getStr("house_info"));
                }
           }

            // 查找默认房屋
            HouseInfo defaultHouse = null;
            boolean isHouseAuth = false;

            if (houseList != null && !houseList.isEmpty()) {
                isHouseAuth = true;
                for (HouseInfo house : houseList) {
                    if (house.getIsDefault() != null && house.getIsDefault() == 1) {
                        defaultHouse = house;
                        break;
                    }
                }
                if(defaultHouse==null){
                    defaultHouse = houseList.get(0);
                }
            }

            JSONObject communityInfo =  cache.get("communityInfo"+ownerInfo.getCommunityId(), JSONObject.class);
            if(communityInfo==null){
               communityInfo = new JSONObject();
               Record communityRecord = Db.findFirst("select * from eh_community where oc_id = ?",ownerInfo.getCommunityId());
               String communitName = communityRecord.getStr("oc_name");
               ownerInfo.setCommunityName(communitName);
               communityInfo.put("address", communityRecord.get("oc_address"));
               communityInfo.put("servicePhone", communityRecord.get("service_phone"));
               communityInfo.put("communityId", ownerInfo.getCommunityId());
               communityInfo.put("communityName", ownerInfo.getCommunityName());
               communityInfo.put("communityBanner","/static/images/banner.png");
               cache.put("communityInfo"+ownerInfo.getCommunityId(), communityInfo);
            }


            data.put("ownerInfo", ownerInfo);
            data.put("communityInfo", communityInfo);
            data.put("isHouseAuth", isHouseAuth);
            data.put("houseInfo", defaultHouse);
            data.put("tokenUser",getCurrentUser());
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取认证状态失败: " + e.getMessage(), e);
            return AjaxResult.error("获取认证状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public AjaxResult getUserInfo() {
        try {
            LoginUser loginUser = getCurrentUser();
            // 获取最新的用户信息
            WxUser wxUser = wxUserService.selectWxUserById(loginUser.getUserId());
            if (wxUser == null) {
                return AjaxResult.error("用户不存在");
            }
            Map<String, Object> data = new HashMap<>();
            data.put("userId", wxUser.getUserId());
            data.put("nickName", wxUser.getNickName());
            data.put("avatarUrl", wxUser.getAvatarUrl());
            data.put("mobile", wxUser.getMobile());
            data.put("ownerInfo",new HashMap<>());
            if(wxUser.getMobile()!=null){
                Record ownerInfo = Db.findFirst("select * from eh_owner where mobile = ?", wxUser.getMobile());
                if(ownerInfo!=null){
                    data.put("ownerInfo",ownerInfo.toMap());
                }
            }
            return AjaxResult.success(data);
        } catch (Exception e) {
            return AjaxResult.error("获取用户信息失败: " + e.getMessage());
        }
    }



    @GetMapping("/weather/current")
    public AjaxResult getWeather() {
        try {
            HashMap<Object, Object> weather = new HashMap<Object, Object>();
            weather.put("temp", "25");
            weather.put("text", "小雨");
            return AjaxResult.success(weather);
        } catch (Exception e) {
            logger.error("获取天气失败: " + e.getMessage(),e);
            return AjaxResult.error("获取天气失败: " + e.getMessage());
        }
    }

    @GetMapping("/notices")
    public AjaxResult getNotices() {
        try {
            SysNotice notice = new SysNotice();
            List<SysNotice> notices = noticeService.selectNoticeList(notice);

            // 转换为前端需要的格式
            List<Map<String, Object>> noticeList = new ArrayList<>();
            for (SysNotice item : notices) {
                Map<String, Object> noticeMap = new HashMap<>();
                noticeMap.put("id", item.getNoticeId());
                noticeMap.put("title", item.getNoticeTitle());
                noticeMap.put("content", item.getNoticeContent());
                noticeMap.put("type", item.getNoticeType());
                noticeMap.put("createTime", item.getCreateTime());
                noticeList.add(noticeMap);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", noticeList);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取公告列表失败: " + e.getMessage(),e);
            return AjaxResult.error("获取公告列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/notice/detail/{id}")
    public AjaxResult getNoticeDetail(@PathVariable("id") Long id) {
        try {
            SysNotice notice = noticeService.selectNoticeById(id);
            if (notice == null) {
                return AjaxResult.error("公告不存在");
            }
            Map<String, Object> result = new HashMap<>();
            result.put("id", notice.getNoticeId());
            result.put("title", notice.getNoticeTitle());
            result.put("content", notice.getNoticeContent());
            result.put("type", notice.getNoticeType());
            result.put("date", notice.getUpdateTime());
            result.put("createTime", notice.getCreateTime());
            return AjaxResult.success(result);
        }catch (Exception e) {
            logger.error("获取公告详情失败: " + e.getMessage(),e);
            return AjaxResult.error("获取公告详情失败: " + e.getMessage());
        }
    }

}

