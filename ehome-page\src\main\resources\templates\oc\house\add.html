<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增房屋')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-house-add">           
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼栋：</label>
                <div class="col-sm-4">
                    <select name="building_id" class="form-control" data-url="/queryBuilding" onchange="queryUnit(this.value);" required>
                        <option value="">请选择楼栋</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label is-required">单元：</label>
                <div class="col-sm-4">
                    <select name="unit_id" class="form-control" data-url="/queryUnit" required>
                        <option value="">请选择单元</option>
                    </select>
                </div>
            </div>
            <div class="form-group">               
                <label class="col-sm-2 control-label is-required">房间号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="room" required>
                </div>
                <label class="col-sm-2 control-label">房屋状态：</label>
                <div class="col-sm-4">
                    <select name="house_status" class="form-control">
                        <option value="">请选择房屋状态</option> 
                        <option value="2002">未销售</option> 
                        <option value="2001">已入住</option>
                        <option value="2003">已交房</option>
                        <option value="2005">已装修</option> 
                        <option value="2004">未入住</option>
                        <option value="2009">装修中</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼层：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="floor" required>
                </div>
                <label class="col-sm-2 control-label">房屋类型：</label>
                <div class="col-sm-4">
                    <select name="house_type" class="form-control">
                        <option value="">请选择房屋类型</option>
                        <option value="1">住宅</option>
                        <option value="2">商铺</option>
                    </select>
                </div>              
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">建筑面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" name="total_area" step="0.01" required>
                </div>
                <label class="col-sm-2 control-label is-required">室内面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" name="use_area" step="0.01" required>
                </div>
            </div>    
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/house";
        
        $("#form-house-add").validate({
            focusCleanup: true
        });

        $(function() {
            $('#form-house-add').renderSelect({prefix:prefix});
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-house-add').serialize());
            }
        }

        function queryUnit(buildingId) {
            $("select[name='unit_id']").renderSelect({url:prefix+'/queryUnit',data:{buildingId:buildingId}}, function() {

            });
        }

    </script>
</body>
</html> 