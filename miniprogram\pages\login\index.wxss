/* 登录页面样式 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  box-sizing: border-box;
  background: #fff;
}

.logo {
  margin-top: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-img {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 24rpx;
}

.logo-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.content {
  width: 100%;
  margin-top: 120rpx;
}

.login-section, .phone-section {
  width: 100%;
}

.login-btn, .phone-btn {
  width: 100% !important;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  margin-bottom: 32rpx;
  padding: 0;
  border: none;
}

.login-btn::after, .phone-btn::after {
  display: none;
}

.tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

.link {
  color: #07c160;
  display: inline;
}

/* 手机号绑定弹窗样式 */
.phone-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 560rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 32rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  padding: 48rpx 32rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.modal-phone-btn {
  width: 100% !important;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  padding: 0;
  border: none;
}

.modal-phone-btn::after {
  display: none;
}

.icon-wechat:before { content: "\e883"; } 