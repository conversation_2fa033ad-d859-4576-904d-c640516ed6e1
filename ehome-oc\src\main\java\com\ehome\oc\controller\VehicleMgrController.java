package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/oc/vehicle")
public class VehicleMgrController extends BaseController {

    private static final String PREFIX = "oc/vehicle";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select t1.*, t2.owner_name, t2.mobile",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String vehicleId = params.getString("vehicle_id");
        if (StringUtils.isEmpty(vehicleId)) {
            return AjaxResult.error("车辆ID不能为空");
        }
        Record vehicle = Db.findFirst("select * from eh_vehicle where vehicle_id = ?", vehicleId);
        return AjaxResult.success(null, vehicle.toMap());
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{vehicleId}")
    public String edit(@PathVariable("vehicleId") String vehicleId, ModelMap mmap) {
        Record vehicle = Db.findFirst("select * from eh_vehicle where vehicle_id = ?", vehicleId);
        mmap.put("vehicle", vehicle.toMap());
        return PREFIX + "/edit";
    }

    @Log(title = "新增车辆", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        Record vehicle = new Record();
        vehicle.setColumns(params);
        vehicle.set("vehicle_id", Seq.getId());
        setCreateAndUpdateInfo(vehicle);
        Db.save("eh_vehicle", "vehicle_id", vehicle);
        return AjaxResult.success();
    }

    @Log(title = "修改车辆", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record vehicle = new Record();
        vehicle.setColumns(params);
        setUpdateInfo(vehicle);
        return toAjax(Db.update("eh_vehicle", "vehicle_id", vehicle));
    }

    @Log(title = "删除车辆", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById("eh_vehicle", "vehicle_id", id);
        }
        return success();
    }
    
    /**
     * 业主选择对话框
     */
    @GetMapping("/ownerDialog")
    public String ownerDialog() {
        return PREFIX + "/ownerDialog";
    }
    
    /**
     * 查询业主列表
     */
    @PostMapping("/ownerList")
    @ResponseBody
    public TableDataInfo ownerList() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_owner where 1=1");
        sql.append(params.getString("community_id"), "and community_id = ?");
        sql.appendLike(params.getString("owner_name"), "and owner_name like ?");
        sql.appendLike(params.getString("mobile"), "and mobile like ?");
        sql.append(params.getString("user_type"), "and user_type = ?");
        sql.append("order by create_time desc");
        
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }
    
    /**
     * 停车位选择对话框
     */
    @GetMapping("/parkingSpaceDialog")
    public String parkingSpaceDialog() {
        return PREFIX + "/parkingSpaceDialog";
    }
    
    /**
     * 查询停车位列表
     */
    @PostMapping("/parkingSpaceList")
    @ResponseBody
    public TableDataInfo parkingSpaceList() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_parking_space where 1=1");
        sql.append(params.getString("community_id"), "and community_id = ?");
        sql.appendLike(params.getString("parking_code"), "and parking_code like ?");
        sql.append(params.getString("parking_type"), "and parking_type = ?");
        sql.append(params.getString("status"), "and status = ?");
        sql.append("order by create_time desc");
        
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_vehicle t1");
        sql.append("left join eh_vehicle_owner_rel r on t1.vehicle_id = r.vehicle_id and r.is_default = 1");
        sql.append("left join eh_owner t2 on r.owner_id = t2.owner_id");
        sql.append("where 1=1");
        sql.append(params.getString("community_id"), "and t1.community_id = ?");
        sql.appendLike(params.getString("plate_no"), "and t1.plate_no like ?");
        sql.append(params.getString("vehicle_type"), "and t1.vehicle_type = ?");
        sql.append(params.getString("check_status"), "and t1.check_status = ?");
        sql.append("order by t1.create_time desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
}