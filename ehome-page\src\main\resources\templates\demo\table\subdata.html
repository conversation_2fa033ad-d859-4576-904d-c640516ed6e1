<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('主子表提交')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style type="text/css">
        table label.error{position: inherit;}select + label.error{z-index:1;right:40px;}
    </style>
</head>
<body class="gray-bg">
    <div class="main-content">
        <form id="form-add" class="form-horizontal">
            <h4 class="form-header h4">客户信息</h4>
            <div class="row">
            	<div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">客户名称：</label>
                        <div class="col-sm-8">
                            <input name="name" placeholder="请输入客户名称" class="form-control" type="text" maxlength="30" required>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">用户性别：</label>
                        <div class="col-sm-8">
                            <select name="sex" class="form-control" th:with="type=${@dict.getType('sys_user_sex')}">
				                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
				            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">手机号码：</label>
                        <div class="col-sm-8">
                            <input id="phonenumber" name="phonenumber" placeholder="请输入手机号码" class="form-control isPhone" type="text" maxlength="11">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">生日：</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
		                        <input name="birthday" class="form-control" placeholder="yyyy-MM-dd" type="text">
		                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
		                    </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-xs-2 control-label">备注：</label>
                        <div class="col-xs-10">
                            <textarea name="remark" maxlength="500" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <h4 class="form-header h4">商品数据</h4>
            <div class="row">
                <div class="col-sm-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus"> 增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus"> 删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
					    <table id="bootstrap-table"></table>
					</div>
                </div>
            </div>
        </form>
    </div>
      
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
	<script th:inline="javascript">
	    $(function() {
	    	// 初始化数据, 可以由后台传过来
	    	var data = [
	    	{
	    		id: "100",
	    		name: "商品名称",
            	weight: "100",
            	price: "12.5",
            	date: "2021-02-01",
            	type: "1",
	    	},
	    	{
	    		id: "101",
	    		name: "商品名称2",
            	weight: "50",
            	price: "10.8",
            	date: "2021-02-01",
            	type: "0",
	    	}];
		    var options = {
		    	data: data,
                pagination: false,
		        showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
		        columns: [{
		            checkbox: true
		        },
		        {
		        	field: 'index',
		        	align: 'center',
		        	title: "序号",
                    formatter: function (value, row, index) {
                    	var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                    	var columnId = $.common.sprintf("<input type='hidden' name='goods[%s].id' value='%s'>", index, row.id);
                    	return columnIndex + $.table.serialNumber(index) + columnId;
                    }
                },
		        {
		            field: 'name',
		            align: 'center',
		            title: '商品名称',
		            formatter: function(value, row, index) {
		            	var html = $.common.sprintf("<input class='form-control goodsName' type='text' name='goods[%s].name' value='%s'>", index, value);
		        		return html;
                    }
		        },
		        {
		            field: 'weight',
		            align: 'center',
		            title: '商品重量',
		            formatter: function(value, row, index) {
		            	var html = $.common.sprintf("<input class='form-control goodsWeight' type='text' name='goods[%s].weight' value='%s'>", index, value);
		        		return html;
                    }
		        },
		        {
		            field: 'date',
		            align: 'center',
		            title: '商品日期',
		            formatter: function(value, row, index) {
		            	var html = $.common.sprintf("<input class='form-control' type='text' name='goods[%s].date' value='%s' placeholder='yyyy-MM-dd'>", index, value);
		        		return html;
                    }
		        },
		        {
		            field: 'price',
		            align: 'center',
		            title: '商品价格',
		            formatter: function(value, row, index) {
		            	var html = $.common.sprintf("<input class='form-control' type='text' name='goods[%s].price' value='%s'>", index, value);
		        		return html;
                    }
		        },
		        {
		            field: 'type',
		            align: 'center',
		            title: '商品种类',
		            formatter: function(value, row, index) {
		        		var data = [{ index: index, type: value }];
		                return $("#goodsTypeTpl").tmpl(data).html();
                    }
		        },
		        {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                    	var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                        return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\')"><i class="fa fa-remove"></i>删除</a>';
                    }
                }]
		    };
		    $.table.init(options);
		});
	    
        $.validator.addMethod("goodsName", function(value, element) {
            return !this.optional(element);
        }, "商品名称必填。");
        
        $.validator.addMethod("goodsWeight", function(value, element) {
            return !this.optional(element) && (value <= 100 && value >= 0);
        }, "商品重量长度区间0-100。");
        
        /* 主子表-提交 */
        function submitHandler(index, layero){
            if ($.validate.form()) {
                var data = $("#form-add").serializeArray();
                alert(JSON.stringify(data))
                $.operate.saveModal("/demo/operate/customer/add", data);
            }
        }
	    
        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
        
        function addRow() {
        	var count = $("#" + table.options.id).bootstrapTable('getData').length;
        	var row = {
                index: $.table.serialNumber(count),
            	name: "",
            	weight: "",
            	price: "",
            	date: "",
            	type: "",
            }
        	sub.addRow(row);
        }
        
        $("#bootstrap-table").on("post-body.bs.table", function (e, args) {
        	$("input[name$='date']").datetimepicker({
	            format: "yyyy-mm-dd",
	            minView: "month",
	            autoclose: true,
	            pickerPosition:'top-right'
	        });
    	});
    </script>
</body>
</html>

<!-- 商品类型 -->
<script id="goodsTypeTpl" type="text/x-jquery-tmpl">
<div>
<select class='form-control' name='goods[${index}].type'>
    <option value="">所有</option>
    <option value="0" {{if type==="0"}}selected{{/if}}>寒性</option>
    <option value="1" {{if type==="1"}}selected{{/if}}>热性</option>
</select>
</div>
</script>
