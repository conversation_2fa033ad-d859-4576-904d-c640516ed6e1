package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/oc/bankAccount")
public class BankAccountMgrController extends BaseController {

    private static final String TABLE = "eh_pms_bank_account";

    private static final String PREFIX = "oc/ccb/bankAccount";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }   

    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        mmap.put("bankAccount", Db.findFirst("select * from " + TABLE + " where id = ?", id).toMap());
        mmap.put("id", id);
        return PREFIX + "/edit";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from " + TABLE + " t where 1=1");
        sql.append(params.getString("bank_type"), "and t.bank_type = ?");
        sql.append(params.getString("cust_id"), "and t.cust_id = ?");
        sql.append(params.getString("user_id"), "and t.user_id = ?");
        sql.append(params.getString("acc_no"), "and t.acc_no = ?");
        sql.append(params.getString("status"), "and t.status = ?");
        sql.appendLike(params.getString("remark"), "and t.remark like ?");
        sql.append("order by t.create_time desc");

        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        Long id = params.getLong("id");
        if (id == null) {
            return AjaxResult.error("ID不能为空");
        }
        Record record = Db.findFirst("select * from " + TABLE + " where id = ?", id);
        return AjaxResult.success(null, record == null ? null : record.toMap());
    }

    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        JSONObject params = getParams();
        Record record = new Record();
        record.setColumns(params);
        record.set("pms_id",getSysUser().getPmsId());
        record.set("community_id",getSysUser().getCommunityId());
        record.set("create_time", new Date());
        record.set("update_time", new Date());
        record.set("create_by", getLoginName());
        Db.save(TABLE, "id", record);
        return AjaxResult.success();
    }

    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Long id = params.getLong("id");
        if (id == null) {
            return AjaxResult.error("ID不能为空");
        }
        Record record = new Record();
        record.setColumns(params);
        record.set("update_time", new Date());
        record.set("community_id",getSysUser().getCommunityId());
        record.set("update_by", getLoginName());
        Db.update(TABLE, "id", record);
        return AjaxResult.success();
    }   


    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return AjaxResult.error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById(TABLE, "id", id);
        }
        return AjaxResult.success();
    }

    /**
     * 查询所有有效银行账户（供ccbjob用）
     */
    @PostMapping("/activeList")
    @ResponseBody
    public AjaxResult activeList() {
        List<Record> list = Db.find("select * from " + TABLE + " where status = 0");
        return AjaxResult.success(list);
    }
} 