

/* 新闻列表页面样式 */
.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 24rpx;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.news-item {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.news-icon {
  width: 88rpx;
  height: 88rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44rpx;
  border-radius: 16rpx;
}

/* 三种不同的图标样式 */
.icon-gonggao {
  color: #07c160;
  background: rgba(7,193,96,0.1);
}

.icon-tongzhi {
  color: #ff9500;
  background: rgba(255,149,0,0.1);
}

.icon-xinwen {
  color: #576b95;
  background: rgba(87,107,149,0.1);
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* 防止文本溢出 */
}

.news-info {
  flex: 1;
}

.news-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.news-desc {
  font-size: 26rpx;
  color: #666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.news-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.news-time {
  font-size: 24rpx;
  color: #999;
}

.news-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.news-type {
  font-size: 24rpx;
  color: #07c160;
  background: rgba(7,193,96,0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.news-type.notice {
  color: #ff9500;
  background: rgba(255,149,0,0.1);
}

.icon-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 8rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
} 