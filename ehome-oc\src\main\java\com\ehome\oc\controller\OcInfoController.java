package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.entity.SysUser;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ShiroUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.jfinal.model.OcInfoModel;
import com.ehome.jfinal.model.PmsInfoModel;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小区信息管理 控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/info")
public class OcInfoController extends BaseController {

    private static final String PREFIX = "oc/info";

    /**
     * 跳转到小区管理页面
     */
    @RequestMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @RequestMapping("/pmsOc/{pmsId}")
    public String pmsOc(@PathVariable("pmsId") String pmsId, ModelMap mmap) {
        mmap.put("pmsId", pmsId);
        return "pms/info/pms-oc";
    }

    /**
     * 跳转到新增小区页面
     */
    @RequestMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    /**
     * 跳转到编辑小区页面
     *
     * @param ocId 小区ID
     * @param mmap 数据传输对象
     */
    @GetMapping("/edit/{ocId}")
    public String edit(@PathVariable("ocId") String ocId, ModelMap mmap) {
        OcInfoModel ocInfo = OcInfoModel.dao.findById(ocId);
        mmap.put("ocInfo", ocInfo.toMap());
        return PREFIX + "/edit";
    }

    @PostMapping("/pmsMap")
    @ResponseBody
    public AjaxResult pmsMap() {
        List<PmsInfoModel> list = PmsInfoModel.dao.findAll();
        Map<String,String> pmsMap = new HashMap<String,String>();
        list.forEach(pms -> {
            pmsMap.put(pms.getStr("pms_id"),pms.getStr("pms_name"));
        });
        return AjaxResult.success(pmsMap);
    }
    /**
     * 查询小区列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/pmsOcList")
    @ResponseBody
    public TableDataInfo pmsOcList() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 获取单条小区记录
     */
    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String ocId = params.getString("oc_id");
        if (StringUtils.isEmpty(ocId)) {
            return AjaxResult.error("小区ID不能为空");
        }
        OcInfoModel model = OcInfoModel.dao.findById(ocId);
        return AjaxResult.success(null, model.toMap());
    }

    /**
     * 新增小区信息
     */
    @PostMapping("/addData")
    @ResponseBody
    @Log(title = "新增小区信息", businessType = BusinessType.INSERT)
    public AjaxResult addData() {
        JSONObject params = getParams();
        OcInfoModel model = new OcInfoModel();
        model.setColumns(params);
        model.set("oc_id", Seq.getId());
        setCreateAndUpdateInfo(model);
        model.save();
        return AjaxResult.success();
    }

    /**
     * 修改小区信息
     */
    @Log(title = "更改小区信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        OcInfoModel model = new OcInfoModel();
        model.setColumns(params);
        setUpdateInfo(model);
        return toAjax(model.update());
    }

    /**
     * 删除小区信息
     *
     * @param ids 小区ID,多个以逗号分隔
     */
    @Log(title = "删除小区", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            OcInfoModel.dao.deleteById(id);
        }
        return success();
    }

    /**
     * 校验小区名称是否唯一
     */
    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String ocName = params.getString("oc_name");
        if (StringUtils.isEmpty(ocName)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_community where 1=1");
        sql.append(ocName, "and oc_name = ?");
        OcInfoModel model = OcInfoModel.dao.findFirst(sql.getSQL(), sql.getParams());
        return model == null;
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_community t1");
        String pmsId = params.getString("pmsId");
        sql.append(" where 1=1");
        if (StringUtils.isNotEmpty(pmsId)) {
            sql.append(pmsId, "and t1.pms_id = ?");
        }
        // 小区名称 - 模糊查询
        sql.appendLike(params.getString("oc_name"), "and t1.oc_name like ?");
        // 小区代码 - 精确匹配
        sql.append(params.getString("oc_code"), "and t1.oc_code = ?");
        // 所属社区 - 精确匹配
        sql.append(params.getString("community_name"), "and t1.community_name = ?");
        // 小区状态 - 精确匹配
        sql.append(params.getString("oc_state"), "and t1.oc_state = ?");
        // 业主数量 - 精确匹配
        sql.append(params.getString("owner_count"), "t1.and owner_count = ?");
        // 楼宇栋数 - 精确匹配
        sql.append(params.getString("building_num"), "and t1.building_num = ?");

        // 创建时间范围查询
        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and t1.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and t1.create_time <= ?");

        // 默认按创建时间倒序
        sql.append("order by t1.create_time desc");
        return sql;
    }

    /**
     * 设置创建和更新信息
     */
    private void setCreateAndUpdateInfo(OcInfoModel model) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        model.set("create_time", now);
        model.set("update_time", now);
        model.set("create_by", loginName);
        model.set("update_by", loginName);
    }

    /**
     * 设置更新信息
     */
    private void setUpdateInfo(OcInfoModel model) {
        model.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        model.set("update_by", getSysUser().getLoginName());
    }

    /**
     * 切换当前登录用户的小区
     */
    @GetMapping("/community/switch")
    @ResponseBody
    public AjaxResult switchCommunity(@RequestParam("id") String communityId) {
        try {
            // 获取当前登录用户
            SysUser user = getSysUser();
            // 设置新的小区ID
            user.setCommunityId(communityId);
            ShiroUtils.setSysUser(user);
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("切换小区失败：" + e.getMessage());
        }
    }
}
