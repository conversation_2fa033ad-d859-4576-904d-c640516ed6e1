// 通用反馈管理器
import { FEEDBACK_TYPES, REPAIR_OPTIONS, COMPLAINT_OPTIONS } from '../constants/index.js'

class FeedbackManager {
  constructor(type) {
    this.type = type // 'bx' 或 'complaint'
    this.apiPrefix = `/api/wx/${type}`
    this.typeOptions = type === FEEDBACK_TYPES.REPAIR ? REPAIR_OPTIONS : COMPLAINT_OPTIONS
  }

  // 获取选项列表
  getTypeOptions() {
    return this.typeOptions
  }

  // 验证表单数据
  validateForm(formData) {
    const { type, content, address } = formData
    
    if (!type) {
      return { valid: false, message: '请选择类型' }
    }
    
    if (!content || content.trim().length < 5) {
      return { valid: false, message: '请输入至少5个字符的反馈内容' }
    }
    
    if (!address) {
      return { valid: false, message: '请输入详细地址' }
    }
    
    return { valid: true }
  }

  // 提交反馈
  async submitFeedback(formData) {
    try {
      // 假设外部已做校验，这里不再抛异常
      const app = getApp()
      const result = await app.request({
        url: `${this.apiPrefix}/addData`,
        method: 'POST',
        data: {
          ...formData,
          media_urls: JSON.stringify(formData.mediaUrls || [])
        }
      })
      if (result.code === 0) {
        return { success: true, data: result.data }
      } else {
        throw new Error(result.msg || '提交失败')
      }
    } catch (error) {
      console.error(`[FeedbackManager] ${this.type} 提交失败:`, error)
      return { success: false, error: error.message }
    }
  }

  // 获取历史记录
  async getHistory() {
    try {
      const app = getApp()
      const result = await app.request({
        url: `${this.apiPrefix}/history`,
        method: 'POST'
      })

      if (result.code === 0) {
        // 处理历史记录数据
        const list = (result.data || []).map(item => ({
          ...item,
          mediaUrls: item.media_urls ? JSON.parse(item.media_urls) : []
        }))
        return { success: true, data: list }
      } else {
        throw new Error(result.msg || '获取历史记录失败')
      }
    } catch (error) {
      console.error(`[FeedbackManager] ${this.type} 获取历史失败:`, error)
      return { success: false, error: error.message, data: [] }
    }
  }

  // 上传媒体文件
  async uploadMedia(filePath) {
    try {
      return new Promise((resolve, reject) => {
        wx.uploadFile({
          url: `${this.apiPrefix}/upload`,
          filePath: filePath,
          name: 'file',
          success: (uploadRes) => {
            try {
              const data = JSON.parse(uploadRes.data)
              if (data.code === 200) {
                resolve(data.data)
              } else {
                reject(new Error(data.msg || '上传失败'))
              }
            } catch (parseError) {
              reject(new Error('上传响应解析失败'))
            }
          },
          fail: (error) => {
            reject(new Error(error.errMsg || '上传失败'))
          }
        })
      })
    } catch (error) {
      console.error(`[FeedbackManager] ${this.type} 上传失败:`, error)
      throw error
    }
  }

  // 预填充用户信息
  getPrefilledForm() {
    try {
      const ownerInfo = wx.getStorageSync('ownerInfo') || {}
      return {
        type: '',
        content: '',
        mediaUrls: [],
        address: ownerInfo.houseStr || '',
        name: ownerInfo.ownerName || '',
        phone: ownerInfo.mobile || ''
      }
    } catch (error) {
      console.error(`[FeedbackManager] 获取预填信息失败:`, error)
      return {
        type: '',
        content: '',
        mediaUrls: [],
        address: '',
        name: '',
        phone: ''
      }
    }
  }
}

// 工厂函数
export function createFeedbackManager(type) {
  return new FeedbackManager(type)
}

export default FeedbackManager 