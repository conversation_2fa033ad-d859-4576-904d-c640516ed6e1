// index.js
import { getStateManager } from '../../utils/stateManager.js'
import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { formatTime, filterHtmlTags } from '../../utils/common.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

// 登录校验的上次时间戳（毫秒）
let lastLoginCheckTime = 0
const LOGIN_CHECK_INTERVAL = 3 * 60 * 1000 // 3分钟，单位毫秒

// 数据缓存 - 添加用户标识
let weatherCache = new Map()
let newsCache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

Page({
  data: {
    isLogin: false,
    isAuth: false,
    userInfo: null,
    ownerInfo: null,
    phoneNumber: null,
    hasBindPhone: false,
    isHouseAuth: false,
    houseInfo: null,
    communityInfo: {
      communityName: '智慧小区',
      communityBanner: '/static/images/banner.png'
    },
    weather: {
      temp: '--',
      text: '获取中'
    },
    newsList: []
  },

  async onLoad() {
    await this.initializePage()
  },

  async onShow() {
    // 设置tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 0 })
    }

    // 刷新页面状态
    this.refreshPageState()

    // 检查用户是否变化，如果变化则清除缓存
    this.checkUserChange()

    // 登录校验保护：3分钟内只校验一次
    const now = Date.now()
    if (now - lastLoginCheckTime > LOGIN_CHECK_INTERVAL) {
      const loginSuccess = await this.checkAndUpdateLoginStatus()
      if (loginSuccess) {
        await this.refreshPageData()
      }
      lastLoginCheckTime = now
    } else {
      // 3分钟内已校验过，直接刷新页面数据
      await this.refreshPageData()
    }
  },

  // 初始化页面
  async initializePage() {
    try {
      loadingManager.show('加载中...')
      
      const loginSuccess = await this.checkAndUpdateLoginStatus()
      if (loginSuccess) {
        await Promise.all([
          this.getWeatherInfo(),
          this.getNewsList()
        ])
      }
    } catch (error) {
      handleError(error, '页面初始化')
    } finally {
      loadingManager.hide()
    }
  },

  // 获取用户缓存key
  getUserCacheKey() {
    const state = stateManager.getState()
    return state.userInfo?.userId || 'anonymous'
  },

  // 检查用户变化并清除缓存
  checkUserChange() {
    const currentUserKey = this.getUserCacheKey()
    if (this.lastUserKey && this.lastUserKey !== currentUserKey) {
      // 用户变化，清除所有缓存
      weatherCache.clear()
      newsCache.clear()
    }
    this.lastUserKey = currentUserKey
  },

  // 刷新页面数据
  async refreshPageData() {
    const state = stateManager.getState()
    this.setData({
      isLogin: state.isLogin,
      isAuth: state.isHouseAuth,
      userInfo: state.userInfo || null,
      ownerInfo: state.ownerInfo || null,
      houseInfo: state.houseInfo || null,
      communityInfo: state.communityInfo || this.data.communityInfo
    })
    wx.setNavigationBarTitle({
      title: (state.communityInfo && state.communityInfo.communityName) || this.data.communityInfo.communityName || '智慧小区'
    })
  },

  // 检查并更新登录状态
  async checkAndUpdateLoginStatus() {
    try {
      const token = wx.getStorageSync('token')
      const userInfo = wx.getStorageSync('wxUserInfo')

      if (!token || !userInfo?.nickName) {
        return this.checkLoginStatus()
      }

      // 更新状态管理器
      stateManager.setState({
        isLogin: true,
        userInfo,
        hasBindPhone: !!userInfo.phoneNumber
      })

      // 检查认证状态
      const authResult = await this.checkAuthenticationStatus()
      return authResult

    } catch (error) {
      handleError(error, '登录状态检查')
      return this.checkLoginStatus()
    }
  },

  // 检查认证状态
  async checkAuthenticationStatus() {
    try {
      const state = stateManager.getState()
      const res = await app.request({
        url: '/api/wx/data/status',
        method: 'POST',
        data: {
          ownerInfo: state.ownerInfo,
          userInfo: state.userInfo,
          tokenUser: state.tokenUser
        }
      })

      if (res.code === 500) {
        this.redirectToLogin()
        return false
      }

      if (res.code === 0) {
        // 更新状态管理器
        stateManager.setState({
          isHouseAuth: res.data.isHouseAuth,
          houseInfo: res.data.houseInfo,
          ownerInfo: res.data.ownerInfo,
          communityInfo: res.data.communityInfo,
          tokenUser: res.data.tokenUser
        })

        // 如果有新token，更新存储
        if (res.token) {
          wx.setStorageSync('token', res.token)
        }

        await this.refreshPageData()
        return true
      }

      return false
    } catch (error) {
      handleError(error, '认证状态检查')
      return false
    }
  },

  // 获取天气信息（带缓存）
  async getWeatherInfo() {
    try {
      // 检查缓存
      const now = Date.now()
      const userKey = this.getUserCacheKey()
      const cachedWeather = weatherCache.get(userKey)

      if (cachedWeather && (now - cachedWeather.timestamp < CACHE_DURATION)) {
        this.setData({ weather: cachedWeather.data })
        return
      }

      const res = await app.request({
        url: '/api/wx/data/weather/current'
      })

      if (res.code === 0) {
        // 更新缓存
        weatherCache.set(userKey, {
          data: res.data,
          timestamp: now
        })
        this.setData({ weather: res.data })
      }
    } catch (error) {
      console.warn('[Weather] 获取天气信息失败:', error)
      // 天气信息不是关键功能，失败不显示错误提示
    }
  },

  // 获取新闻列表（带缓存）
  async getNewsList() {
    try {
      // 检查缓存
      const now = Date.now()
      const userKey = this.getUserCacheKey()
      const cachedNews = newsCache.get(userKey)

      if (cachedNews && (now - cachedNews.timestamp < CACHE_DURATION)) {
        this.setData({ newsList: cachedNews.data })
        return
      }

      const res = await app.request({
        url: '/api/wx/data/notices'
      })

      if (res.code === 0) {
        const list = (res.data.list || [])
          .slice(0, 3)
          .map(item => ({
            ...item,
            time: formatTime(item.createTime, 'YYYY-MM-DD'),
            title: filterHtmlTags(item.title)
          }))

        // 更新缓存
        newsCache.set(userKey, {
          data: list,
          timestamp: now
        })
        this.setData({ newsList: list })
      }
    } catch (error) {
      console.warn('[News] 获取新闻列表失败:', error)
      // 新闻列表不是关键功能，失败不显示错误提示
    }
  },



  // 导航方法
  goToNoticeList() {
    if (!this.checkLoginStatus()) return

    wx.navigateTo({
      url: '/pages/notice/list'
    })
  },

  goToNewsDetail(e) {
    if (!this.checkLoginStatus()) return

    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/notice/detail?id=${id}`
    })
  },

  goToRepair() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({ 
      url: '/pages/bx/bx' 
    })
  },

  goToComplaint() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({ 
      url: '/pages/complaint/complaint' 
    })
  },

  goToServicePhone() {
    const phone = this.data.communityInfo?.servicePhone
    if (phone) {
      wx.makePhoneCall({ phoneNumber: phone })
    } else {
      wx.showToast({
        title: '暂无服务电话',
        icon: 'none'
      })
    }
  },

  // 其他功能（计划开发中）
  goToPayment() {
    this.showComingSoon('物业缴费')
  },

  goToVisitor() {
    this.showComingSoon('邀请住户')
  },

  goToOcInfo() {
    wx.navigateTo({ url: '/pages/ocinfo/ocinfo' })
  },

  // 显示即将推出提示
  showComingSoon(feature) {
    wx.showToast({
      title: `${feature}功能即将推出`,
      icon: 'none'
    })
  }

})

