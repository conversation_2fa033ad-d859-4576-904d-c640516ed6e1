######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml
### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml
!/logs/
!/logs/
# SpecStory explanation file
.specstory/.what-is-this.md
!/.specstory/
/.specstory/
# SpecStory project identity file
.specstory/.project.json
# SpecStory derived-cursor-rules.mdc backup files
.specstory/ai_rules_backups/*
# SpecStory explanation file
.specstory/.what-is-this.md
