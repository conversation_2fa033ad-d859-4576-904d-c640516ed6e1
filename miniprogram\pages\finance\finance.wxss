.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 导航栏样式 */
.nav-bar {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  height: 88rpx;
  background: #fff;
}

.nav-back {
  position: absolute;
  left: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

.nav-actions {
  position: absolute;
  right: 30rpx;
  display: flex;
  align-items: center;
}

.action-btn {
  margin-left: 20rpx;
  font-size: 32rpx;
}

.circle-btn {
  font-size: 40rpx;
}

/* 余额信息卡片样式 */
.balance-card {
  margin: 20rpx 10rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.balance-info {
  display: flex;
  flex-direction: column;
}

.balance-amount {
  font-size: 70rpx;
  font-weight: bold;
  color: #ff4d5e;
  line-height: 1.2;
}

.balance-label {
  font-size: 28rpx;
  color: #888;
  margin-top: 10rpx;
}

.balance-metadata {
  text-align: right;
}

.metadata-item {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

/* 年度统计样式 */
.year-stat {
  margin-bottom: 24rpx;
  background: #fff;
  margin: 10rpx 10rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.03);
}

.year-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx 30rpx;
  margin-bottom: 0rpx;
}

.year-title-row {
  display: flex;
  align-items: center;
}

.year-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
  letter-spacing:2rpx;
}

.year-desc {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

.year-arrow, .toggle-icon, .detail-arrow {
  font-family: "iconfont" !important;
  font-size: 36rpx;
  color: #bbb;
  margin-left: 16rpx;
  transition: transform 0.2s;
  display: inline-flex;
  align-items: center;
}

.year-arrow.expanded {
  transform: rotate(180deg);
}

.year-toggle {
  font-size: 32rpx;
  color: #999;
}

.year-summary {
  display: flex;
  padding: 20rpx;
}

.summary-item {
  flex: 1;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10rpx;
}

.income-block {
  background: #ff6b6b;
}

.expense-block {
  background: #00c9a7;
}

.summary-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0rpx;
}

.summary-value {
  font-size: 38rpx;
  font-weight: bold;
  color: #fff;
}

/* 财务表格样式 */
.finance-table {
  margin: 20rpx 0 0;
}

.table-header {
  display: flex;
  background: #f7f7f7;
  padding: 24rpx 30rpx;
  font-size: 24rpx;
  color: #666;
  border-bottom: 1rpx solid #eee;
}

.header-cell {
  flex: 1;
  text-align: center;
}

.month-cell {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

/* 月份行样式 */
.month-row {
  display: flex;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  position: relative;
  align-items: center;
}

.amount-cell {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}

.toggle-icon {
  position: absolute;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
  font-size: 24rpx;
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.toggle-icon.expanded {
  transform: rotate(0deg);
}

/* 交易明细列表样式 */
.transaction-list {
  background: #f9f9f9;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.transaction-list.expanded {
  max-height: 4000rpx;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background: #fff;
  margin: 2rpx 0;
  position: relative;
}

.transaction-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  margin-right: 20rpx;
  color: #fff;
}

.income-icon {
  background-color: #4e9dff;
}

.expense-icon {
  background-color: #cccccc;
}

.transaction-info {
  flex: 1;
  overflow: hidden;
}

.transaction-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.transaction-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.transaction-time {
  font-size: 24rpx;
  color: #999;
}

.transaction-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 40rpx;
}

.income {
  color: #4e9dff;
}

.expense {
  color: #333;
}

.detail-arrow {
  position: absolute;
  right: 30rpx;
  color: #ccc;
  font-size: 24rpx;
}

/* 保留原有的空数据提示样式 */
.empty-tip {
  text-align: center;
  color: #b0b0b0;
  padding: 60rpx 0 40rpx 0;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-img {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 16rpx;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.page-title-area {
  margin-top: 16rpx;
  margin-bottom: 12rpx;
  text-align: center;
}

.page-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1767d2;
  letter-spacing: 2rpx;
}

.page-title-underline {
  width: 120rpx;
  height: 8rpx;
  background: linear-gradient(90deg, #1767d2 0%, #4fa3f7 100%);
  border-radius: 4rpx;
  margin: 12rpx auto 0 auto;
}

/* 加载状态样式 */
.loading-tip {
  text-align: center;
  color: #666;
  padding: 40rpx 0;
  font-size: 28rpx;
}

/* 月度汇总样式 */
.month-summary {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: #f0f8ff;
  border-bottom: 1rpx solid #e8f4fd;
  font-size: 26rpx;
}

.month-income {
  color: #4e9dff;
  font-weight: bold;
}

.month-expense {
  color: #666;
  font-weight: bold;
} 