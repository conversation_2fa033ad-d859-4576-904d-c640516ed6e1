package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/oc/complain")
public class ComplainDataController extends BaseController {
    private static final String PREFIX = "oc/complaint";

    @RequestMapping("/compl")
    public String complaint() {
        return PREFIX+"/compl-list";
    }

    @RequestMapping("/bx")
    public String bx() {
        return PREFIX+"/bx-list";
    }

    @ResponseBody
    @PostMapping("/complaintData")
    public TableDataInfo complaintData(){
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_complaint t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("status"),"and t1.status = ?");
        sql.append("order by t1.create_time desc");
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @ResponseBody
    @PostMapping("/updateComplaintStatus")
    public AjaxResult updateComplaintStatus() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        String status = params.getString("status");
        Db.update("update eh_wx_complaint set status = ? where id = ?",status, id);
        return success();
    }

    @ResponseBody
    @PostMapping("/bxData")
    public TableDataInfo bxData(){
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_bx t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("status"),"and t1.status = ?");
        sql.append("order by t1.create_time desc");
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @ResponseBody
    @PostMapping("/updateBxStatus")
    public AjaxResult updateBxStatus() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        String status = params.getString("status");
        String replyContent = params.getString("reply_content");
        if ("2".equals(status)) {
            Db.update("update eh_wx_bx set status = ?, reply_content = ? where id = ?", status, replyContent, id);
        } else {
            Db.update("update eh_wx_bx set status = ? where id = ?", status, id);
        }
        return success();
    }

    @ResponseBody
    @PostMapping("/complaintDetail")
    public AjaxResult complaintDetail() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        Record record = Db.findFirst("select * from eh_wx_complaint where id = ?", id);
        if (record == null) {
            return error("未找到投诉记录");
        }
        return success(record.toMap());
    }

    @ResponseBody
    @PostMapping("/bxDetail")
    public AjaxResult bxDetail() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        Record record = Db.findFirst("select * from eh_wx_bx where id = ?", id);
        if (record == null) {
            return error("未找到报修记录");
        }
        return success(record.toMap());
    }

    @ResponseBody
    @PostMapping("/batchUpdateComplaintStatus")
    public AjaxResult batchUpdateComplaintStatus() {
        JSONObject params = getParams();
        String ids = params.getString("ids"); // 逗号分隔
        String status = params.getString("status");
        if (StringUtils.isEmpty(ids) || StringUtils.isEmpty(status)) {
            return error("参数错误");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.update("update eh_wx_complaint set status = ? where id = ?", status, id);
        }
        return success();
    }

    @ResponseBody
    @PostMapping("/batchUpdateBxStatus")
    public AjaxResult batchUpdateBxStatus() {
        JSONObject params = getParams();
        String ids = params.getString("ids"); // 逗号分隔
        String status = params.getString("status");
        String replyContent = params.getString("reply_content");
        if (StringUtils.isEmpty(ids) || StringUtils.isEmpty(status)) {
            return error("参数错误");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            if ("2".equals(status)) {
                Db.update("update eh_wx_bx set status = ?, reply_content = ? where id = ?", status, replyContent, id);
            } else {
                Db.update("update eh_wx_bx set status = ? where id = ?", status, id);
            }
        }
        return success();
    }

}
