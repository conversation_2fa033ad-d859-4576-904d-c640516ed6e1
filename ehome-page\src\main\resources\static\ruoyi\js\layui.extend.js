/**
 * 数组去重
 */
function arrayUnique(array) {
    array.sort();//排序
    var n = [array[0]];
    for (var i = 1; i < array.length; i++) {
        if (array[i] !== n[n.length - 1]) {
            n.push(array[i]);
        }
    }
    return n;
}
/**
 * 获取 页面上的 元素兼容  兼容所有方式 id,nama,classname,jquery对象,选择器
 * @param element 所需获取的元素的选择器
 * @param parentId 指定获取的element 元素，是在某个根对象下面
 * return js 对象 需 $($g("xx"))转jquery对象
 */
var $g =  function(element, parentId) {
    parentId = parentId || 'html';
    //获取父级元素
    var gg  = parentId == 'html'?  $(document) : getElm(parentId,$(document));
    if(!gg) {alert("找不到" + parentId + "的对象!"); return null};
    return getElm(element,gg);
    function getElm(element,parentElm){
        var pElm = $(parentElm);
        if (typeof element == 'string'){
            var s=null;
            if(element.charAt(0)=='#'||element.charAt(0)=='.'){
                s= pElm.find(element);
                return s[0];
            }else{
                s=pElm.find('#'+element);
                if(s[0]){
                    return s[0];
                }else{
                    var ss=pElm.find("[name='"+element+"']");
                    if(ss==null||ss.length==0){
                        s= pElm.find(element);
                        return s[0];
                    }else{
                        if(ss.length==1){
                            return ss[0];
                        }else if(ss.length>1){ //多个name属性相同的值
                            s=new Array();
                            for(var i=0;i<ss.length;i++){
                                s[i]=ss[i];
                            }
                            return s;
                        }
                    }
                }
            }
        }else{
            return pElm.find($(element));
        }
    }
};

/**
 * 获取URL参数
 * @param name 参数名称
 * @returns
 */
function getUrlParam(name){
    var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);//search,查询？后面的参数，并匹配正则
    if(r!=null)return  unescape(r[2]); return '';
}
function $setVal(element,value,parentId){
    $setVal(element,value,{},parentId);
}

/**
 * 设置 某个页面元素的 属性值,  支持普通输入域, 单选，多选 框等
 * @param element 为 表单域 的id 或者是 name 属性
 * @param value  可以为 字符串 或 字符串数组
 */
function $setVal(element,value,json,parentId){
    if(typeof element == 'string'){
        var s= $g(element,parentId);
    }else{
        s=element;
    }
    if($(s).data("value"))value=$(s).data("value");
    if(s)
        if(s.length&&!s.tagName){ //如果是数组
            // 判断是否 单选, 多选
            var tagName = s[0].tagName.toLowerCase();
            if("input"==tagName){
                var tagNametype = s[0].type.toLowerCase();
                if(tagNametype=="checkbox"||tagNametype=="radio"){
                    //判断 value 是否数组, 如果是，则逐一比较填写
                    if(isArray(value)){
                        for(var i=0;i< s.length;i++){
                            for(var j=0;j< value.length;j++)
                                if(s[i].value==value[j])
                                    s[i].checked=true;
                        }
                    }else{
                        for(var i=0;i< s.length;i++){
                            if(s[i].value==value)
                                s[i].checked=true;
                        }
                    }
                }else{ //普通form 表单对象
                    for(var i=0;i< s.length;i++)
                        s[i].value=value;
                }
            }else{
                //Form.Element.setValue(s,value);
                for(var i=0;i< s.length;i++)
                    s[i].value=value;
            }
        }else{ // 单个对象
            var tagName = s.tagName.toLowerCase();
            if("input"==tagName){
                var tagNametype = s.type.toLowerCase();
                if(tagNametype=="checkbox"||tagNametype=="radio"){
                    if(s.value==value)
                        s.checked=true;
                }else
                    s.value=formatVal(s,value,json);
            }else if("select"==tagName){ //暂不考虑多选下拉框
                s.value=value;
            }else{
                s.innerHTML=formatVal(s,value,json);
            }
        }
    function formatVal(el,value,json) {
        var fn = $(el).data('fn');
        var match = $(el).data('match');
        var v = value;
        if(fn&&$.isFunction(window[fn])){
            var data = $.extend({},$(el).data(),json);
            v =  excuteReturnMethod(fn,[value,data,$(el)]);
        }else if(match){
            var json = eval('(' + match + ')');
            var val = json[v];
            if(val){
                return val;
            }else{
                return json['default']||'';
            }
        }
        return v;
    }
}

/**
 * 把字符串转为可执行的方法
 * @param functionName
 * @param args
 */
function excuteReturnMethod(fnName,args){
    try{
        var fn = eval(fnName);
        if(typeof(fn)=='function'){
            return fn.apply(this,args)
        }
    }catch (e) {
        console.error(e);
    }
    return '';
}

/**
 * 截取区分数字 字符 中文
 * 一个中文长度为2，其他则为1；
 * 例：text="我和你223我和他",displayLength=7，返回"我和你2..."
 */

function  cutText(text,displayLength,unit) {
    var displayLength =displayLength==null?100:displayLength;
    if (!text) return "";
    text = text.replace(/<\/?[^>]*>/g,'');//返回前端内容默认移除html标记
    text = text.replace('&nbsp;','');//移除空格
    var result = "";
    var count = 0;
    for (var i = 0; i < displayLength; i++) {
        var _char = text.charAt(i);
        if (count >= displayLength)  break;
        if (/[^x00-xff]/.test(_char))  count++;  //双字节字符，//[u4e00-u9fa5]中文
        result += _char;
        count++;
    }
    if (result.length < text.length) {
        if(unit!=undefined){
            result += unit;
        }else{
            result += "...";
        }
    }
    return result;
}

//判断某个对象是否为数组,不能对使用$g() 之类的方法得到的dom对象使用
function isArray(o) {
    return Object.prototype.toString.call(o) === '[object Array]';
}

/**
 * 设置 json 对象到 表单元素中, 映射规则按照 json对象的属性名 追加前缀进行对应
 * @param json : json 对象
 * @param prefix : 映射到 表单域中的前缀字符串 ,可为空
 * @param splitfix:  如果json 中有 多选等字段, 该值自动转换成数组的 分隔符，默认为 , 号
 * @param parent:  指定元素以内的数据
 */
function fillRecord(json,prefix,splitfix,parent){
    if(isArray(json)){ //暂不支持数组类数据的回填
        alert(marsI18n("数据格式错误，不能回填"));
        return ;
    }
    prefix = prefix || "";
    splitfix =splitfix || ",";
    for(var name in json){
        var value=json[name]+"";
        //多选数组
        if(value.toString().indexOf(splitfix)>0) {
            value=value.toString().split(splitfix);
        }
        var elem=$g(prefix+name,parent);
        if(elem){
            $setVal(elem,value,json,parent);
        }
    }
    $(parent).editform();
}

$.fn.editform = function(){

};


$.fn.extend({
    //得到元素序列后的数据 支持任意元素
    serializeObject:function(){
        var o = {};
        var t = this;
        var isForm = t.isForm();
        var tempForm = null;
        var targetForm = t;
        if(!isForm){
            tempForm =  document.createElement('form');
            tempForm.id = 'form'+new Date().getTime()
            t.wrap(tempForm);
            targetForm = $("#"+tempForm.id)
        }

        var a = targetForm.serializeArray();
        $.each(a, function() {
            var _target = t.find('[name="'+this.name +'"]')
            var type = _target.attr('type');
            var multiple = _target.attr('multiple');
            if (o[this.name]) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                if(type=='checkbox'){
                    o[this.name] = [this.value]||[];
                }else if(multiple=='multiple'){
                    o[this.name] = [this.value]||[];
                }else{
                    o[this.name] = this.value || '';
                }
            }
        });
        if(!isForm){
            t.insertAfter(targetForm);
            targetForm.remove();
        }
        return o;
    },
    //判断是否是form表单
    isForm:function(){
        var formObj = ['form','input','textarea','select'];
        var o=$(this);
        if(o!=null&&o[0]!=null){
            var tagName = $(this)[0].tagName.toLowerCase();
            var idx  = $.inArray(tagName, formObj);
            return idx <0 ? false: true;
        }else{
            return false;
        }
    }
});

function transValueToText(element, val){
    return $($g(element)).transValueToText(val);
}
function getText(val,element){
    return $($g(element)).transValueToText(val);
}

var replace = function(text,replaceText,placeText){
    if(!text) return '';
    if(placeText==null || placeText==undefined) placeText = '';
    return replace(text,replaceText,placeText);
    function replace(text,replaceText,placeText){
        var newText = null;
        newText = text.replace(new RegExp(replaceText,'g'),placeText);
        return newText;
    };
}
var advReplace = function (text,start,length,placeText){
    if(text==null || text==undefined) return '';
    return replace(text,start,length,placeText);
    function replace(text,start,length,placeText){
        var newText = null;
        if(placeText==null || placeText==undefined) placeText = '*';
        text = text.toString();
        //截取处比文字长,直接返回
        if(Math.abs(start)>text.length) return text;
        //截取
        if(start>0){
            var textArr1 = text.substr(0,start);
            var end = start+length;
            //判断是起点+替换长度是否超过文字长度
            var longText = end>text.length?true:false;
            var textArr2 = longText?"":text.substr(end);
            var replaceArrLength = longText? text.length-start : length;
            return textArr1 + new Array(replaceArrLength+1).join(placeText) + textArr2
        }else{
            var end = text.substr(start);
            var startlen =  text.length + start - length;//
            var replacelen = startlen>0?length:length + startlen;
            var startIndex = startlen>0?startlen:0;
            var startText = text.substr(0,startIndex);
            return startText + new Array(replacelen+1).join(placeText) + end
        }
    };
}

$.fn.renderForm = function (option,fn){
    !!option || (option = {});
    var el = $(this);
    var dataObj = $.extend({},form.getJSONObject(el),option.data);
    $.ajax({
        url : option.url,type : 'post',cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8",
        data : dataObj,
        success : function(result) {
            var data = result.data;
           if(!$.isEmptyObject(data)){
                fillRecord(data,el.data("prefix"),el.data("splitfix"), el);
                fn&&fn(result);
            }
        },
        error : function(error) {
            console.error(error);
        }
    });
}

$.fn.renderSelect = function (option, fn) {
    !!option || (option = {});
    var el = $(this);
    var dataObj = $.extend({}, form.getJSONObject(el), option.data);
    var url = option.url;
    if (url) {
        // 如果 url 存在，直接使用 el 作为 select 元素
        var select = el;
        performAjaxRequest(select, url, dataObj, fn);
    } else {
        // 如果 url 不存在，使用 el.find("select[data-url]") 逻辑
        el.find("select[data-url]").each(function () {
            var select = $(this);
            var _url = select.data("url");
            var baseUrl = select.data("baseUrl");
            var postUrl = option.prefix + _url;
            if (baseUrl) {
                postUrl = ctx + baseUrl + _url;
            }
            performAjaxRequest(select, postUrl, dataObj, fn);
        });
    }
    function performAjaxRequest(select, url, dataObj, fn) {
        $.ajax({
            url: url,
            type: 'post',
            cache: false,
            dataType: 'json',
            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
            data: dataObj,
            success: function (result) {
                var data = result.data;
                if (!$.isEmptyObject(data)) {
                    var selectData = data[select.attr("name")] ? data[select.attr("name")] : data;
                    select.find("option[data-fetch]").remove();
                    var selectHtml = "";
                    for (var key in selectData) {
                        var selected = select.attr("value") == key ? "selected" : "";
                        selectHtml += "<option data-fetch value='" + key + "' " + selected + ">" + selectData[key] + "</option>";
                    }
                    // 将新的 options 追加到原有内容后
                    select.html(select.html() + selectHtml);
                }
                fn && fn(select);
            },
            error: function (error) {
                console.error(error);
            }
        });
    }
}



form = {
    getJSONObject:function(element){
        return $($g(element)).serializeObject();
    }
}

var layTable={};
var _table={};
var layTables={};

layTable.getColSum=function(fieldNames){
    if(fieldNames){
        var arrays=fieldNames.split(",");
        var sum=0;
        for ( var i = 0; i <arrays.length; i++){
            var fieldName=arrays[i];
            var fieldObj=$(".layui-table-main [data-field='"+fieldName+"']");
            $(fieldObj).each(function() {
                var t=$(this);
                var tdata=t.data("content");
                if(tdata||tdata==0){
                    var val=tdata+'';
                    if(val)sum = numAdd(sum,val);
                }else{
                    var val=t.find('div').text();
                    if(val)sum = numAdd(sum,val);
                }
            });
        }
        return sum;
    }
    return 0;
}
layTable.getlayTableTotalField=function(fieldName){
    var fillValue=$(".layui-table-total [data-field='"+fieldName+"']").find("div");
    return fillValue;
}
layTable.getlayTableFieldLength=function(fieldName){
    var length=$(".layui-table-main [data-field='"+fieldName+"']").length;
    return length;
}
layTable.addTitle=function(){
    $(".layui-table-header th").each(function(){
        var t=$(this);
        t.attr("title",t.text());
    });
}
layTable.colSum=function(fieldNames,str){
    var joinStr='';
    if (arguments.length>1) {
        joinStr = str?str:'';
    }
    if(fieldNames){
        var arrays=fieldNames.split(",");
        for ( var i = 0; i <arrays.length; i++){
            var fieldName=arrays[i];
            var sum=layTable.getColSum(fieldName);
            layTable.getlayTableTotalField(fieldName).text(sum+joinStr);
        }
    }
}
/***
 * 计算平均数 未转换
 * @param fieldName
 */
layTable.colAvg=function(fieldNames,str){
    var joinStr='';
    if (arguments.length>1) {
        joinStr=str?str:'';
    }
    if(fieldNames){
        var arrays=fieldNames.split(",");
        for ( var i = 0; i <arrays.length; i++){
            var fieldName=arrays[i];
            var sum=layTable.getColSum(fieldName);
            var length=layTable.getlayTableFieldLength(fieldName);
            var result=numDiv(sum,length);
            layTable.getlayTableTotalField(fieldName).text(result+joinStr);
        }
    }
}

function numAdd(num1, num2) {
    var baseNum, baseNum1, baseNum2;
    try {
        baseNum1 = num1.toString().split(".")[1].length;
    } catch (e) {
        baseNum1 = 0;
    }
    try {
        baseNum2 = num2.toString().split(".")[1].length;
    } catch (e) {
        baseNum2 = 0;
    }
    baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
    var precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;//精度
    return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(precision);;
};

//除法运算，避免数据相除小数点后产生多位数和计算精度损失。
function numDiv(num1, num2) {
    var baseNum1 = 0, baseNum2 = 0;
    var baseNum3, baseNum4;
    try {
        baseNum1 = num1.toString().split(".")[1].length;
    } catch (e) {
        baseNum1 = 0;
    }
    try {
        baseNum2 = num2.toString().split(".")[1].length;
    } catch (e) {
        baseNum2 = 0;
    }
    with (Math) {
        baseNum3 = Number(num1.toString().replace(".", ""));
        baseNum4 = Number(num2.toString().replace(".", ""));
        //return (baseNum3 / baseNum4) * pow(10, baseNum2 - baseNum1);
        return (baseNum3 / baseNum4).toFixed(2);
    }
};

layTable.totalRow = function(option){
    var othis=option['othis'];
    if(othis.totalRow){
        var cols = othis['cols'][0];
        for(var index in cols){
            var row  = cols[index];
            if(row['totalRow']){
                var fieldName  = row['field'];
                var totalRow  = row['totalRow'];
                var totalFormat  = row['totalFormat'];
                var sum=0;
                var fieldObj = othis['elem'].next().find(".layui-table-main [data-field='"+fieldName+"']");
                $(fieldObj).each(function() {
                    var t=$(this);
                    var tdata=t.data("content");
                    if((tdata||tdata==0)&&tdata!=''){
                        var val=tdata+'';
                        if(val)sum = numAdd(sum,val);
                    }else{
                        var val=t.find('div').text();
                        if(val)sum = numAdd(sum,val);
                    }
                });
                if(totalRow=='avg'){
                    var length = othis['elem'].next().find(".layui-table-main [data-field='"+fieldName+"']").length;
                    var result = numDiv(sum,length);
                    othis['elem'].next().find(".layui-table-total [data-field='"+fieldName+"']").find("div").text(formatVal(result,totalFormat));
                }else{
                    othis['elem'].next().find(".layui-table-total [data-field='"+fieldName+"']").find("div").text(formatVal(sum,totalFormat));
                }
            }
        }
    }
    function formatVal(val,format){
        if(format){
            return format.replace('this',val);
        }else{
            return val;
        }
    }
}


function getlayTable(el,option){
    !!option || (option = {});
    var elem=option.elem;
    var tempTable=null;
    if(elem){
        if($.isPlainObject(elem)){
            tempTable=elem;
        }else{
            tempTable=$($g(option.elem));
        }
    }else{
        if(option.id){
            tempTable=el.find('#'+option.id);
        }else{
            tempTable=el.find('table').first();
        }
    }
    var id=new Date().getTime();
    if(tempTable.attr("id")==''||tempTable.attr("id")==undefined){
        tempTable.attr("id",id);
    }
    id=tempTable.attr("id");
    if(tempTable.attr("lay-filter")==undefined||tempTable.attr("lay-filter")==''){
        tempTable.attr("lay-filter",id);
    }
    return tempTable;

}
function getLayUrl(option){
    var url=option.url;
    if(url){
        return url;
    }else{
        if(option.mars){
            return getCtxPath()+"/webcall?action="+option.mars
        }else{
            return '';
        }
    }
}

function getCtxPath(){
    var pathName = window.document.location.pathname;
    var contextPath=pathName.substring(0, pathName.substr(1).indexOf('/') + 1);
    return contextPath;
}


/***
 * 返回渲染成功后的字符串
 * @param templateId jsrender魔板Id
 * @param data 数据
 */
function renderTpl(templateId,data){
    var tmp=null;
    if(templateId){
        tmp = $.templates("#"+templateId);
    }else{
        return '';
    }
    if(data){
        return tmp.render(data);
    }else{
        return '';
    }
}
$.fn.getConfig = function(option){
    !!option || (option = {});
    var el=$(this);
    var tableObj=getlayTable(el,option);
    var tableId=option.id||tableObj.attr("id");
    if(layTables[tableId]==null){
        console.error("table "+tableId+" not init.");
        return {};
    }
    return layTables[tableId].config;
}
$.fn.queryData = function(option){
    !!option || (option = {});
    var el=$(this);
    var elData=el.data()||{'form':true};
    if(elData['form']==false){
        dataObj = $.extend({},{pageType:3,searchFlag:1},option.data,option.where);
    }else{
        dataObj = $.extend({},{pageType:3,searchFlag:1},el.serializeObject(),elData,option.data,option.where);
    }
    delete option['where'];
    var tableObj=getlayTable(el,option);
    var tableId=option.id||tableObj.attr("id");
    if(layTables[tableId]==null){
        console.error("table "+tableId+" not init.");
        return;
    }
    if(option['jumpOne']==false){

    }else{
        if(option.page==false){
            delete option['page'];
        }else{
            option['page']={curr:1};
        }
    }
    var dataStr=JSON.stringify(dataObj);
    if(typeof(filterXSS)!="undefined"){
        dataStr=filterXSS(dataStr);
    }
    layTables[tableId].reload($.extend({loading:true},{where:{data:dataStr}},option),false);
}

function layTableAllChoose(args){

}

function excuteFn(eventName,args){
    try{
        var fn = eval(eventName);
        if(typeof(fn)=='function'){
            fn.apply(this,args)
        }

    }catch (e) {
        console.error(e);
    }
}

$.fn.initTable = function (option){
    !!option || (option = {});
    var el=$(this);
    layui.use('table', function(){
        table = layui.table;
        var dataObj = {};
        var elData=el.data();
        if(elData==undefined){
            elData = {'form':true};
        }
        if(elData['form']==false){
            dataObj = $.extend({},{pageType:3},option.data,option.where);
        }else{
            dataObj = $.extend({},{pageType:3},el.serializeObject(),option.data,elData,option.where);
        }
        delete option['where'];
        var tableObj=getlayTable(el,option);
        id=tableObj.attr("id");
        //表格装载数据
        var tempDone=option.done;
        var then=option.then;
        if(option.done){
            delete option['done'];
        }
        var dataStr=JSON.stringify(dataObj);
        var tableOption=$.extend(
            {
                elem: tableObj
                ,id:id
                ,title:'列表数据'
                ,escape:true
                ,page:{theme: '#337ab7'}
                ,loading:true
                ,contentType:'application/x-www-form-urlencoded;charset=UTF-8'
                ,method:'post'
                ,where:{data:dataStr}
                ,parseData:function(res){
                    var state=1;
                    if(res.state!=undefined){
                        state=res.state;
                    }
                    return $.extend({},res,{state:state,totalRow:res.totalData,data:res.data,count:res.totalRow||res.count});
                }
                ,request:{pageName:'pageIndex',limitName:'pageSize'}
                ,response:{statusCode:1,statusName:'state'}
                ,error:function(e,content){
                    console.error(e,content);
                }
            }
            ,tableObj.data(),option
        );
        var options=$.extend(tableOption,{url:getLayUrl(tableOption)},{done:function(res,curr,count){

                //init begin
                var events=el.find("[lay-filter]");
                if(events.length>0){
                    layui.use('form', function(){
                        var form = layui.form;
                        events.each(function(){
                            var obj=$(this);
                            var tagName = obj[0].tagName.toLowerCase();
                            var tagNametype = "";
                            if(obj[0].type){
                                tagNametype=obj[0].type.toLowerCase()
                            }
                            var event=obj.attr("lay-filter");
                            var action='';
                            if("select"==tagName){
                                action="select("+event+")"
                            }else if(tagNametype=="checkbox"){
                                if(obj.attr("lay-skin")=='switch'){
                                    action="switch("+event+")"
                                }else{
                                    action="checkbox("+event+")"
                                }
                            }else if(tagNametype=="radio"){
                                action="radio("+event+")"
                            }else if(tagName=="button"){
                                action="submit("+event+")"
                            }else{
                                return true;
                            }
                            form.on(action, function(data){
                                if(event){
                                    excuteFn(event,[data]);
                                }
                            });
                        });
                    });
                }
                //头工具栏事件
                table.on('toolbar('+tableOption.id+')', function(obj){
                    var checkStatus = table.checkStatus(obj.config.id);
                    var data = checkStatus.data;
                    var event=obj.event;
                    if(event){
                        excuteFn(event,[data,obj]);
                    }
                });
                //监听行工具事件
                table.on('tool('+tableOption.id+')', function(obj){
                    var data = obj.data;
                    var event=obj.event;
                    if(event){
                        excuteFn(event,[data,obj]);
                    }
                });
                //监听行单击事件（单击事件/双击事件 row/rowDouble）
                var rowDoubleEvent=tableOption.rowDoubleEvent;
                var rowEvent=tableOption.rowEvent;
                if(rowDoubleEvent){
                    var action='rowDouble('+tableOption.id+')';
                    tableRowEvent(action,rowDoubleEvent);
                }
                if(rowEvent){
                    var action='row('+tableOption.id+')';
                    tableRowEvent(action,rowEvent);
                }
                function tableRowEvent(action,event){
                    table.on(action, function(obj){
                        var data = obj.data;
                        if(event){
                            excuteFn(event,[data,obj]);
                            obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
                        }
                    });
                }
                if(tableOption.edit){
                    table.on('edit('+tableOption.id+')', function(obj){
                        var event=tableOption.edit;
                        if(event){
                            excuteFn(event,[obj]);
                        }
                    });
                }
                if(!tableOption.autoSort){
                    table.on('sort('+tableOption.id+')', function(obj){
                        el.attr('data-sort-name',obj.field||'');
                        el.attr('data-sort-type',obj.type||'');
                        var _params  = {initSort: obj,id:tableOption.id,data:{sortName:obj.field,sortType:obj.type}};
                        var event = tableOption.sort;
                        if(event){
                            excuteFn(event,[_params]);
                        }else{
                            el.queryData(_params);
                        }
                    });
                }
                //init end
                res['othis'] = this;
                tableOption.result=res;
                layTable.totalRow(res);
                tempDone&&tempDone(res,curr,count);
                option.success&&option.success(res,curr,count);
                then&&then(this,res,curr,count);
                table.resize(tableOption.id);
            }
        });
        var tableIns=table.render(options);
        layTables[id]=tableIns;

        var laydates=el.find("[data-laydate]");
        if(laydates.length>0){
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydates.each(function(){
                    var obj=$(this);
                    var config=eval('(' + obj.data("laydate") + ')');
                    laydate.render($.extend({},{elem:this},config));
                });
            });
        }
    });

};

