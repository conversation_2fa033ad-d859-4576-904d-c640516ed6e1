/* pages/mine/index.wxss */

/* 用户头部 */
.user-header {
  position: relative;
  padding: 60rpx 30rpx;
  background: linear-gradient(135deg, #07c160, #0ab956);
  margin-bottom: 20rpx;
  border-radius: 0 0 4rpx 4rpx;
}

.user-card {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.4);
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  color: #fff;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  display: inline-block;
  backdrop-filter: blur(4px);
}

.status.auth {
  background: rgba(255, 255, 255, 0.3);
}

.login-btn {
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 48rpx;
  border-radius: 36rpx;
  backdrop-filter: blur(4px);
}

/* 分区标题 */
.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 30rpx;
  font-weight: 500;
}

/* 菜单列表 */
.menu-list {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #fafafa;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-item-left .iconfont {
  font-size: 40rpx;
  margin-right: 20rpx;
  color: #07c160;
  width: 48rpx;
  text-align: center;
}

.menu-item-left text {
  font-size: 28rpx;
  color: #333;
}

.menu-item-right {
  display: flex;
  align-items: center;
}

.menu-item-right .desc {
  font-size: 26rpx;
  color: #999;
  margin-right: 12rpx;
}

.menu-item-right .icon-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 联系客服按钮 */
.contact-btn {
  font-weight: 400;
  width: 100% !important;
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  border-radius: 0;
}

.contact-btn::after {
  display: none;
}

/* 退出登录按钮 */
.logout-btn {
  margin: 40rpx 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #fff;
  color: #ff4d4f;
  font-size: 28rpx;
  border-radius: 44rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.logout-btn:active {
  background: #fafafa;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
  margin-top: auto;
}

/* 功能图标 */
.icon-house:before { content: "\e61a"; }
.icon-profile:before { content: "\e6b7"; }
.icon-suggestion:before { content: "\e6b4"; }
.icon-about:before { content: "\e640"; }
.icon-service:before { content: "\e6c7"; }
.icon-arrow:before { content: "\e664"; } 