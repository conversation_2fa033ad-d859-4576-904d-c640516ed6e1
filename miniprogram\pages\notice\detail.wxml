<view class="container">
  <!-- 加载中 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="weui-loadmore">
      <view class="weui-loading"></view>
      <view class="weui-loadmore__tips">加载中...</view>
    </view>
  </view>

  <!-- 公告详情 -->
  <view class="notice-card" wx:elif="{{detail}}">
    <view class="notice-header">
      <text class="notice-title">{{detail.title}}</text>
      <text class="notice-date">发布时间：{{detail.date || detail.createTime}}</text>
    </view>
    
    <view class="notice-content">
      <rich-text nodes="{{detail.content}}"></rich-text>
    </view>

    <!-- 附件列表 -->
    <view class="attachments" wx:if="{{detail.attachments && detail.attachments.length > 0}}">
      <text class="attach-title">附件列表</text>
      <view class="attach-list">
        <view wx:for="{{detail.attachments}}" 
              wx:key="id" 
              class="attach-item"
              bindtap="viewAttachment"
              data-url="{{item.url}}"
              data-name="{{item.name}}">
          <text class="attach-icon">📎</text>
          <text class="attach-name">{{item.name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-image" src="/static/images/empty.png" mode="aspectFit" />
    <text class="empty-text">公告不存在或已删除</text>
  </view>
</view> 