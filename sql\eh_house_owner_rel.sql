-- 房屋业主关系表
CREATE TABLE `eh_house_owner_rel` (
  `rel_id` bigint(20) NOT NULL COMMENT '关系ID',
  `house_id` bigint(20) NOT NULL COMMENT '房屋ID',
  `owner_id` varchar(32) NOT NULL COMMENT '业主ID',
  `rel_type` tinyint(1) DEFAULT '1' COMMENT '关系类型（1业主 2家庭成员 3租户）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认（0否 1是）',
  `check_status` tinyint(1) DEFAULT '0' COMMENT '审核状态（0未审核 1已审核 2审核不通过）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
  `update_time` varchar(19) DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`rel_id`),
  KEY `idx_house_id` (`house_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋业主关系表';

-- 修改房屋信息表，移除业主相关字段
ALTER TABLE `eh_house_info` 
  DROP COLUMN `owner_name`,
  DROP COLUMN `owner_phone`,
  DROP COLUMN `id_card`,
  DROP COLUMN `owner_id`;

-- 修改业主表，移除房屋ID字段
ALTER TABLE `eh_owner` 
  DROP COLUMN `house_id`; 