Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#07c160",
    list: [{
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "icon-home"
    }, {
      pagePath: "/pages/finance/finance",
      text: "收支公示",
      icon: "icon-finance"
    }, {
      pagePath: "/pages/house/index",
      text: "我的房屋",
      icon: "icon-auth"
    }, {
      pagePath: "/pages/mine/index",
      text: "我的",
      icon: "icon-mine"
    }]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({
        url
      })
      this.setData({
        selected: data.index
      })
    }
  }
}) 